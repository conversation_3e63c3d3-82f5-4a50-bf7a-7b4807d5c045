@import '../../styles/variables.less';
@import '../../styles/mixins.less';
@import '../shared/wizard.less';

:host {
    // Content Container
    section {
        .panel {
            .animation(fade-in-up, 50ms);
            .transition(flex, box-shadow);

            &:nth-child(2) {
                animation-delay: 100ms;
            }

            &:nth-child(3) {
                animation-delay: 150ms;
            }

            &:nth-child(4) {
                animation-delay: 200ms;
            }

            &:nth-child(5) {
                animation-delay: 250ms;
            }

            &.settings {
                width: 0rem;
                animation: none;
            }

            &.options {
                flex: 0 0 5rem;
            }

            &.collapsed {
                flex: 0 0 5rem;
                width: 5rem;
                padding: 1.5rem 1rem;
                align-items: center;
                justify-content: flex-start;
                position: relative;
            }
        }
    }
}
