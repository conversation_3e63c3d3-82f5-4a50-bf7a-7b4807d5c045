<header>
    <div class="icon">
        <fa-icon icon="sliders"></fa-icon>
    </div>
    <h2>General Settings</h2>
</header>

<section>
    <div class="section">
        <div class="header">
            <fa-icon icon="calendar-lines-pen"></fa-icon>
            <h3>Event Log</h3>
        </div>
        <div class="body">
            <div class="form-group inline">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" name="purgeEventLogs" [(ngModel)]="purgeEventLogs">
                    <span class="form-checkbox-label">Purge event logs older than</span>
                </label>
                <input type="number" class="form-input always-enable-label" [(ngModel)]="purgeEventLogsDays" [disabled]="!purgeEventLogs">
                <label class="form-label">days</label>
            </div>

            <div class="options">
                <div class="form-group">
                    <label class="form-label">
                        <input type="checkbox"  class="form-checkbox" name="purgeTaskLogs" [(ngModel)]="purgeTaskLogs" [disabled]="!purgeEventLogs">
                        <span class="form-checkbox-label">Purge task logs as well</span>
                    </label>
                </div>
            </div>

            <div class="form-group inline">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" name="logWindowsEvent" [(ngModel)]="logWindowsEvent">
                    <span class="form-checkbox-label">Log Windows event when</span>
                </label>
                <div class="form-select-wrapper">
                    <select class="form-select always-enable-label" name="logWindowsEventCondition" [(ngModel)]="logWindowsEventCondition" [disabled]="!logWindowsEvent">
                        @for (option of logWindowsEventOptions; track option) {
                            <option [value]="option">{{ option }}</option>
                        }
                    </select>
                </div>
                <label class="form-label">days</label>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="header">
            <fa-icon icon="rotate"></fa-icon>
            <h3>Hyper Standby</h3>
        </div>
        <div class="body">
            <div class="form-group inline">
                <label for="" class="form-label">Run</label>
                <input type="number" class="form-input" [(ngModel)]="hyperStandbyTasks">
                <label for="" class="form-label">Hyper Standby Task(s) simultaneously</label>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="header">
            <fa-icon icon="copy"></fa-icon>
            <h3>Hyper Replication</h3>
        </div>
        <div class="body">
            <div class="form-group inline">
                <label class="form-label">Run</label>
                <input type="number" class="form-input" [(ngModel)]="hyperReplicationTasks">
                <label for="" class="form-label">Hyper Replication Task(s) simultaneously</label>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="header">
            <fa-icon icon="chart-simple"></fa-icon>
            <h3>Default performance</h3>
        </div>
        <div class="body">
            <div class="form-group inline">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" [(ngModel)]="limitCpuLoad">
                    <span class="form-checkbox-label">Limit maximum CPU load</span>
                </label>
                <input type="number" class="form-input always-enable-label" [(ngModel)]="maxCpuLoad" [disabled]="!limitCpuLoad">
                <label for="" class="form-label">%</label>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="header">
            <fa-icon icon="hard-drive"></fa-icon>
            <h3>Disk Scan</h3>
        </div>
        <div class="body">
            <div class="form-group inline">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" [(ngModel)]="limitDiskScan">
                    <span class="form-checkbox-label">Limit number of disks to scan to</span>
                </label>
                <input type="number" class="form-input always-enable-label" [(ngModel)]="maxDisksToScan" [disabled]="!limitDiskScan">
                <label for="" class="form-label">disks</label>
            </div>
        </div>
    </div>
</section>

<footer>
    <span class="notice">0 Configuration changes waiting to be applied</span>
    <div class="buttons">
        <button type="button" class="button secondary" (click)="onCancel()">Cancel</button>
        <button type="button" class="button primary" (click)="onApply()">Apply</button>
    </div>
</footer>
