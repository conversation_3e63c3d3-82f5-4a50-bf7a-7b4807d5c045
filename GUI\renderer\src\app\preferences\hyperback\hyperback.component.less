@import "../../../styles/variables.less";
@import "../../../styles/mixins.less";
@import "../shared/preferences.less";

:host {
    section {
        padding: 1rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: @border-radius-small;
        overflow-y: auto;

        /* Hide scrollbar for webkit browsers */
        &::-webkit-scrollbar {
            display: none;
        }

        .section {
            margin-bottom: 1rem;

            h3 {
                font-family: @font-family;
                font-weight: 700;
                font-size: 1rem;
                line-height: 1.25;
                color: @text-color;
                margin-bottom: 1rem;
            }

            .subsection {
                padding-left: 1rem;
                margin-bottom: 1rem;

                h4 {
                    font-family: @font-family;
                    font-weight: 700;
                    font-size: 0.875rem;
                    line-height: 1.4285714285714286;
                    color: @text-color;
                    margin-bottom: 1rem;
                }
            }

            .form-group {
                margin-bottom: .5rem;
            }
        }
    }
}
