import { Component, input, output, computed } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

export interface RecoveryType {
    id: string;
    title: string;
    description: string;
    icon: string;
}

@Component({
    selector: 'app-recovery-type',
    templateUrl: './recovery-type.component.html',
    styleUrl: './recovery-type.component.less',
    imports: [FontAwesomeModule],
})
export class RecoveryTypeComponent {
    // Inputs from parent component
    selectedRecoveryType = input<string | null>(null);
    expandedPanel = input<string | null>(null);

    // Outputs to parent component
    recoveryTypeSelected = output<string>();
    onPanelExpand = output<'type'>();

    // Recovery types configuration
    recoveryTypes: RecoveryType[] = [
        {
            id: 'disk-volume-local',
            title: 'Disk / Volume recovery on this computer',
            description: 'Use this to recover this computer into previously safe state',
            icon: 'hard-drive'
        },
        {
            id: 'disk-volume-remote',
            title: 'Disk / Volume recovery on another computer',
            description: 'Use this to recover another computer into previously safe state',
            icon: 'hard-drive'
        },
        {
            id: 'file-local',
            title: 'File recovery on this computer',
            description: 'Use this to retrieve individual files from backup image and recover to this computer',
            icon: 'file'
        },
        {
            id: 'file-remote',
            title: 'File recovery on another computer',
            description: 'Use this to retrieve individual files from backup image and recover to another computer',
            icon: 'file'
        },
        {
            id: 'hyper-recovery',
            title: 'HyperRecovery',
            description: 'Use this to recover to a Hypervisor VM on a HyperVisor',
            icon: 'server'
        },
        {
            id: 'hyper-recovery-live',
            title: 'HyperRecovery LIVE!',
            description: 'Use this to recover to a Hypervisor VM on a HyperVisor with zero downtime.',
            icon: 'server'
        }
    ];

    // Check if panel is expanded
    isPanelExpanded = computed(() => this.expandedPanel() === 'type');

    // Check if panel is collapsed
    isPanelCollapsed = computed(() => {
        const expanded = this.expandedPanel();
        return expanded !== null && expanded !== 'type';
    });

    // Handle recovery type selection
    selectRecoveryType(typeId: string): void {
        this.recoveryTypeSelected.emit(typeId);
    }

    // Handle panel expand
    expandPanel(): void {
        this.onPanelExpand.emit('type');
    }
}
