/**
 * Form Elements Styles
 * Common form element styles based on the design system
 * <AUTHOR> Assistant
 */

@import "variables.less";
@import "mixins.less";

// Base form styles
.form-group {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: .5rem;

    &.inline {
        flex-direction: row;
        align-items: center;
    }

    &:has(input:disabled:not(.always-enable-label)),
    &:has(select:disabled:not(.always-enable-label)),
    &:has(textarea:disabled:not(.always-enable-label)) {
        .form-label {
            color: @text-secondary;
        }
    }

    .form-label {
        display: inline-flex;
        align-items: center;
        gap: .5rem;
        font-family: @font-family;
        font-weight: 500;
        font-size: .875rem; // 14px
        line-height: 1.25rem;
        color: @text-color;
        white-space: nowrap;

        .transition(color);

        &.required::after {
            content: ' *';
            color: @primary-color;
        }
    }

    // Input fields
    .form-input,
    .form-textarea,
    .form-select {
        font-family: @font-family;
        font-size: .875rem; // 14px
        font-weight: 500;
        line-height: 1;
        color: @text-color;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid transparent;
        border-radius: .25rem; // 4px
        padding: 0.375rem 0.75rem; // 6px 12px
        height: 1.875rem; // 30px
        width: 100%;

        .transition(background-color, border-color, box-shadow, opacity);

        &::placeholder {
            color: @text-secondary;
        }

        &:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.25);
            border-color: @primary-color;
            box-shadow: 0 0 0 2px rgba(239, 78, 56, 0.2);
        }

        &:hover:not(:focus):not(:disabled) {
            background: rgba(255, 255, 255, 0.3);
        }

        &:disabled {
            color: @text-secondary;
            cursor: not-allowed;
            opacity: 0.7;
            background: rgba(255, 255, 255, 0.1);
        }

        &.error {
            border-color: @primary-color;
            background: rgba(239, 78, 56, 0.1);
        }

        &.success {
            border-color: @success-color;
            background: @success-bg;
        }

        &.small {
            padding: .25rem .5rem; // 4px 8px
            height: 1.5rem; // 24px
            font-size: .75rem; // 12px
        }

        &.large {
            padding: .5rem 1rem; // 8px 16px
            height: 2.25rem; // 36px
            font-size: 1rem; // 16px
        }
    }

    .form-textarea {
        min-height: 5.625rem; // 90px
        resize: vertical;
    }

    // Select dropdown
    .form-select-wrapper {
        position: relative;

        &::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 0.75rem; // 12px
            width: 0;
            height: 0;
            border-left: 0.25rem solid transparent; // 4px
            border-right: 0.25rem solid transparent; // 4px
            border-top: 0.3125rem solid @text-color; // 5px
            transform: translateY(-50%);
            pointer-events: none;

            .transition(border-top-color, opacity);
        }

        &:has(select:disabled):after {
            border-top-color: @text-secondary;
            opacity: 0.7;
        }

        .form-select {
            appearance: none;
            padding-right: 2rem;
        }
    }

    // Checkbox
    .form-checkbox {
        appearance: none;
        position: relative;
        width: 0.9375rem;
        height: 0.9375rem;
        border-radius: .1875rem; // 3px
        background: rgba(255, 255, 255, 0.2);

        .transition(background, box-shadow);

        &::after {
            content: '';
            position: absolute;
            top: .125rem; // 1px
            left: .3125rem; // 4px
            width: .25rem; // 4px
            height: .4375rem; // 7px
            border: .125rem solid @text-color;
            border-top: 0;
            border-left: 0;
            transform: rotate(45deg);
            opacity: 0;

            .transition(opacity);
        }

        &:focus {
            box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);
        }

        &:checked {
            background: rgba(16, 185, 129, 0.5);

            &::after {
                opacity: 1;
            }
        }

        &:disabled {
            cursor: not-allowed;
            background: rgba(255, 255, 255, 0.1);

            &:checked::after {
                opacity: .5;
            }

            + .form-checkbox-label {
                color: @text-secondary;
            }
        }

        &:hover:not(:disabled):not(:checked) {
            background: rgba(255, 255, 255, 0.3);
        }
    }

    // Radio button
    .form-radio {
        appearance: none;
        position: relative;
        width: 0.9375rem;
        height: 0.9375rem;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);

        .transition(background, box-shadow, border-color);

        &::after {
            content: '';
            position: absolute;
            top: .25rem;
            left: .25rem;
            width: .4375rem;
            height: .4375rem;
            background: @text-color;
            border-radius: 50%;
            opacity: 0;

            .transition(opacity);
        }

        &:focus {
            box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);
        }

        &:checked {
            background: rgba(16, 185, 129, 0.5);

            &::after {
                opacity: 1;
            }
        }

        &:disabled {
            cursor: not-allowed;
            background: rgba(255, 255, 255, 0.1);

            &:checked::after {
                opacity: .5;
            }

            + .form-radio-label {
                color: @text-secondary;
            }
        }

        &:hover:not(:disabled):not(:checked) {
            background: rgba(255, 255, 255, 0.3);
        }
    }

    // Range slider
    .form-range {
        appearance: none;
        width: 100%;
        height: 1.125rem; // 18px
        background: transparent;
        cursor: pointer;

        .transition(opacity);

        &::-webkit-slider-runnable-track{
            background-color: rgba(255, 255, 255, 0.2);
            height: .375rem; // 6px
            border-radius: .25rem; // 4px
            border: none;
        }

        &::-webkit-slider-thumb {
            appearance: none;
            height: 1.125rem; // 18px
            width: .5rem; // 8px
            margin-top: -.375rem; // -6px
            border-radius: .25rem; // 4px
            background: @primary-gradient;
            border: none;
            cursor: pointer;

            .transition(box-shadow);

            &:hover {
                box-shadow: 0 0 2px rgba(239, 78, 56, 0.4);
            }
        }

        &:focus {
            outline: none;

            &::-webkit-slider-thumb {
                box-shadow: 0 0 0 2px rgba(239, 78, 56, 0.2);
            }
        }

        &:disabled {
            opacity: 0.3;
            cursor: not-allowed;

            &::-webkit-slider-thumb {
                cursor: not-allowed;
            }

            ~ .form-range-labels {
                color: @text-secondary;
            }
        }
    }

    .form-range-labels {
        display: flex;
        justify-content: space-between;
        gap: 1rem;
        width: 100%;
        font-family: @font-family;
        font-size: .625rem; // 10px
        font-weight: 500;
        color: @text-color;

        .transition(color);

        > .label {
            flex: 1;
            text-align: center;

            &:first-child {
                flex: 0.5;
                text-align: left;
            }

            &:last-child {
                flex: 0.5;
                text-align: right;
            }
        }
    }

    // Input with icon
    .form-input-group {
        position: relative;
        display: flex;
        align-items: center;

        .form-input {
            flex: 1;
            padding-right: 2rem; // Space for icon
        }

        .form-input-icon {
            position: absolute;
            top: .3125rem; // 5px
            right: .3125rem; // 5px
            color: @text-secondary;
            pointer-events: none;

            &.clickable {
                pointer-events: auto;
                cursor: pointer;

                .transition(color);

                &:hover {
                    color: @text-color;
                }
            }
        }
    }

    .form-description {
        font-family: @font-family;
        font-size: 12px;
        line-height: 1.33;
        color: @text-secondary;
        margin-top: 4px;
    }
}

.form-row {
    display: flex;
    align-items: center;
    gap: .5rem;
}

// Button styles for forms
.form-button {
    font-family: @font-family;
    font-size: .875rem; // 14px
    font-weight: 400;
    line-height: 1.36;
    color: @text-color;
    background: @primary-gradient;
    border: 1px solid @border-color;
    border-radius: @border-radius-small;
    padding: .5rem .75rem; // 8px 12px
    min-height: 2.125rem; // 34px
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: .5rem;
    text-decoration: none;

    .transition(opacity, transform, box-shadow);

    &:hover:not(:disabled) {
        opacity: 0.9;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(239, 78, 56, 0.3);
    }

    &:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(239, 78, 56, 0.2);
    }

    &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(239, 78, 56, 0.2);
    }

    &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    &.form-button--secondary {
        background: @bg-overlay;
        color: @text-color;

        &:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
        }
    }

    &.form-button--small {
        font-size: 12px;
        padding: 6px 10px;
        min-height: 28px;
    }

    &.form-button--large {
        font-size: 16px;
        padding: 12px 20px;
        min-height: 44px;
    }
}


// Form validation states
.form-error {
    color: @primary-color;
    font-size: 12px;
    font-weight: 500;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;

    &::before {
        content: '⚠';
        font-size: 14px;
    }
}

.form-success {
    color: @success-color;
    font-size: 12px;
    font-weight: 500;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;

    &::before {
        content: '✓';
        font-size: 14px;
    }
}
