"use strict";const i=require("electron");class t{appId="com.actiphy.backup-and-standby";title="Actiphy Backup and Standby";bgColor="#f0f0f0";mainWindowWidth=1440;mainWindowHeight=780}const e=new t;class s{constructor(o){this.platform=o}window;run(){i.app.requestSingleInstanceLock()||i.app.quit(),i.app.on("second-instance",()=>this.openMainWindow()),i.app.on("ready",()=>{this.platform==="win32"&&(i.app.setAppUserModelId(e.appId),i.Menu.setApplicationMenu(null)),this.createMainWindow()}),i.app.on("activate",()=>{this.window||this.createMainWindow()}),i.app.on("window-all-closed",()=>{this.platform!=="darwin"&&i.app.quit()}),i.app.on("will-quit",()=>{this.platform==="darwin"&&i.app.dock?.hide()})}createMainWindow(){this.window=new i.BrowserWindow({show:!1,frame:!1,resizable:!0,width:e.mainWindowWidth,height:e.mainWindowHeight,title:e.title,backgroundColor:e.bgColor,titleBarStyle:"hidden",titleBarOverlay:{color:"#142a33",symbolColor:"#ffffff",height:39},webPreferences:{devTools:!process.env.prod,spellcheck:!1,enableWebSQL:!1}}),this.window.once("ready-to-show",()=>this.window?.show()),this.window.once("closed",()=>{this.window=void 0}),this.window.loadFile("browser/index.html"),this.window.webContents.setWindowOpenHandler(o=>(i.shell.openExternal(o.url),{action:"deny"})),process.env.prod||this.window.webContents.openDevTools({mode:"undocked"})}openMainWindow(){this.platform==="darwin"&&i.app.dock?.show(),this.window?(this.window.isMinimized()&&this.window.restore(),this.window.focus(),this.window.show()):this.createMainWindow()}}const a=new s(process.platform);a.run();
//# sourceMappingURL=index.js.map
