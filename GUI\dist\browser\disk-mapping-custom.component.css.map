{"version": 3, "sources": ["renderer/src/app/recovery/shared/recovery-destination/shared/disk-mapping-custom/disk-mapping-custom.component.less", "renderer/src/styles/mixins.less"], "sourcesContent": ["@import '../../../../../../styles/variables.less';\n@import '../../../../../../styles/mixins.less';\n\n:host {\n    display: block;\n\n    .disk-mapping {\n        display: grid;\n        grid-template-columns: 1fr auto 1fr;\n        flex: 1;\n        overflow-y: auto;\n    }\n\n    // Hide scrollbar for Chromium/WebKit (Electron)\n    &::-webkit-scrollbar {\n        display: none;\n    }\n\n    .source-column,\n    .target-column {\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n\n        .column-header {\n            h4 {\n                font-size: 1rem;\n                font-weight: 500;\n                color: @text-color;\n                margin: 0;\n                text-align: center;\n            }\n        }\n    }\n\n    .source-column-container{\n        position: relative;\n\n        .source-column:last-child {\n            position: absolute;\n            top: 0;\n            left: 0;\n            width: 100%;\n            z-index: 1;\n            opacity: 0;\n        }\n    }\n\n    .disk-item {\n        display: flex;\n        flex-direction: column;\n        gap: 0.5rem;\n        flex: 1;\n\n        .disk-header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            gap: 1rem;\n\n            .disk-name {\n                font-size: 0.75rem;\n                font-weight: 400;\n                color: @text-color;\n                line-height: 1.36;\n            }\n\n            .disk-type {\n                font-size: 0.75rem;\n                font-weight: 400;\n                color: @text-color;\n                line-height: 1.36;\n                text-align: center;\n                flex: 1;\n            }\n\n            .disk-info {\n                font-size: 0.75rem;\n                font-weight: 400;\n                color: @text-color;\n                line-height: 1.36;\n                text-align: right;\n            }\n        }\n\n        .disk-body {\n            display: flex;\n            flex-direction: column;\n            gap: 0.5rem;\n            border: 1px solid rgba(255, 255, 255, 0.1);\n            border-radius: 0.5rem;\n            padding: 0.5rem;\n            background: rgba(255, 255, 255, 0.1);\n            flex: 1;\n\n            .disk-icons {\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n\n                .disk-icon {\n                    width: 1.5rem;\n                    height: 1.5rem;\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    color: @text-color;\n\n                    fa-icon {\n                        font-size: 1.25rem;\n                    }\n                }\n            }\n\n            .disk-partitions {\n                display: flex;\n                flex-direction: column;\n                gap: 0.5rem;\n                min-height: 3rem;\n                flex: 1;\n\n                .more-options-disk {\n                    position: absolute;\n                    right: 0.5rem;\n                    top: 1rem;\n                    width: 1.2rem;\n                    height: 0.3rem;\n                    background: none;\n                    border: none;\n                    color: @text-color;\n                    cursor: pointer;\n\n                    .transition(color);\n\n                    &:hover {\n                        color: @primary-color;\n                    }\n\n                    fa-icon {\n                        font-size: 0.8rem;\n                    }\n                }\n            }\n        }\n    }\n\n    .arrow-section {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: flex-start;\n        gap: 0;\n        padding-top: 4.375rem;\n        min-width: 16rem;\n\n        .mapping-arrows {\n            display: flex;\n            flex-direction: column;\n            gap: .5rem;\n            width: 100%;\n\n            .arrow-line {\n                display: flex;\n                align-items: center;\n                width: 100%;\n                height: 4.375rem;\n\n                &:first-child {\n                    height: 1.25rem;\n\n                    &::before {\n                        background: rgba(204, 204, 204, 0.5);\n                    }\n\n                    &::after {\n                        border-left-color: rgba(204, 204, 204, 0.5);\n                    }\n                }\n\n                &::before {\n                    content: '';\n                    width: 100%;\n                    height: .25rem;\n                    background: rgba(16, 185, 129, 0.5);\n                }\n\n                &::after {\n                    content: '';\n                    border-top: 0.5rem solid transparent;\n                    border-bottom: 0.5rem solid transparent;\n                    border-left: 0.75rem solid rgba(16, 185, 129, 0.5);\n                }\n            }\n        }\n    }\n}\n\n.partition-item {\n    display: flex;\n    flex-direction: column;\n    gap: 0.5rem;\n    // background: rgba(255, 255, 255, 0.1);\n    background: rgba(16, 185, 129, 0.2);\n    border: 1px solid rgba(16, 185, 129, 0.5);\n    border-radius: 0.5rem;\n    padding: 0.625rem;\n\n    .partition-header {\n        display: flex;\n        align-items: center;\n        gap: 0.5rem;\n\n        .partition-icon {\n            color: @text-color;\n            font-size: 1rem;\n        }\n\n        .partition-name {\n            font-size: 0.75rem;\n            font-weight: 400;\n            color: @text-color;\n            line-height: 1.36;\n            flex: 1;\n        }\n\n        .more-options {\n            background: none;\n            border: none;\n            color: @text-color;\n            cursor: pointer;\n\n            .transition(color);\n\n            &:hover {\n                color: @primary-color;\n            }\n\n            fa-icon {\n                font-size: 0.8rem;\n            }\n        }\n    }\n\n    .partition-usage {\n        .usage-bar {\n            width: 100%;\n            height: 1.25rem;\n            background: linear-gradient(to bottom, #868686 0%, #ffffff 100%);\n            border-radius: 0.25rem;\n            overflow: hidden;\n            position: relative;\n\n            .usage-fill {\n                height: 100%;\n                background: linear-gradient(to right, #e73820 0%, #c32615 100%);\n                border-radius: 0.25rem;\n                transition: width 0.3s ease;\n            }\n        }\n    }\n}\n", "/**\n * Mixin\n * <AUTHOR>\n */\n\n@import \"variables.less\";\n\n// Transition\n.transition(...) {\n    & when (length(@arguments) = 0) {\n        transition: none;\n    }\n\n    & when (length(@arguments) = 1) {\n        transition: @arguments 300ms ease;\n    }\n\n    & when (length(@arguments) > 1) {\n        @properties: replace(\"@{arguments}\", \" \", \", \", \"g\");\n        transition-property: ~\"@{properties}\";\n        transition-timing-function: ease;\n        transition-duration: 300ms;\n    }\n}\n\n// Animations\n.animation(@name, @delay: 0ms) {\n    animation-duration: 500ms;\n    animation-fill-mode: forwards;\n    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n    animation-delay: @delay;\n\n    & when (@name = fade-in-up) {\n        animation-name: fadeInUp;\n    }\n\n    & when (@name = fade-in-left) {\n        animation-name: fadeInLeft;\n    }\n\n    & when (@name = fade-in) {\n        animation-name: fadeIn;\n    }\n}\n"], "mappings": ";AAGA;AACI,WAAA;;AADJ,MAGI,CAAA;AACI,WAAA;AACA,yBAAA,IAAA,KAAA;AACA,QAAA;AACA,cAAA;;AAIJ,KAAC;AACG,WAAA;;AAZR,MAeI,CAAA;AAfJ,MAgBI,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;;AAnBR,MAeI,CAAA,cAMI,CAAA,cACI;AAtBZ,MAgBI,CAAA,cAKI,CAAA,cACI;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA;;AA3BhB,MAgCI,CAAA;AACI,YAAA;;AAjCR,MAgCI,CAAA,wBAGI,CApBJ,aAoBkB;AACV,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,WAAA;AACA,WAAA;;AAzCZ,MA6CI,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;AACA,QAAA;;AAjDR,MA6CI,CAAA,UAMI,CAAA;AACI,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;;AAvDZ,MA6CI,CAAA,UAMI,CAAA,YAMI,CAAA;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;;AA7DhB,MA6CI,CAAA,UAMI,CAAA,YAaI,CAAA;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;AACA,cAAA;AACA,QAAA;;AAtEhB,MA6CI,CAAA,UAMI,CAAA,YAsBI,CAAA;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;AACA,cAAA;;AA9EhB,MA6CI,CAAA,UAqCI,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;AACA,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,WAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,QAAA;;AA1FZ,MA6CI,CAAA,UAqCI,CAAA,UAUI,CAAA;AACI,WAAA;AACA,eAAA;AACA,mBAAA;;AA/FhB,MA6CI,CAAA,UAqCI,CAAA,UAUI,CAAA,WAKI,CAAA;AACI,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,SAAA;;AAvGpB,MA6CI,CAAA,UAqCI,CAAA,UAUI,CAAA,WAKI,CAAA,UAQI;AACI,aAAA;;AA1GxB,MA6CI,CAAA,UAqCI,CAAA,UA6BI,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;AACA,cAAA;AACA,QAAA;;AApHhB,MA6CI,CAAA,UAqCI,CAAA,UA6BI,CAAA,gBAOI,CAAA;AACI,YAAA;AACA,SAAA;AACA,OAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA;AACA,UAAA;AACA,SAAA;AACA,UAAA;ACpHZ,cAAA,MAAA,MAAA;;ADwHY,MAtFhB,CAAA,UAqCI,CAAA,UA6BI,CAAA,gBAOI,CAAA,iBAaK;AACG,SAAA;;AApIxB,MA6CI,CAAA,UAqCI,CAAA,UA6BI,CAAA,gBAOI,CAAA,kBAiBI;AACI,aAAA;;AAxIxB,MA+II,CAAA;AACI,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA;AACA,eAAA;AACA,aAAA;;AAtJR,MA+II,CAAA,cASI,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;AACA,SAAA;;AA5JZ,MA+II,CAAA,cASI,CAAA,eAMI,CAAA;AACI,WAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA;;AAEA,MArBZ,CAAA,cASI,CAAA,eAMI,CAAA,UAMK;AACG,UAAA;;AAEA,MAxBhB,CAAA,cASI,CAAA,eAMI,CAAA,UAMK,YAGI;AACG,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,MA5BhB,CAAA,cASI,CAAA,eAMI,CAAA,UAMK,YAOI;AACG,qBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAIR,MAjCZ,CAAA,cASI,CAAA,eAMI,CAAA,UAkBK;AACG,WAAS;AACT,SAAA;AACA,UAAA;AACA,cAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,MAxCZ,CAAA,cASI,CAAA,eAMI,CAAA,UAyBK;AACG,WAAS;AACT,cAAA,OAAA,MAAA;AACA,iBAAA,OAAA,MAAA;AACA,eAAA,QAAA,MAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAOpB,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;AAEA,cAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA,IAAA,MAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,WAAA;;AARJ,CAAA,eAUI,CAAA;AACI,WAAA;AACA,eAAA;AACA,OAAA;;AAbR,CAAA,eAUI,CAAA,iBAKI,CAAA;AACI,SAAA;AACA,aAAA;;AAjBZ,CAAA,eAUI,CAAA,iBAUI,CAAA;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;AACA,QAAA;;AAzBZ,CAAA,eAUI,CAAA,iBAkBI,CAAA;AACI,cAAA;AACA,UAAA;AACA,SAAA;AACA,UAAA;ACvNJ,cAAA,MAAA,MAAA;;AD2NI,CApCZ,eAUI,CAAA,iBAkBI,CAAA,YAQK;AACG,SAAA;;AArChB,CAAA,eAUI,CAAA,iBAkBI,CAAA,aAYI;AACI,aAAA;;AAzChB,CAAA,eA8CI,CAAA,gBACI,CAAA;AACI,SAAA;AACA,UAAA;AACA;IAAY;MAAA,GAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACZ,iBAAA;AACA,YAAA;AACA,YAAA;;AArDZ,CAAA,eA8CI,CAAA,gBACI,CAAA,UAQI,CAAA;AACI,UAAA;AACA;IAAY;MAAA,GAAA,KAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACZ,iBAAA;AACA,cAAA,MAAA,KAAA;;", "names": []}