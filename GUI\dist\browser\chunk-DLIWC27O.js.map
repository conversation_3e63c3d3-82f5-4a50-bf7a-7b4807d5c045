{"version": 3, "sources": ["renderer/src/app/preferences/notifications/notifications.component.ts", "renderer/src/app/preferences/notifications/notifications.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\n\r\n@Component({\r\n    selector: 'app-notifications',\r\n    imports: [FontAwesomeModule],\r\n    templateUrl: './notifications.component.html',\r\n    styleUrl: './notifications.component.less'\r\n})\r\nexport class NotificationsComponent {\r\n}\r\n", "<header>\r\n    <div class=\"icon\">\r\n        <fa-icon icon=\"bell\"></fa-icon>\r\n    </div>\r\n    <h2>Notifications</h2>\r\n</header>\r\n\r\n<section>\r\n    <p>Notification settings will be implemented here.</p>\r\n</section>\r\n\r\n<footer>\r\n    <span class=\"notice\">0 Configuration changes waiting to be applied</span>\r\n    <div class=\"buttons\">\r\n        <button type=\"button\" class=\"button secondary\">Cancel</button>\r\n        <button type=\"button\" class=\"button primary\">Apply</button>\r\n    </div>\r\n</footer>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AASM,IAAO,yBAAP,MAAO,wBAAsB;;qCAAtB,yBAAsB;EAAA;yEAAtB,yBAAsB,WAAA,CAAA,CAAA,mBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,MAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,WAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,SAAA,CAAA,GAAA,UAAA,SAAA,gCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACTnC,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,OAAA,CAAA;AAEA,MAAA,oBAAA,GAAA,WAAA,CAAA;AACJ,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,eAAA;AAAa,MAAA,uBAAA,EAAK;AAG1B,MAAA,yBAAA,GAAA,SAAA,EAAS,GAAA,GAAA;AACF,MAAA,iBAAA,GAAA,iDAAA;AAA+C,MAAA,uBAAA,EAAI;AAG1D,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,QAAA,CAAA;AACiB,MAAA,iBAAA,IAAA,+CAAA;AAA6C,MAAA,uBAAA;AAClE,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,UAAA,CAAA;AAC8B,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AACrD,MAAA,yBAAA,IAAA,UAAA,CAAA;AAA6C,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAS,EACzD;;oBDXI,mBAAiB,eAAA,GAAA,QAAA,CAAA,29CAAA,EAAA,CAAA;;;sEAIlB,wBAAsB,CAAA;UANlC;uBACa,qBAAmB,SACpB,CAAC,iBAAiB,GAAC,UAAA,qhBAAA,QAAA,CAAA,otCAAA,EAAA,CAAA;;;;6EAInB,wBAAsB,EAAA,WAAA,0BAAA,UAAA,yEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}