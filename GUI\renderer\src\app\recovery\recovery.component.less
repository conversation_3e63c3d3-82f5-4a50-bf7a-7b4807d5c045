@import '../../styles/variables.less';
@import '../../styles/mixins.less';
@import '../shared/wizard.less';

:host {
    // Content Container
    section {
        .panel {
            .animation(fade-in-up, 50ms);
            .transition(flex, box-shadow);

            &:nth-child(2) {
                animation-delay: 100ms;
            }

            &:nth-child(3) {
                animation-delay: 150ms;
            }

            &.expanded {
                flex: 1;
                cursor: default;
            }

            &.collapsed {
                flex: 0 0 5rem;
                width: 5rem;
                padding: 1.5rem 1rem;
                align-items: center;
                justify-content: flex-start;
                position: relative;
            }
        }
    }
}
