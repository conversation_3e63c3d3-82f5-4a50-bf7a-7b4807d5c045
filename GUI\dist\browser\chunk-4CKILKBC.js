import {
  FaIconComponent,
  FontAwesomeModule
} from "./chunk-N34W2DJ7.js";
import {
  Component,
  ViewChild,
  ViewChildren,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵloadQuery,
  ɵɵqueryRefresh,
  ɵɵtext,
  ɵɵviewQuery
} from "./chunk-TBQAZTY7.js";

// renderer/src/app/dashboard/dashboard.component.ts
var _c0 = ["storageValue"];
var _c1 = ["storagePercentage"];
var _c2 = ["backupValue"];
var _c3 = ["recoveryValue"];
var _c4 = ["progressFill"];
var DashboardComponent = class _DashboardComponent {
  progressFills;
  storageValue;
  storagePercentage;
  backupValue;
  recoveryValue;
  ngAfterViewInit() {
    setTimeout(() => {
      this.progressFills.forEach((element) => {
        const el = element.nativeElement;
        const width = el.style.width;
        if (width) {
          el.style.setProperty("--progress-width", width);
          el.style.width = "var(--progress-width)";
        }
      });
      this.animateCounter(this.backupValue.nativeElement, 0, 147, 700);
      this.animateCounter(this.recoveryValue.nativeElement, 0, 1234, 700);
    }, 300);
  }
  // Number counter animation - optimized for faster display
  animateCounter(element, start, end, duration) {
    const steps = Math.min(30, end - start);
    const stepValue = (end - start) / steps;
    const stepTime = duration / steps;
    let current = start;
    let step = 0;
    const formatNumber = (num) => {
      return Math.round(num).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    };
    const timer = setInterval(() => {
      step++;
      current = start + stepValue * step;
      if (step >= steps) {
        current = end;
        clearInterval(timer);
      }
      element.textContent = formatNumber(current);
    }, stepTime);
  }
  static \u0275fac = function DashboardComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DashboardComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DashboardComponent, selectors: [["app-dashboard"]], viewQuery: function DashboardComponent_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c0, 5);
      \u0275\u0275viewQuery(_c1, 5);
      \u0275\u0275viewQuery(_c2, 5);
      \u0275\u0275viewQuery(_c3, 5);
      \u0275\u0275viewQuery(_c4, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.storageValue = _t.first);
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.storagePercentage = _t.first);
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.backupValue = _t.first);
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.recoveryValue = _t.first);
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.progressFills = _t);
    }
  }, decls: 108, vars: 0, consts: [["storageValue", ""], ["storagePercentage", ""], ["progressFill", ""], ["backupValue", ""], ["recoveryValue", ""], [1, "welcome"], [1, "row"], [1, "widget", "storage"], [1, "widget-header"], [1, "widget-title"], [1, "icon-container"], ["icon", "hdd"], [1, "widget-value"], [1, "primary-value", "animated"], [1, "secondary-value", "animated"], [1, "progress-bar"], [1, "progress-fill", 2, "width", "48%"], [1, "widget", "backup"], [1, "widget-content"], [1, "icon-container", "green"], ["icon", "box-archive"], [1, "widget-value", "animated"], [1, "widget-footer"], [1, "trend-icon"], ["icon", "arrow-up"], [1, "trend-text"], [1, "widget", "recovery"], ["icon", "clock-rotate-left"], [1, "widget-info"], [1, "widget", "recent-backups"], [1, "widget-head"], [1, "view-all"], [1, "backups-list"], [1, "backup-item"], [1, "backup-info"], [1, "backup-icon"], ["icon", "database"], [1, "backup-details"], [1, "status-badge", "success"], ["icon", "file"], [1, "widget", "quick-actions"], [1, "actions-grid"], [1, "action-button", "primary"], ["icon", "plus"], [1, "action-button"], ["icon", "rotate-left"], ["icon", "play"], ["icon", "copy"], [1, "widget", "empty-widget"]], template: function DashboardComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "section")(1, "div", 5)(2, "h1");
      \u0275\u0275text(3, "Welcome back, Admin");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p");
      \u0275\u0275text(5, "Here's what's happening with your backups today.");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 6)(7, "div", 7)(8, "div", 8)(9, "div", 9)(10, "span");
      \u0275\u0275text(11, "Storage Usage");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "div", 10);
      \u0275\u0275element(13, "fa-icon", 11);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(14, "div", 12)(15, "span", 13, 0);
      \u0275\u0275text(17, "2.4 TB");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "span", 14, 1);
      \u0275\u0275text(20, "48%");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(21, "div", 15);
      \u0275\u0275element(22, "div", 16, 2);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(24, "div", 17)(25, "div", 18)(26, "div", 8)(27, "span");
      \u0275\u0275text(28, "Active Backups");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(29, "div", 19);
      \u0275\u0275element(30, "fa-icon", 20);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(31, "p", 21, 3);
      \u0275\u0275text(33, "147");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(34, "div", 22)(35, "div", 23);
      \u0275\u0275element(36, "fa-icon", 24);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(37, "span", 25);
      \u0275\u0275text(38, "12% from last week");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(39, "div", 26)(40, "div", 18)(41, "div", 8)(42, "span");
      \u0275\u0275text(43, "Recovery Points");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(44, "div", 10);
      \u0275\u0275element(45, "fa-icon", 27);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(46, "p", 21, 4);
      \u0275\u0275text(48, "1,234");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(49, "p", 28);
      \u0275\u0275text(50, "Last point: 5 mins ago");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(51, "div", 29)(52, "div", 30)(53, "h3");
      \u0275\u0275text(54, "Recent Backups");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(55, "button", 31);
      \u0275\u0275text(56, "View All");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(57, "div", 32)(58, "div", 33)(59, "div", 34)(60, "div", 35);
      \u0275\u0275element(61, "fa-icon", 36);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(62, "div", 37)(63, "h4");
      \u0275\u0275text(64, "Production DB");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(65, "span");
      \u0275\u0275text(66, "2.1 GB \u2022 5 mins ago");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(67, "span", 38);
      \u0275\u0275text(68, "Success");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(69, "div", 33)(70, "div", 34)(71, "div", 35);
      \u0275\u0275element(72, "fa-icon", 39);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(73, "div", 37)(74, "h4");
      \u0275\u0275text(75, "User Files");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(76, "span");
      \u0275\u0275text(77, "5.4 GB \u2022 15 mins ago");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(78, "span", 38);
      \u0275\u0275text(79, "Success");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(80, "div", 40)(81, "h3");
      \u0275\u0275text(82, "Quick Actions");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(83, "div", 41)(84, "button", 42);
      \u0275\u0275element(85, "fa-icon", 43);
      \u0275\u0275elementStart(86, "span");
      \u0275\u0275text(87, "New Backup");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(88, "button", 44);
      \u0275\u0275element(89, "fa-icon", 45);
      \u0275\u0275elementStart(90, "span");
      \u0275\u0275text(91, "Recover");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(92, "button", 44);
      \u0275\u0275element(93, "fa-icon", 46);
      \u0275\u0275elementStart(94, "span");
      \u0275\u0275text(95, "Standby");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(96, "button", 44);
      \u0275\u0275element(97, "fa-icon", 47);
      \u0275\u0275elementStart(98, "span");
      \u0275\u0275text(99, "Replication");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(100, "div", 48)(101, "div", 30)(102, "h3");
      \u0275\u0275text(103, "Another Widget");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(104, "div", 48)(105, "div", 30)(106, "h3");
      \u0275\u0275text(107, "Another Widget");
      \u0275\u0275elementEnd()()()()();
    }
  }, dependencies: [FontAwesomeModule, FaIconComponent], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n  overflow-y: auto;\n  height: 100%;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  padding: 1rem;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .welcome[_ngcontent-%COMP%] {\n  animation: fadeInUp 0.5s cubic-bezier(0.215, 0.61, 0.355, 1) forwards;\n  animation-delay: 0.02s;\n  opacity: 0;\n  will-change: transform, opacity;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .welcome[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .welcome[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(6, 1fr);\n  gap: 1rem;\n  width: 100%;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget[_ngcontent-%COMP%] {\n  background-color: rgba(255, 255, 255, 0.1);\n  border-radius: 0.75rem;\n  transition: box-shadow 0.3s ease, background-color 0.3s ease;\n  animation: fadeInUp 0.5s cubic-bezier(0.215, 0.61, 0.355, 1) forwards;\n  position: relative;\n  overflow: hidden;\n  opacity: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 0.5rem 1.25rem rgba(0, 0, 0, 0.2);\n  background-color: rgba(255, 255, 255, 0.15);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%] {\n  grid-column: span 2;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.empty-widget[_ngcontent-%COMP%] {\n  grid-column: span 3;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%] {\n  padding: 1rem;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  animation-delay: 0.05s;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .widget-title[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .widget-title[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  font-size: 18px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .widget-title[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%] {\n  width: 17.5px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .widget-title[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  fill: #36AB6B;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .widget-value[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .widget-value[_ngcontent-%COMP%]   .primary-value[_ngcontent-%COMP%] {\n  font-size: 30px;\n  font-weight: 700;\n  margin-right: 12px;\n  position: relative;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .widget-value[_ngcontent-%COMP%]   .primary-value.animated[_ngcontent-%COMP%] {\n  opacity: 0;\n  animation: fadeIn 0.4s cubic-bezier(0.215, 0.61, 0.355, 1) forwards 0.15s;\n  will-change: opacity;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .widget-value[_ngcontent-%COMP%]   .secondary-value[_ngcontent-%COMP%] {\n  font-size: 16px;\n  color: rgba(255, 255, 255, 0.5);\n  position: relative;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .widget-value[_ngcontent-%COMP%]   .secondary-value.animated[_ngcontent-%COMP%] {\n  opacity: 0;\n  animation: fadeIn 0.4s cubic-bezier(0.215, 0.61, 0.355, 1) forwards 0.2s;\n  will-change: opacity;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\n  height: 8px;\n  background-color: rgba(255, 255, 255, 0.1);\n  border-radius: 9999px;\n  overflow: hidden;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.storage[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      #E73820 0%,\n      #C32615 100%);\n  border-radius: 9999px;\n  width: 0;\n  animation: progressAnimate 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards 0.1s;\n  will-change: width;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%] {\n  padding: 1rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup.backup[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery.backup[_ngcontent-%COMP%] {\n  animation-delay: 0.1s;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup.recovery[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery.recovery[_ngcontent-%COMP%] {\n  animation-delay: 0.15s;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  font-size: 18px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .icon-container.green[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]   .icon-container.green[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  fill: #34d399;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-value[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-value[_ngcontent-%COMP%] {\n  font-size: 30px;\n  font-weight: 700;\n  line-height: 1.21;\n  margin: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-value.animated[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-value.animated[_ngcontent-%COMP%] {\n  opacity: 0;\n  animation: countUp 0.5s cubic-bezier(0.215, 0.61, 0.355, 1) forwards 0.15s;\n  position: relative;\n  will-change: transform, opacity;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-info[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-info[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-footer[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-footer[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-footer[_ngcontent-%COMP%]   .trend-icon[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-footer[_ngcontent-%COMP%]   .trend-icon[_ngcontent-%COMP%] {\n  width: 10.5px;\n  height: 14px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-footer[_ngcontent-%COMP%]   .trend-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-footer[_ngcontent-%COMP%]   .trend-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  fill: #34d399;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.backup[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-footer[_ngcontent-%COMP%]   .trend-text[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recovery[_ngcontent-%COMP%]   .widget-content[_ngcontent-%COMP%]   .widget-footer[_ngcontent-%COMP%]   .trend-text[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #34d399;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions[_ngcontent-%COMP%] {\n  padding: 24px;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups.recent-backups[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions.recent-backups[_ngcontent-%COMP%] {\n  animation-delay: 0.2s;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups.quick-actions[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions.quick-actions[_ngcontent-%COMP%] {\n  animation-delay: 0.25s;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .widget-head[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions[_ngcontent-%COMP%]   .widget-head[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .widget-head[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions[_ngcontent-%COMP%]   .widget-head[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 18px;\n  font-weight: 400;\n  margin: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .widget-head[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%], \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions[_ngcontent-%COMP%]   .widget-head[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%] {\n  background: transparent;\n  border: none;\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 14px;\n  cursor: pointer;\n  padding: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .widget-head[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]:hover, \n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions[_ngcontent-%COMP%]   .widget-head[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]:hover {\n  color: #ffffff;\n  text-decoration: underline;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .backups-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .backups-list[_ngcontent-%COMP%]   .backup-item[_ngcontent-%COMP%] {\n  background-color: rgba(255, 255, 255, 0.05);\n  border-radius: 0.5rem;\n  padding: 12px 16px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .backups-list[_ngcontent-%COMP%]   .backup-item[_ngcontent-%COMP%]:hover {\n  background-color: rgba(255, 255, 255, 0.15);\n  transform: translateX(5px);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .backups-list[_ngcontent-%COMP%]   .backup-item[_ngcontent-%COMP%]   .backup-info[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .backups-list[_ngcontent-%COMP%]   .backup-item[_ngcontent-%COMP%]   .backup-info[_ngcontent-%COMP%]   .backup-icon[_ngcontent-%COMP%] {\n  width: 14px;\n  height: 16px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .backups-list[_ngcontent-%COMP%]   .backup-item[_ngcontent-%COMP%]   .backup-info[_ngcontent-%COMP%]   .backup-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .backups-list[_ngcontent-%COMP%]   .backup-item[_ngcontent-%COMP%]   .backup-info[_ngcontent-%COMP%]   .backup-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  font-size: 16px;\n  font-weight: 500;\n  margin: 0 0 4px 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .backups-list[_ngcontent-%COMP%]   .backup-item[_ngcontent-%COMP%]   .backup-info[_ngcontent-%COMP%]   .backup-details[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.5);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .backups-list[_ngcontent-%COMP%]   .backup-item[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\n  padding: 4px 10px;\n  border-radius: 9999px;\n  font-size: 14px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.recent-backups[_ngcontent-%COMP%]   .backups-list[_ngcontent-%COMP%]   .backup-item[_ngcontent-%COMP%]   .status-badge.success[_ngcontent-%COMP%] {\n  background-color: rgba(16, 185, 129, 0.2);\n  color: #34d399;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\n  flex: 1 0 calc(50% - 8px);\n  min-width: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n  padding: 10px 16px;\n  height: 52px;\n  border-radius: 0.75rem;\n  background-color: rgba(255, 255, 255, 0.1);\n  border: none;\n  color: #ffffff;\n  font-size: 16px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(255, 255, 255, 0.2);\n  transform: scale(1.03);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button.primary[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      90deg,\n      #ef4e38 0%,\n      #b61e0e 100%);\n  animation: pulse 2s infinite;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button.primary[_ngcontent-%COMP%]:hover {\n  opacity: 0.9;\n  transform: scale(1.03);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.empty-widget[_ngcontent-%COMP%] {\n  padding: 24px;\n  animation-delay: 0.3s;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.empty-widget[_ngcontent-%COMP%]:nth-of-type(odd) {\n  animation-delay: 0.3s;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.empty-widget[_ngcontent-%COMP%]:nth-of-type(even) {\n  animation-delay: 0.35s;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .widget.empty-widget[_ngcontent-%COMP%]   .widget-head[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 18px;\n  font-weight: 400;\n  margin: 0;\n}\n/*# sourceMappingURL=dashboard.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DashboardComponent, [{
    type: Component,
    args: [{ selector: "app-dashboard", imports: [FontAwesomeModule], template: `<section>\r
    <div class="welcome">\r
        <h1>Welcome back, Admin</h1>\r
        <p>Here's what's happening with your backups today.</p>\r
    </div>\r
    <!-- Place all widgets in a unified grid container -->\r
    <div class="row">\r
        <!-- First three widgets each occupy 2 grid columns -->\r
        <div class="widget storage">\r
            <div class="widget-header">\r
                <div class="widget-title">\r
                    <span>Storage Usage</span>\r
                    <div class="icon-container">\r
                        <fa-icon icon="hdd"></fa-icon>\r
                    </div>\r
                </div>\r
                <div class="widget-value">\r
                    <span #storageValue class="primary-value animated">2.4 TB</span>\r
                    <span #storagePercentage class="secondary-value animated">48%</span>\r
                </div>\r
                <div class="progress-bar">\r
                    <div #progressFill class="progress-fill" style="width: 48%"></div>\r
                </div>\r
            </div>\r
        </div>\r
        <div class="widget backup">\r
            <div class="widget-content">\r
                <div class="widget-header">\r
                    <span>Active Backups</span>\r
                    <div class="icon-container green">\r
                        <fa-icon icon="box-archive"></fa-icon>\r
                    </div>\r
                </div>\r
                <p #backupValue class="widget-value animated">147</p>\r
                <div class="widget-footer">\r
                    <div class="trend-icon">\r
                        <fa-icon icon="arrow-up"></fa-icon>\r
                    </div>\r
                    <span class="trend-text">12% from last week</span>\r
                </div>\r
            </div>\r
        </div>\r
        <div class="widget recovery">\r
            <div class="widget-content">\r
                <div class="widget-header">\r
                    <span>Recovery Points</span>\r
                    <div class="icon-container">\r
                        <fa-icon icon="clock-rotate-left"></fa-icon>\r
                    </div>\r
                </div>\r
                <p #recoveryValue class="widget-value animated">1,234</p>\r
                <p class="widget-info">Last point: 5 mins ago</p>\r
            </div>\r
        </div>\r
        <!-- Last four widgets each occupy 3 grid columns -->\r
        <!-- TODO: Extract into recent-backups component -->\r
        <div class="widget recent-backups">\r
            <div class="widget-head">\r
                <h3>Recent Backups</h3>\r
                <button class="view-all">View All</button>\r
            </div>\r
            <div class="backups-list">\r
                <div class="backup-item">\r
                    <div class="backup-info">\r
                        <div class="backup-icon">\r
                            <fa-icon icon="database"></fa-icon>\r
                        </div>\r
                        <div class="backup-details">\r
                            <h4>Production DB</h4>\r
                            <span>2.1 GB \u2022 5 mins ago</span>\r
                        </div>\r
                    </div>\r
                    <span class="status-badge success">Success</span>\r
                </div>\r
                <div class="backup-item">\r
                    <div class="backup-info">\r
                        <div class="backup-icon">\r
                            <fa-icon icon="file"></fa-icon>\r
                        </div>\r
                        <div class="backup-details">\r
                            <h4>User Files</h4>\r
                            <span>5.4 GB \u2022 15 mins ago</span>\r
                        </div>\r
                    </div>\r
                    <span class="status-badge success">Success</span>\r
                </div>\r
            </div>\r
        </div>\r
        <!-- TODO: Extract into quick-actions component -->\r
        <div class="widget quick-actions">\r
            <h3>Quick Actions</h3>\r
            <div class="actions-grid">\r
                <button class="action-button primary">\r
                    <fa-icon icon="plus"></fa-icon>\r
                    <span>New Backup</span>\r
                </button>\r
                <button class="action-button">\r
                    <fa-icon icon="rotate-left"></fa-icon>\r
                    <span>Recover</span>\r
                </button>\r
                <button class="action-button">\r
                    <fa-icon icon="play"></fa-icon>\r
                    <span>Standby</span>\r
                </button>\r
                <button class="action-button">\r
                    <fa-icon icon="copy"></fa-icon>\r
                    <span>Replication</span>\r
                </button>\r
            </div>\r
        </div>\r
        <div class="widget empty-widget">\r
            <div class="widget-head">\r
                <h3>Another Widget</h3>\r
            </div>\r
        </div>\r
        <div class="widget empty-widget">\r
            <div class="widget-head">\r
                <h3>Another Widget</h3>\r
            </div>\r
        </div>\r
    </div>\r
</section>\r
`, styles: ["/* renderer/src/app/dashboard/dashboard.component.less */\n:host {\n  display: block;\n  overflow-y: auto;\n  height: 100%;\n}\n:host section {\n  padding: 1rem;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n:host section .welcome {\n  animation: fadeInUp 0.5s cubic-bezier(0.215, 0.61, 0.355, 1) forwards;\n  animation-delay: 0.02s;\n  opacity: 0;\n  will-change: transform, opacity;\n}\n:host section .welcome h1 {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n}\n:host section .welcome p {\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n}\n:host section .row {\n  display: grid;\n  grid-template-columns: repeat(6, 1fr);\n  gap: 1rem;\n  width: 100%;\n}\n:host section .row .widget {\n  background-color: rgba(255, 255, 255, 0.1);\n  border-radius: 0.75rem;\n  transition: box-shadow 0.3s ease, background-color 0.3s ease;\n  animation: fadeInUp 0.5s cubic-bezier(0.215, 0.61, 0.355, 1) forwards;\n  position: relative;\n  overflow: hidden;\n  opacity: 0;\n}\n:host section .row .widget:hover {\n  box-shadow: 0 0.5rem 1.25rem rgba(0, 0, 0, 0.2);\n  background-color: rgba(255, 255, 255, 0.15);\n}\n:host section .row .widget.storage,\n:host section .row .widget.backup,\n:host section .row .widget.recovery {\n  grid-column: span 2;\n}\n:host section .row .widget.recent-backups,\n:host section .row .widget.quick-actions,\n:host section .row .widget.empty-widget {\n  grid-column: span 3;\n}\n:host section .row .widget.storage {\n  padding: 1rem;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  animation-delay: 0.05s;\n}\n:host section .row .widget.storage .widget-header {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n:host section .row .widget.storage .widget-header .widget-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n:host section .row .widget.storage .widget-header .widget-title span {\n  font-size: 18px;\n}\n:host section .row .widget.storage .widget-header .widget-title .icon-container {\n  width: 17.5px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n}\n:host section .row .widget.storage .widget-header .widget-title .icon-container img {\n  fill: #36AB6B;\n}\n:host section .row .widget.storage .widget-header .widget-value {\n  display: flex;\n  align-items: center;\n}\n:host section .row .widget.storage .widget-header .widget-value .primary-value {\n  font-size: 30px;\n  font-weight: 700;\n  margin-right: 12px;\n  position: relative;\n}\n:host section .row .widget.storage .widget-header .widget-value .primary-value.animated {\n  opacity: 0;\n  animation: fadeIn 0.4s cubic-bezier(0.215, 0.61, 0.355, 1) forwards 0.15s;\n  will-change: opacity;\n}\n:host section .row .widget.storage .widget-header .widget-value .secondary-value {\n  font-size: 16px;\n  color: rgba(255, 255, 255, 0.5);\n  position: relative;\n}\n:host section .row .widget.storage .widget-header .widget-value .secondary-value.animated {\n  opacity: 0;\n  animation: fadeIn 0.4s cubic-bezier(0.215, 0.61, 0.355, 1) forwards 0.2s;\n  will-change: opacity;\n}\n:host section .row .widget.storage .widget-header .progress-bar {\n  height: 8px;\n  background-color: rgba(255, 255, 255, 0.1);\n  border-radius: 9999px;\n  overflow: hidden;\n}\n:host section .row .widget.storage .widget-header .progress-bar .progress-fill {\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      #E73820 0%,\n      #C32615 100%);\n  border-radius: 9999px;\n  width: 0;\n  animation: progressAnimate 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards 0.1s;\n  will-change: width;\n}\n:host section .row .widget.backup,\n:host section .row .widget.recovery {\n  padding: 1rem;\n}\n:host section .row .widget.backup.backup,\n:host section .row .widget.recovery.backup {\n  animation-delay: 0.1s;\n}\n:host section .row .widget.backup.recovery,\n:host section .row .widget.recovery.recovery {\n  animation-delay: 0.15s;\n}\n:host section .row .widget.backup .widget-content,\n:host section .row .widget.recovery .widget-content {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n:host section .row .widget.backup .widget-content .widget-header,\n:host section .row .widget.recovery .widget-content .widget-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n:host section .row .widget.backup .widget-content .widget-header span,\n:host section .row .widget.recovery .widget-content .widget-header span {\n  font-size: 18px;\n}\n:host section .row .widget.backup .widget-content .widget-header .icon-container,\n:host section .row .widget.recovery .widget-content .widget-header .icon-container {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n:host section .row .widget.backup .widget-content .widget-header .icon-container.green img,\n:host section .row .widget.recovery .widget-content .widget-header .icon-container.green img {\n  fill: #34d399;\n}\n:host section .row .widget.backup .widget-content .widget-value,\n:host section .row .widget.recovery .widget-content .widget-value {\n  font-size: 30px;\n  font-weight: 700;\n  line-height: 1.21;\n  margin: 0;\n}\n:host section .row .widget.backup .widget-content .widget-value.animated,\n:host section .row .widget.recovery .widget-content .widget-value.animated {\n  opacity: 0;\n  animation: countUp 0.5s cubic-bezier(0.215, 0.61, 0.355, 1) forwards 0.15s;\n  position: relative;\n  will-change: transform, opacity;\n}\n:host section .row .widget.backup .widget-content .widget-info,\n:host section .row .widget.recovery .widget-content .widget-info {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n}\n:host section .row .widget.backup .widget-content .widget-footer,\n:host section .row .widget.recovery .widget-content .widget-footer {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n:host section .row .widget.backup .widget-content .widget-footer .trend-icon,\n:host section .row .widget.recovery .widget-content .widget-footer .trend-icon {\n  width: 10.5px;\n  height: 14px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n:host section .row .widget.backup .widget-content .widget-footer .trend-icon img,\n:host section .row .widget.recovery .widget-content .widget-footer .trend-icon img {\n  fill: #34d399;\n}\n:host section .row .widget.backup .widget-content .widget-footer .trend-text,\n:host section .row .widget.recovery .widget-content .widget-footer .trend-text {\n  font-size: 14px;\n  color: #34d399;\n}\n:host section .row .widget.recent-backups,\n:host section .row .widget.quick-actions {\n  padding: 24px;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n:host section .row .widget.recent-backups.recent-backups,\n:host section .row .widget.quick-actions.recent-backups {\n  animation-delay: 0.2s;\n}\n:host section .row .widget.recent-backups.quick-actions,\n:host section .row .widget.quick-actions.quick-actions {\n  animation-delay: 0.25s;\n}\n:host section .row .widget.recent-backups .widget-head,\n:host section .row .widget.quick-actions .widget-head {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n:host section .row .widget.recent-backups .widget-head h3,\n:host section .row .widget.quick-actions .widget-head h3 {\n  font-size: 18px;\n  font-weight: 400;\n  margin: 0;\n}\n:host section .row .widget.recent-backups .widget-head .view-all,\n:host section .row .widget.quick-actions .widget-head .view-all {\n  background: transparent;\n  border: none;\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 14px;\n  cursor: pointer;\n  padding: 0;\n}\n:host section .row .widget.recent-backups .widget-head .view-all:hover,\n:host section .row .widget.quick-actions .widget-head .view-all:hover {\n  color: #ffffff;\n  text-decoration: underline;\n}\n:host section .row .widget.recent-backups .backups-list {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n:host section .row .widget.recent-backups .backups-list .backup-item {\n  background-color: rgba(255, 255, 255, 0.05);\n  border-radius: 0.5rem;\n  padding: 12px 16px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n:host section .row .widget.recent-backups .backups-list .backup-item:hover {\n  background-color: rgba(255, 255, 255, 0.15);\n  transform: translateX(5px);\n}\n:host section .row .widget.recent-backups .backups-list .backup-item .backup-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n:host section .row .widget.recent-backups .backups-list .backup-item .backup-info .backup-icon {\n  width: 14px;\n  height: 16px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n:host section .row .widget.recent-backups .backups-list .backup-item .backup-info .backup-icon img {\n  width: 100%;\n  height: 100%;\n}\n:host section .row .widget.recent-backups .backups-list .backup-item .backup-info .backup-details h4 {\n  font-size: 16px;\n  font-weight: 500;\n  margin: 0 0 4px 0;\n}\n:host section .row .widget.recent-backups .backups-list .backup-item .backup-info .backup-details span {\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.5);\n}\n:host section .row .widget.recent-backups .backups-list .backup-item .status-badge {\n  padding: 4px 10px;\n  border-radius: 9999px;\n  font-size: 14px;\n}\n:host section .row .widget.recent-backups .backups-list .backup-item .status-badge.success {\n  background-color: rgba(16, 185, 129, 0.2);\n  color: #34d399;\n}\n:host section .row .widget.quick-actions .actions-grid {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n}\n:host section .row .widget.quick-actions .actions-grid .action-button {\n  flex: 1 0 calc(50% - 8px);\n  min-width: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n  padding: 10px 16px;\n  height: 52px;\n  border-radius: 0.75rem;\n  background-color: rgba(255, 255, 255, 0.1);\n  border: none;\n  color: #ffffff;\n  font-size: 16px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n:host section .row .widget.quick-actions .actions-grid .action-button:hover {\n  background-color: rgba(255, 255, 255, 0.2);\n  transform: scale(1.03);\n}\n:host section .row .widget.quick-actions .actions-grid .action-button.primary {\n  background:\n    linear-gradient(\n      90deg,\n      #ef4e38 0%,\n      #b61e0e 100%);\n  animation: pulse 2s infinite;\n}\n:host section .row .widget.quick-actions .actions-grid .action-button.primary:hover {\n  opacity: 0.9;\n  transform: scale(1.03);\n}\n:host section .row .widget.quick-actions .actions-grid .action-button img {\n  width: 16px;\n  height: 16px;\n}\n:host section .row .widget.empty-widget {\n  padding: 24px;\n  animation-delay: 0.3s;\n}\n:host section .row .widget.empty-widget:nth-of-type(odd) {\n  animation-delay: 0.3s;\n}\n:host section .row .widget.empty-widget:nth-of-type(even) {\n  animation-delay: 0.35s;\n}\n:host section .row .widget.empty-widget .widget-head h3 {\n  font-size: 18px;\n  font-weight: 400;\n  margin: 0;\n}\n/*# sourceMappingURL=dashboard.component.css.map */\n"] }]
  }], null, { progressFills: [{
    type: ViewChildren,
    args: ["progressFill"]
  }], storageValue: [{
    type: ViewChild,
    args: ["storageValue"]
  }], storagePercentage: [{
    type: ViewChild,
    args: ["storagePercentage"]
  }], backupValue: [{
    type: ViewChild,
    args: ["backupValue"]
  }], recoveryValue: [{
    type: ViewChild,
    args: ["recoveryValue"]
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DashboardComponent, { className: "DashboardComponent", filePath: "renderer/src/app/dashboard/dashboard.component.ts", lineNumber: 10 });
})();
export {
  DashboardComponent
};
//# sourceMappingURL=chunk-4CKILKBC.js.map
