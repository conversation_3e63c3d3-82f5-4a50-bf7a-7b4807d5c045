@import '../../styles/variables.less';
@import '../../styles/mixins.less';

:host {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    height: 100%;
    padding: 1rem;
    overflow: hidden;

    // Header Section
    header {
        display: flex;
        justify-content: space-between;

        .animation(fade-in-up);

        .title {
            h1 {
                font-size: 1.5rem;
                font-weight: 700;
                margin: 0 0 .5rem 0;
                color: @text-color;
            }

            p {
                font-size: 1rem;
                color: @text-secondary;
                margin: 0;
            }
        }
    }

    // Main Content Container
    section {
        flex: 1;
        display: flex;
        gap: 1rem;
        width: 100%;
        min-width: 0;
        padding: 1rem;
        overflow: hidden;
        border-radius: @border-radius;
        opacity: 0;
        background: rgba(255, 255, 255, 0.1);

        .animation(fade-in-up, 50ms);

        // Navigation panel
        nav {
            display: flex;
            flex-direction: column;
            width: 14rem;
            gap: .5rem;
            flex-shrink: 0;
            overflow-y: auto;

            /* Hide scrollbar for webkit browsers */
            &::-webkit-scrollbar {
                display: none;
            }

            .item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 4px 8px;
                border-radius: @border-radius-small;
                color: @text-color;
                text-decoration: none;
                font-family: @font-family;
                font-weight: 500;
                font-size: 14px;
                line-height: 1.36;
                width: 100%;
                border: 1px solid transparent;
                opacity: 0;

                .transition(background, border, box-shadow);
                .animation(fade-in-left, calc(100ms + var(--nav-index, 0) * 25ms));

                fa-icon {
                    width: 16px;
                    height: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                    font-size: 16px;
                }

                span {
                    flex: 1;
                    text-align: left;
                    white-space: nowrap;
                }

                &:hover:not(.active) {
                    background: rgba(255, 255, 255, 0.1);
                }

                &.active {
                    background: rgba(16, 185, 129, 0.2);
                    border: 1px solid rgba(16, 185, 129, 0.5);
                    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
                }
            }

            .divider {
                width: 100%;
                height: 31px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                margin: 0;
                padding: 0;
                display: flex;
                align-items: center;
                opacity: 0;

                .animation(fade-in-left, calc(100ms + var(--nav-index, 0) * 25ms));

                span {
                    font-family: @font-family;
                    font-weight: 400;
                    font-size: 16px;
                    line-height: 1.36;
                    color: @text-color;
                }
            }
        }

        // Right content panel
        .main {
            flex: 1;
            overflow: hidden;
        }
    }
}
