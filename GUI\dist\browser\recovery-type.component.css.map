{"version": 3, "sources": ["renderer/src/app/recovery/shared/recovery-type/recovery-type.component.less", "renderer/src/styles/mixins.less"], "sourcesContent": ["@import '../../../../styles/variables.less';\n@import '../../../../styles/mixins.less';\n\n:host {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 0.75rem;\n    overflow: hidden;\n    cursor: pointer;\n    padding: 1rem;\n\n    // Panel Header\n    header {\n        flex: 0 0 auto;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        gap: 1rem;\n        padding-bottom: 1rem;\n\n        h2 {\n            font-size: 1.125rem;\n            font-weight: 600;\n            margin: 0;\n            line-height: 1.125rem;\n            color: @text-color;\n        }\n\n        .icon-container {\n            width: 20px;\n            height: 20px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            flex-shrink: 0;\n            border-radius: 6px;\n            background: rgba(255, 255, 255, 0);\n            padding: 0;\n        }\n    }\n\n    // Collapsed vertical panel styling\n    &.collapsed {\n        header {\n            writing-mode: vertical-lr;\n            text-orientation: mixed;\n            padding: 0;\n            height: 100%;\n            flex-direction: row-reverse;\n            justify-content: flex-end;\n\n            h2 {\n                font-size: 1rem;\n                font-weight: 600;\n                line-height: 1.2;\n                text-align: center;\n                margin: 0;\n                writing-mode: vertical-lr;\n                text-orientation: mixed;\n                color: @text-color;\n                letter-spacing: -0.02em;\n                transform: rotate(180deg);\n            }\n\n            .icon-container {\n                width: 20px;\n                height: 20px;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                flex-shrink: 0;\n                border-radius: 6px;\n                background: rgba(255, 255, 255, 0);\n                padding: 0;\n            }\n        }\n\n        section {\n            display: none;\n        }\n    }\n\n    section {\n        flex: 1;\n        overflow-y: auto;\n        overflow-x: hidden;\n        display: grid;\n        grid-template-columns: repeat(2, 1fr);\n        gap: 1rem;\n\n        // Hide scrollbar for Chromium/WebKit (Electron)\n        &::-webkit-scrollbar {\n            display: none;\n        }\n\n        .item {\n            display: flex;\n            align-items: center;\n            gap: 1rem;\n            padding: 1rem;\n            background: rgba(255, 255, 255, 0.05);\n            border: 1px solid rgba(255, 255, 255, 0.1);\n            border-radius: 0.5rem;\n            cursor: pointer;\n            overflow: hidden;\n\n            .transition(background, border-color, box-shadow);\n\n            &:hover {\n                background: rgba(255, 255, 255, 0.08);\n                border-color: rgba(255, 255, 255, 0.2);\n            }\n\n            &.selected {\n                background: rgba(255, 255, 255, 0.1);\n                border-color: @primary-color;\n                box-shadow: 0 0 0 2px rgba(239, 78, 56, 0.3);\n\n                .item-icon {\n                    color: @primary-color;\n                }\n            }\n\n            .item-icon {\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                width: 3.25rem;\n                height: 2.75rem;\n                font-size: 3rem;\n                color: @text-secondary;\n\n                .transition(color);\n            }\n\n            .item-content {\n                flex: 1;\n\n                h3 {\n                    font-size: 1rem;\n                    font-weight: 500;\n                    color: @text-color;\n                    margin: 0 0 0.5rem 0;\n                    line-height: 1.3;\n                }\n\n                p {\n                    font-size: 0.8125rem;\n                    color: @text-secondary;\n                    margin: 0;\n                    line-height: 1.4;\n                }\n            }\n        }\n    }\n}\n", "/**\n * Mixin\n * <AUTHOR>\n */\n\n@import \"variables.less\";\n\n// Transition\n.transition(...) {\n    & when (length(@arguments) = 0) {\n        transition: none;\n    }\n\n    & when (length(@arguments) = 1) {\n        transition: @arguments 300ms ease;\n    }\n\n    & when (length(@arguments) > 1) {\n        @properties: replace(\"@{arguments}\", \" \", \", \", \"g\");\n        transition-property: ~\"@{properties}\";\n        transition-timing-function: ease;\n        transition-duration: 300ms;\n    }\n}\n\n// Animations\n.animation(@name, @delay: 0ms) {\n    animation-duration: 500ms;\n    animation-fill-mode: forwards;\n    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n    animation-delay: @delay;\n\n    & when (@name = fade-in-up) {\n        animation-name: fadeInUp;\n    }\n\n    & when (@name = fade-in-left) {\n        animation-name: fadeInLeft;\n    }\n\n    & when (@name = fade-in) {\n        animation-name: fadeIn;\n    }\n}\n"], "mappings": ";AAGA;AACI,WAAA;AACA,kBAAA;AACA,UAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,YAAA;AACA,UAAA;AACA,WAAA;;AARJ,MAWI;AACI,QAAA,EAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,kBAAA;;AAjBR,MAWI,OAQI;AACI,aAAA;AACA,eAAA;AACA,UAAA;AACA,eAAA;AACA,SAAA;;AAxBZ,MAWI,OAgBI,CAAA;AACI,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;;AAKR,KAAC,CAAA,UACG;AACI,gBAAA;AACA,oBAAA;AACA,WAAA;AACA,UAAA;AACA,kBAAA;AACA,mBAAA;;AAPR,KAAC,CAAA,UACG,OAQI;AACI,aAAA;AACA,eAAA;AACA,eAAA;AACA,cAAA;AACA,UAAA;AACA,gBAAA;AACA,oBAAA;AACA,SAAA;AACA,kBAAA;AACA,aAAW,OAAA;;AAnBvB,KAAC,CAAA,UACG,OAqBI,CApCJ;AAqCQ,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;;AA/BZ,KAAC,CAAA,UAmCG;AACI,WAAA;;AA7EZ,MAiFI;AACI,QAAA;AACA,cAAA;AACA,cAAA;AACA,WAAA;AACA,yBAAuB,OAAA,CAAA,EAAA;AACvB,OAAA;;AAGA,MATJ,OASK;AACG,WAAA;;AA3FZ,MAiFI,QAaI,CAAA;AACI,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,UAAA;AACA,YAAA;ACvFJ;IAAA,UAAA;IAAA,YAAA;IAAA;AACA,8BAAA;AACA,uBAAA;;ADyFI,MA1BR,QAaI,CAAA,IAaK;AACG,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,gBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,MA/BR,QAaI,CAAA,IAkBK,CAAA;AACG,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,gBAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,KAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA;;AAHJ,MA/BR,QAaI,CAAA,IAkBK,CAAA,SAKG,CAAA;AACI,SAAA;;AAtHpB,MAiFI,QAaI,CAAA,KA4BI,CALI;AAMA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,SAAA;AACA,UAAA;AACA,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;ACtHR,cAAA,MAAA,MAAA;;ADXR,MAiFI,QAaI,CAAA,KAwCI,CAAA;AACI,QAAA;;AAvIhB,MAiFI,QAaI,CAAA,KAwCI,CAAA,aAGI;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA,EAAA,EAAA,OAAA;AACA,eAAA;;AA9IpB,MAiFI,QAaI,CAAA,KAwCI,CAAA,aAWI;AACI,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;AACA,eAAA;;", "names": []}