import { ApplicationConfig, inject, provideAppInitializer, provideZonelessChangeDetection } from '@angular/core';
import { provideRouter, withHashLocation } from '@angular/router';
import { provideAnimations } from '@angular/platform-browser/animations';
import { FaConfig, FaIconLibrary } from '@fortawesome/angular-fontawesome';

import { routes } from './app.routes';
import { icons } from './app.icons';

export const appConfig: ApplicationConfig = {
    providers: [
        provideZonelessChangeDetection(),
        provideRouter(routes, withHashLocation()),
        provideAnimations(),
        provideAppInitializer(initIcons),
    ],
};

/**
 * Initializes the FontAwesome icons library with the specified icons.
 * This function is called during the application initialization phase.
 */
function initIcons() {
    const config = inject(FaConfig);
    const library = inject(FaIconLibrary);

    config.fixedWidth = true; // Set fixed width for all icons by default
    config.defaultPrefix = 'fas'; // Set default prefix for icons
    library.addIcons(...icons);
}
