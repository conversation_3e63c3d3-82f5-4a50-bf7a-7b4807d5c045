@import '../../styles/variables.less';

:host {
    display: block;
    overflow-y: auto;
    height: 100%;

    section {
        padding: 1rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .welcome {
            animation: fadeInUp 0.5s cubic-bezier(0.215, 0.61, 0.355, 1) forwards;
            animation-delay: 0.02s;
            opacity: 0;
            will-change: transform, opacity;

            h1 {
                font-size: 1.5rem;
                font-weight: 700;
                margin: 0 0 .5rem 0;
            }

            p {
                font-size: 1rem;
                color: @text-secondary;
                margin: 0;
            }
        }

        // Modify widgets-row to use grid layout
        .row {
            display: grid;
            grid-template-columns: repeat(6, 1fr); // Create a 6-column grid
            gap: 1rem; // Unified spacing
            width: 100%;

            .widget {
                background-color: @bg-overlay;
                border-radius: @border-radius;
                transition: box-shadow 0.3s ease, background-color 0.3s ease;
                animation: fadeInUp .5s cubic-bezier(0.215, 0.61, 0.355, 1) forwards;
                position: relative;
                overflow: hidden;
                opacity: 0; // Ensure initial state is completely invisible

                &:hover {
                    box-shadow: 0 .5rem 1.25rem rgba(0, 0, 0, 0.2);
                    background-color: rgba(255, 255, 255, 0.15);
                }

                // First three elements each occupy 2 columns width
                &.storage,
                &.backup,
                &.recovery {
                    grid-column: span 2; // Occupy 2 columns width
                }

                // Last four elements each occupy 3 columns width
                &.recent-backups,
                &.quick-actions,
                &.empty-widget {
                    grid-column: span 3; // Occupy 3 columns width
                }

                &.storage {
                    padding: 1rem;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    animation-delay: 0.05s;

                    .widget-header {
                        display: flex;
                        flex-direction: column;
                        gap: 12px;

                        .widget-title {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;

                            span {
                                font-size: 18px;
                            }

                            .icon-container {
                                width: 17.5px;
                                height: 20px;
                                display: flex;
                                align-items: center;
                                justify-content: flex-end;

                                img {
                                    fill: #36AB6B;
                                }
                            }
                        }

                        .widget-value {
                            display: flex;
                            align-items: center;

                            .primary-value {
                                font-size: 30px;
                                font-weight: 700;
                                margin-right: 12px;
                                position: relative;

                                &.animated {
                                    opacity: 0;
                                    animation: fadeIn 0.4s cubic-bezier(0.215, 0.61, 0.355, 1) forwards 0.15s;
                                    will-change: opacity;
                                }
                            }

                            .secondary-value {
                                font-size: 16px;
                                color: @text-secondary;
                                position: relative;

                                &.animated {
                                    opacity: 0;
                                    animation: fadeIn 0.4s cubic-bezier(0.215, 0.61, 0.355, 1) forwards 0.2s;
                                    will-change: opacity;
                                }
                            }
                        }

                        .progress-bar {
                            height: 8px;
                            background-color: @bg-overlay;
                            border-radius: 9999px;
                            overflow: hidden;

                            .progress-fill {
                                height: 100%;
                                background: linear-gradient(90deg, #E73820 0%, #C32615 100%);
                                border-radius: 9999px;
                                width: 0;
                                animation: progressAnimate 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards 0.1s;
                                will-change: width;
                            }
                        }
                    }
                }

                &.backup,
                &.recovery {
                    padding: 1rem;

                    &.backup {
                        animation-delay: 0.1s;
                    }

                    &.recovery {
                        animation-delay: 0.15s;
                    }

                    .widget-content {
                        display: flex;
                        flex-direction: column;
                        gap: 12px;

                        .widget-header {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;

                            span {
                                font-size: 18px;
                            }

                            .icon-container {
                                width: 20px;
                                height: 20px;
                                display: flex;
                                align-items: center;
                                justify-content: center;

                                &.green img {
                                    fill: @success-color;
                                }
                            }
                        }

                        .widget-value {
                            font-size: 30px;
                            font-weight: 700;
                            line-height: 1.21;
                            margin: 0;

                            &.animated {
                                opacity: 0;
                                animation: countUp 0.5s cubic-bezier(0.215, 0.61, 0.355, 1) forwards 0.15s;
                                position: relative;
                                will-change: transform, opacity;
                            }
                        }

                        .widget-info {
                            font-size: 14px;
                            color: @text-secondary;
                            margin: 0;
                        }

                        .widget-footer {
                            display: flex;
                            align-items: center;
                            gap: 5px;

                            .trend-icon {
                                width: 10.5px;
                                height: 14px;
                                display: flex;
                                justify-content: center;
                                align-items: center;

                                img {
                                    fill: @success-color;
                                }
                            }

                            .trend-text {
                                font-size: 14px;
                                color: @success-color;
                            }
                        }
                    }
                }

                &.recent-backups,
                &.quick-actions {
                    padding: 24px;
                    display: flex;
                    flex-direction: column;
                    gap: 24px;

                    &.recent-backups {
                        animation-delay: 0.2s;
                    }

                    &.quick-actions {
                        animation-delay: 0.25s;
                    }

                    .widget-head {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        h3 {
                            font-size: 18px;
                            font-weight: 400;
                            margin: 0;
                        }

                        .view-all {
                            background: transparent;
                            border: none;
                            color: @text-secondary;
                            font-size: 14px;
                            cursor: pointer;
                            padding: 0;

                            &:hover {
                                color: @text-color;
                                text-decoration: underline;
                            }
                        }
                    }
                }

                &.recent-backups {
                    .backups-list {
                        display: flex;
                        flex-direction: column;
                        gap: 10px;

                        .backup-item {
                            background-color: @light-overlay;
                            border-radius: @border-radius-small;
                            padding: 12px 16px;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            transition: all 0.3s ease;
                            cursor: pointer;

                            &:hover {
                                background-color: rgba(255, 255, 255, 0.15);
                                transform: translateX(5px);
                            }

                            .backup-info {
                                display: flex;
                                align-items: center;
                                gap: 12px;

                                .backup-icon {
                                    width: 14px;
                                    height: 16px;
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;

                                    img {
                                        width: 100%;
                                        height: 100%;
                                    }
                                }

                                .backup-details {
                                    h4 {
                                        font-size: 16px;
                                        font-weight: 500;
                                        margin: 0 0 4px 0;
                                    }

                                    span {
                                        font-size: 14px;
                                        color: @text-secondary;
                                    }
                                }
                            }

                            .status-badge {
                                padding: 4px 10px;
                                border-radius: 9999px;
                                font-size: 14px;

                                &.success {
                                    background-color: @success-bg;
                                    color: @success-color;
                                }
                            }
                        }
                    }
                }

                &.quick-actions {
                    .actions-grid {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 16px;

                        .action-button {
                            flex: 1 0 calc(50% - 8px);
                            min-width: 0;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 10px;
                            padding: 10px 16px;
                            height: 52px; // Fixed button height
                            border-radius: @border-radius;
                            background-color: @bg-overlay;
                            border: none;
                            color: @text-color;
                            font-size: 16px;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            position: relative;
                            overflow: hidden;

                            &:hover {
                                background-color: rgba(255, 255, 255, 0.2);
                                transform: scale(1.03);
                            }

                            &.primary {
                                background: @primary-gradient;
                                animation: pulse 2s infinite;

                                &:hover {
                                    opacity: 0.9;
                                    transform: scale(1.03);
                                }
                            }

                            img {
                                width: 16px;
                                height: 16px;
                            }
                        }
                    }
                }

                &.empty-widget {
                    padding: 24px;
                    animation-delay: 0.3s;

                    &:nth-of-type(odd) {
                        animation-delay: 0.3s;
                    }

                    &:nth-of-type(even) {
                        animation-delay: 0.35s;
                    }

                    .widget-head {
                        h3 {
                            font-size: 18px;
                            font-weight: 400;
                            margin: 0;
                        }
                    }
                }
            }
        }
    }
}
