{"version": 3, "sources": ["renderer/src/app/backup/shared/options/options.component.less"], "sourcesContent": [":host {\n    display: grid;\n    grid-template-columns: 1fr;\n    grid-template-rows: repeat(6, 1fr);\n    gap: .5rem;\n    width: 5rem;\n    height: 100%;\n    opacity: 0;\n    background: transparent;\n\n    .item {\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        gap: .5rem;\n        padding: .75rem;\n        width: 5rem;\n        height: 100%;\n        background: rgba(255, 255, 255, 0.1);\n        border: 1px solid transparent;\n        border-radius: .75rem;\n        cursor: pointer;\n        transition: all 0.2s ease;\n\n        &:hover {\n            background: rgba(255, 255, 255, 0.15);\n            border-color: rgba(255, 255, 255, 0.2);\n        }\n\n        &.selected {\n            background: rgba(16, 185, 129, 0.2);\n            border-color: rgba(16, 185, 129, 0.6);\n            box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n        }\n\n        .text {\n            font-size: 0.625rem;\n            font-weight: 500;\n            color: rgba(255, 255, 255, 0.9);\n            text-align: center;\n            line-height: 1.2;\n        }\n    }\n}\n"], "mappings": ";AAAA;AACI,WAAA;AACA,yBAAA;AACA,sBAAoB,OAAA,CAAA,EAAA;AACpB,OAAA;AACA,SAAA;AACA,UAAA;AACA,WAAA;AACA,cAAA;;AARJ,MAUI,CAAA;AACI,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,UAAA;AACA,cAAA,IAAA,KAAA;;AAEA,MAfJ,CAAA,IAeK;AACG,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,gBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,MApBJ,CAAA,IAoBK,CAAA;AACG,cAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,gBAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAjCZ,MAUI,CAAA,KA0BI,CAAA;AACI,aAAA;AACA,eAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA;AACA,eAAA;;", "names": []}