{"version": 3, "sources": ["renderer/src/app/preferences/preferences.component.less", "renderer/src/styles/mixins.less", "renderer/src/styles/variables.less"], "sourcesContent": ["@import '../../styles/variables.less';\n@import '../../styles/mixins.less';\n\n:host {\n    display: flex;\n    flex-direction: column;\n    gap: 1rem;\n    height: 100%;\n    padding: 1rem;\n    overflow: hidden;\n\n    // Header Section\n    header {\n        display: flex;\n        justify-content: space-between;\n\n        .animation(fade-in-up);\n\n        .title {\n            h1 {\n                font-size: 1.5rem;\n                font-weight: 700;\n                margin: 0 0 .5rem 0;\n                color: @text-color;\n            }\n\n            p {\n                font-size: 1rem;\n                color: @text-secondary;\n                margin: 0;\n            }\n        }\n    }\n\n    // Main Content Container\n    section {\n        flex: 1;\n        display: flex;\n        gap: 1rem;\n        width: 100%;\n        min-width: 0;\n        padding: 1rem;\n        overflow: hidden;\n        border-radius: @border-radius;\n        opacity: 0;\n        background: rgba(255, 255, 255, 0.1);\n\n        .animation(fade-in-up, 50ms);\n\n        // Navigation panel\n        nav {\n            display: flex;\n            flex-direction: column;\n            width: 14rem;\n            gap: .5rem;\n            flex-shrink: 0;\n            overflow-y: auto;\n\n            /* Hide scrollbar for webkit browsers */\n            &::-webkit-scrollbar {\n                display: none;\n            }\n\n            .item {\n                display: flex;\n                align-items: center;\n                gap: 8px;\n                padding: 4px 8px;\n                border-radius: @border-radius-small;\n                color: @text-color;\n                text-decoration: none;\n                font-family: @font-family;\n                font-weight: 500;\n                font-size: 14px;\n                line-height: 1.36;\n                width: 100%;\n                border: 1px solid transparent;\n                opacity: 0;\n\n                .transition(background, border, box-shadow);\n                .animation(fade-in-left, calc(100ms + var(--nav-index, 0) * 25ms));\n\n                fa-icon {\n                    width: 16px;\n                    height: 16px;\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    flex-shrink: 0;\n                    font-size: 16px;\n                }\n\n                span {\n                    flex: 1;\n                    text-align: left;\n                    white-space: nowrap;\n                }\n\n                &:hover:not(.active) {\n                    background: rgba(255, 255, 255, 0.1);\n                }\n\n                &.active {\n                    background: rgba(16, 185, 129, 0.2);\n                    border: 1px solid rgba(16, 185, 129, 0.5);\n                    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n                }\n            }\n\n            .divider {\n                width: 100%;\n                height: 31px;\n                border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n                margin: 0;\n                padding: 0;\n                display: flex;\n                align-items: center;\n                opacity: 0;\n\n                .animation(fade-in-left, calc(100ms + var(--nav-index, 0) * 25ms));\n\n                span {\n                    font-family: @font-family;\n                    font-weight: 400;\n                    font-size: 16px;\n                    line-height: 1.36;\n                    color: @text-color;\n                }\n            }\n        }\n\n        // Right content panel\n        .main {\n            flex: 1;\n            overflow: hidden;\n        }\n    }\n}\n", "/**\n * Mixin\n * <AUTHOR>\n */\n\n@import \"variables.less\";\n\n// Transition\n.transition(...) {\n    & when (length(@arguments) = 0) {\n        transition: none;\n    }\n\n    & when (length(@arguments) = 1) {\n        transition: @arguments 300ms ease;\n    }\n\n    & when (length(@arguments) > 1) {\n        @properties: replace(\"@{arguments}\", \" \", \", \", \"g\");\n        transition-property: ~\"@{properties}\";\n        transition-timing-function: ease;\n        transition-duration: 300ms;\n    }\n}\n\n// Animations\n.animation(@name, @delay: 0ms) {\n    animation-duration: 500ms;\n    animation-fill-mode: forwards;\n    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n    animation-delay: @delay;\n\n    & when (@name = fade-in-up) {\n        animation-name: fadeInUp;\n    }\n\n    & when (@name = fade-in-left) {\n        animation-name: fadeInLeft;\n    }\n\n    & when (@name = fade-in) {\n        animation-name: fadeIn;\n    }\n}\n", "// variables\r\n@primary-color: #ef4e38;\r\n@primary-gradient: linear-gradient(90deg, #ef4e38 0%, #b61e0e 100%);\r\n@success-color: #34d399;\r\n@success-bg: rgba(16, 185, 129, 0.2);\r\n@dark-bg: linear-gradient(284deg, #152b34 0%, #254b5c 100%);\r\n@background-dark: #1a2a33;\r\n@bg-overlay: rgba(255, 255, 255, 0.1);\r\n@light-overlay: rgba(255, 255, 255, 0.05);\r\n@border-color: rgba(255, 255, 255, 0.1);\r\n@text-color: rgba(255, 255, 255, 1);\r\n@text-secondary: rgba(255, 255, 255, 0.5);\r\n@border-radius: .75rem;\r\n@border-radius-small: .5rem;\r\n@font-family: 'inter', 'noto sans', sans-serif;\r\n"], "mappings": ";AAGA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;AACA,UAAA;AACA,WAAA;AACA,YAAA;;AANJ,MASI;AACI,WAAA;AACA,mBAAA;ACaJ,sBAAA;AACA,uBAAA;AACA,6BAA2B,aAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA;AAC3B,mBAAA;AAGI,kBAAA;;AD9BR,MASI,OAMI,CAAA,MACI;AACI,aAAA;AACA,eAAA;AACA,UAAA,EAAA,EAAA,OAAA;AACA,SAAA;;AApBhB,MASI,OAMI,CAAA,MAQI;AACI,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;;AA1BhB,MAgCI;AACI,QAAA;AACA,WAAA;AACA,OAAA;AACA,SAAA;AACA,aAAA;AACA,WAAA;AACA,YAAA;AACA,iBAAA;AACA,WAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AClBJ,sBAAA;AACA,uBAAA;AACA,6BAA2B,aAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA;AAC3B,mBAAA;AAGI,kBAAA;;AD9BR,MAgCI,QAeI;AACI,WAAA;AACA,kBAAA;AACA,SAAA;AACA,OAAA;AACA,eAAA;AACA,cAAA;;AAGA,MAxBR,QAeI,GASK;AACG,WAAA;;AAzDhB,MAgCI,QAeI,IAaI,CAAA;AACI,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,SAAA;AACA,mBAAA;AACA;IEzDhB,OAAA;IAAS,WAAA;IAAA;AF0DO,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA,IAAA,MAAA;AACA,WAAA;AC1DR;IAAA,UAAA;IAAA,MAAA;IAAA;AACA,8BAAA;AACA,uBAAA;AAMJ,sBAAA;AACA,uBAAA;AACA,6BAA2B,aAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA;AAC3B,mBDkDqC,KAAA,MAAA,EAAa,IAAA,WAAA,EAAA,GAAA,EAAA;AC3C9C,kBAAA;;ADlCR,MAgCI,QAeI,IAaI,CAAA,KAmBI;AACI,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,eAAA;AACA,aAAA;;AAtFpB,MAgCI,QAeI,IAaI,CAAA,KA6BI;AACI,QAAA;AACA,cAAA;AACA,eAAA;;AAGJ,MA/DZ,QAeI,IAaI,CAAA,IAmCK,MAAM,KAAI,CAAA;AACP,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,MAnEZ,QAeI,IAaI,CAAA,IAuCK,CAJU;AAKP,cAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA,IAAA,MAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAtGpB,MAgCI,QAeI,IA2DI,CAAA;AACI,SAAA;AACA,UAAA;AACA,iBAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;AACA,WAAA;AACA,WAAA;AACA,eAAA;AACA,WAAA;AC1FZ,sBAAA;AACA,uBAAA;AACA,6BAA2B,aAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA;AAC3B,mBDyFqC,KAAA,MAAA,EAAa,IAAA,WAAA,EAAA,GAAA,EAAA;AClF9C,kBAAA;;ADlCR,MAgCI,QAeI,IA2DI,CAAA,QAYI;AACI;IE5GpB,OAAA;IAAS,WAAA;IAAA;AF6GW,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AA3HpB,MAgCI,QAiGI,CAAA;AACI,QAAA;AACA,YAAA;;", "names": []}