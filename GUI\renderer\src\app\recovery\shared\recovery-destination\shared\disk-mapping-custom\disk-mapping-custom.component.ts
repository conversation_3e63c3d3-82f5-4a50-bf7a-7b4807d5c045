import { Component, computed, effect, input, OnInit, output, signal } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { CommonModule } from '@angular/common';
import { DiskInfo, PartitionInfo } from '../disk-mapping/disk-mapping.component';
import { CdkDrag, CdkDragDrop, CdkDropList, DragDropConfig, DragDropModule, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';

@Component({
    selector: 'app-disk-mapping-custom',
    templateUrl: './disk-mapping-custom.component.html',
    styleUrl: './disk-mapping-custom.component.less',
    imports: [FontAwesomeModule, CommonModule, DragDropModule],
})
export class DiskMappingCustomComponent implements OnInit {
    // Input properties
    sourceDisks = input.required<DiskInfo[]>();
    targetDisks = input.required<DiskInfo[]>();

    targets = signal<DiskInfo[]>([]);

    // Output events
    resetMapping = output<void>();
    moreOptions = output<string>();

    ngOnInit(): void {
        this.targets.set(this.targetDisks().map(disk => {
            disk.partitions = [];
            return disk;
        }));
    }

    // Handle reset mapping
    onResetMapping(): void {
        this.resetMapping.emit();
    }

    // Handle more options for partition
    onMoreOptions(partitionId: string): void {
        this.moreOptions.emit(partitionId);
    }

    customEnterPredicate(drag: CdkDrag<PartitionInfo>, drop: CdkDropList<PartitionInfo[]>): boolean {
        // Allow dropping only if the dragged item is a partition
        return drop.data.every(partition => partition.id !== drag.data.id);
    }

    onPartitionDrop(event: CdkDragDrop<PartitionInfo[], PartitionInfo[], PartitionInfo>): void {

        this.targets.update(targets => {
            targets.forEach(disk => {
                disk.partitions.push(event.item.data);
            });

            return targets;
        });
    }
}
