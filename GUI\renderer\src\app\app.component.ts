import { Component, OnInit, Signal } from '@angular/core';
import { Router, NavigationEnd, RouterModule } from '@angular/router';
import { filter, map, startWith } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { trigger, style, transition, animate } from '@angular/animations';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { SidebarService } from '../services/sidebar.service';

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrl: './app.component.less',
    imports: [
        RouterModule,
        AsyncPipe,
        FontAwesomeModule,
    ],
    animations: [
        trigger('slideUpDown', [
            transition(':enter', [
                style({
                    height: '0rem',
                    minHeight: '0rem',
                    opacity: 0,
                    overflow: 'hidden'
                }),
                animate('300ms ease-out', style({
                    height: '3.75rem',
                    opacity: 1,
                    overflow: 'hidden'
                }))
            ]),
            transition(':leave', [
                style({
                    height: '3.75rem',
                    opacity: 1,
                    overflow: 'hidden'
                }),
                animate('300ms ease-in', style({
                    height: '0rem',
                    minHeight: '0rem',
                    opacity: 0,
                    overflow: 'hidden'
                }))
            ])
        ])
    ]
})
export class AppComponent implements OnInit {
    isDashboard$!: Observable<boolean>;
    isCollapsed: Signal<boolean>;

    constructor(
        private router: Router,
        private Sidebar: SidebarService
    ) {
        // Initialize the sidebar collapsed state from the service
        this.isCollapsed = this.Sidebar.isCollapsed;
    }

    ngOnInit() {
        this.isDashboard$ = this.router.events.pipe(
            filter(event => event instanceof NavigationEnd),
            map((event: NavigationEnd) => event.url === '/' || event.url.startsWith('/dashboard')),
            startWith(this.router.url === '/' || this.router.url.startsWith('/dashboard'))
        );
    }

    toggleSidebar() {
        this.Sidebar.toggle();
    }
}
