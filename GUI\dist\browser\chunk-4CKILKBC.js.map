{"version": 3, "sources": ["renderer/src/app/dashboard/dashboard.component.ts", "renderer/src/app/dashboard/dashboard.component.html"], "sourcesContent": ["import { Component, AfterViewInit, ElementRef, ViewChildren, QueryList, ViewChild } from '@angular/core';\r\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\n\r\n@Component({\r\n    selector: 'app-dashboard',\r\n    imports: [FontAwesomeModule],\r\n    templateUrl: './dashboard.component.html',\r\n    styleUrl: './dashboard.component.less'\r\n})\r\nexport class DashboardComponent {\r\n    @ViewChildren('progressFill') progressFills!: QueryList<ElementRef>;\r\n    @ViewChild('storageValue') storageValue!: ElementRef;\r\n    @ViewChild('storagePercentage') storagePercentage!: ElementRef;\r\n    @ViewChild('backupValue') backupValue!: ElementRef;\r\n    @ViewChild('recoveryValue') recoveryValue!: ElementRef;\r\n\r\n    ngAfterViewInit() {\r\n        // Set progress bar width style variable\r\n        setTimeout(() => {\r\n            this.progressFills.forEach(element => {\r\n                const el = element.nativeElement;\r\n                const width = el.style.width;\r\n                if (width) {\r\n                    el.style.setProperty('--progress-width', width);\r\n                    el.style.width = 'var(--progress-width)';\r\n                }\r\n            });\r\n\r\n            // Add number animation with shorter duration\r\n            this.animateCounter(this.backupValue.nativeElement, 0, 147, 700);\r\n            this.animateCounter(this.recoveryValue.nativeElement, 0, 1234, 700);\r\n        }, 300);\r\n    }\r\n\r\n    // Number counter animation - optimized for faster display\r\n    animateCounter(element: HTMLElement, start: number, end: number, duration: number) {\r\n        // For large numbers, reduce steps to speed up animation\r\n        const steps = Math.min(30, end - start);\r\n        const stepValue = (end - start) / steps;\r\n        const stepTime = duration / steps;\r\n        let current = start;\r\n        let step = 0;\r\n\r\n        // Format numbers with thousands separator\r\n        const formatNumber = (num: number) => {\r\n            return Math.round(num).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n        };\r\n\r\n        const timer = setInterval(() => {\r\n            step++;\r\n\r\n            // Use non-linear growth, slower at start and faster at end\r\n            current = start + stepValue * step;\r\n\r\n            // Ensure final value is displayed on last step\r\n            if (step >= steps) {\r\n                current = end;\r\n                clearInterval(timer);\r\n            }\r\n\r\n            element.textContent = formatNumber(current);\r\n        }, stepTime);\r\n    }\r\n}\r\n", "<section>\r\n    <div class=\"welcome\">\r\n        <h1>Welcome back, Admin</h1>\r\n        <p>Here's what's happening with your backups today.</p>\r\n    </div>\r\n    <!-- Place all widgets in a unified grid container -->\r\n    <div class=\"row\">\r\n        <!-- First three widgets each occupy 2 grid columns -->\r\n        <div class=\"widget storage\">\r\n            <div class=\"widget-header\">\r\n                <div class=\"widget-title\">\r\n                    <span>Storage Usage</span>\r\n                    <div class=\"icon-container\">\r\n                        <fa-icon icon=\"hdd\"></fa-icon>\r\n                    </div>\r\n                </div>\r\n                <div class=\"widget-value\">\r\n                    <span #storageValue class=\"primary-value animated\">2.4 TB</span>\r\n                    <span #storagePercentage class=\"secondary-value animated\">48%</span>\r\n                </div>\r\n                <div class=\"progress-bar\">\r\n                    <div #progressFill class=\"progress-fill\" style=\"width: 48%\"></div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"widget backup\">\r\n            <div class=\"widget-content\">\r\n                <div class=\"widget-header\">\r\n                    <span>Active Backups</span>\r\n                    <div class=\"icon-container green\">\r\n                        <fa-icon icon=\"box-archive\"></fa-icon>\r\n                    </div>\r\n                </div>\r\n                <p #backupValue class=\"widget-value animated\">147</p>\r\n                <div class=\"widget-footer\">\r\n                    <div class=\"trend-icon\">\r\n                        <fa-icon icon=\"arrow-up\"></fa-icon>\r\n                    </div>\r\n                    <span class=\"trend-text\">12% from last week</span>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"widget recovery\">\r\n            <div class=\"widget-content\">\r\n                <div class=\"widget-header\">\r\n                    <span>Recovery Points</span>\r\n                    <div class=\"icon-container\">\r\n                        <fa-icon icon=\"clock-rotate-left\"></fa-icon>\r\n                    </div>\r\n                </div>\r\n                <p #recoveryValue class=\"widget-value animated\">1,234</p>\r\n                <p class=\"widget-info\">Last point: 5 mins ago</p>\r\n            </div>\r\n        </div>\r\n        <!-- Last four widgets each occupy 3 grid columns -->\r\n        <!-- TODO: Extract into recent-backups component -->\r\n        <div class=\"widget recent-backups\">\r\n            <div class=\"widget-head\">\r\n                <h3>Recent Backups</h3>\r\n                <button class=\"view-all\">View All</button>\r\n            </div>\r\n            <div class=\"backups-list\">\r\n                <div class=\"backup-item\">\r\n                    <div class=\"backup-info\">\r\n                        <div class=\"backup-icon\">\r\n                            <fa-icon icon=\"database\"></fa-icon>\r\n                        </div>\r\n                        <div class=\"backup-details\">\r\n                            <h4>Production DB</h4>\r\n                            <span>2.1 GB • 5 mins ago</span>\r\n                        </div>\r\n                    </div>\r\n                    <span class=\"status-badge success\">Success</span>\r\n                </div>\r\n                <div class=\"backup-item\">\r\n                    <div class=\"backup-info\">\r\n                        <div class=\"backup-icon\">\r\n                            <fa-icon icon=\"file\"></fa-icon>\r\n                        </div>\r\n                        <div class=\"backup-details\">\r\n                            <h4>User Files</h4>\r\n                            <span>5.4 GB • 15 mins ago</span>\r\n                        </div>\r\n                    </div>\r\n                    <span class=\"status-badge success\">Success</span>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <!-- TODO: Extract into quick-actions component -->\r\n        <div class=\"widget quick-actions\">\r\n            <h3>Quick Actions</h3>\r\n            <div class=\"actions-grid\">\r\n                <button class=\"action-button primary\">\r\n                    <fa-icon icon=\"plus\"></fa-icon>\r\n                    <span>New Backup</span>\r\n                </button>\r\n                <button class=\"action-button\">\r\n                    <fa-icon icon=\"rotate-left\"></fa-icon>\r\n                    <span>Recover</span>\r\n                </button>\r\n                <button class=\"action-button\">\r\n                    <fa-icon icon=\"play\"></fa-icon>\r\n                    <span>Standby</span>\r\n                </button>\r\n                <button class=\"action-button\">\r\n                    <fa-icon icon=\"copy\"></fa-icon>\r\n                    <span>Replication</span>\r\n                </button>\r\n            </div>\r\n        </div>\r\n        <div class=\"widget empty-widget\">\r\n            <div class=\"widget-head\">\r\n                <h3>Another Widget</h3>\r\n            </div>\r\n        </div>\r\n        <div class=\"widget empty-widget\">\r\n            <div class=\"widget-head\">\r\n                <h3>Another Widget</h3>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AASM,IAAO,qBAAP,MAAO,oBAAkB;EACG;EACH;EACK;EACN;EACE;EAE5B,kBAAe;AAEX,eAAW,MAAK;AACZ,WAAK,cAAc,QAAQ,aAAU;AACjC,cAAM,KAAK,QAAQ;AACnB,cAAM,QAAQ,GAAG,MAAM;AACvB,YAAI,OAAO;AACP,aAAG,MAAM,YAAY,oBAAoB,KAAK;AAC9C,aAAG,MAAM,QAAQ;QACrB;MACJ,CAAC;AAGD,WAAK,eAAe,KAAK,YAAY,eAAe,GAAG,KAAK,GAAG;AAC/D,WAAK,eAAe,KAAK,cAAc,eAAe,GAAG,MAAM,GAAG;IACtE,GAAG,GAAG;EACV;;EAGA,eAAe,SAAsB,OAAe,KAAa,UAAgB;AAE7E,UAAM,QAAQ,KAAK,IAAI,IAAI,MAAM,KAAK;AACtC,UAAM,aAAa,MAAM,SAAS;AAClC,UAAM,WAAW,WAAW;AAC5B,QAAI,UAAU;AACd,QAAI,OAAO;AAGX,UAAM,eAAe,CAAC,QAAe;AACjC,aAAO,KAAK,MAAM,GAAG,EAAE,SAAQ,EAAG,QAAQ,yBAAyB,GAAG;IAC1E;AAEA,UAAM,QAAQ,YAAY,MAAK;AAC3B;AAGA,gBAAU,QAAQ,YAAY;AAG9B,UAAI,QAAQ,OAAO;AACf,kBAAU;AACV,sBAAc,KAAK;MACvB;AAEA,cAAQ,cAAc,aAAa,OAAO;IAC9C,GAAG,QAAQ;EACf;;qCArDS,qBAAkB;EAAA;yEAAlB,qBAAkB,WAAA,CAAA,CAAA,eAAA,CAAA,GAAA,WAAA,SAAA,yBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;;;;;;;;;;;;;;;;;ACT/B,MAAA,yBAAA,GAAA,SAAA,EAAS,GAAA,OAAA,CAAA,EACgB,GAAA,IAAA;AACb,MAAA,iBAAA,GAAA,qBAAA;AAAmB,MAAA,uBAAA;AACvB,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,kDAAA;AAAgD,MAAA,uBAAA,EAAI;AAG3D,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAiB,GAAA,OAAA,CAAA,EAEe,GAAA,OAAA,CAAA,EACG,GAAA,OAAA,CAAA,EACG,IAAA,MAAA;AAChB,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AACnB,MAAA,yBAAA,IAAA,OAAA,EAAA;AACI,MAAA,oBAAA,IAAA,WAAA,EAAA;AACJ,MAAA,uBAAA,EAAM;AAEV,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,QAAA,IAAA,CAAA;AAC6B,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AACzD,MAAA,yBAAA,IAAA,QAAA,IAAA,CAAA;AAA0D,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA,EAAO;AAExE,MAAA,yBAAA,IAAA,OAAA,EAAA;AACI,MAAA,oBAAA,IAAA,OAAA,IAAA,CAAA;AACJ,MAAA,uBAAA,EAAM,EACJ;AAEV,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,OAAA,EAAA,EACK,IAAA,OAAA,CAAA,EACG,IAAA,MAAA;AACjB,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AACpB,MAAA,yBAAA,IAAA,OAAA,EAAA;AACI,MAAA,oBAAA,IAAA,WAAA,EAAA;AACJ,MAAA,uBAAA,EAAM;AAEV,MAAA,yBAAA,IAAA,KAAA,IAAA,CAAA;AAA8C,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACjD,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,OAAA,EAAA;AAEnB,MAAA,oBAAA,IAAA,WAAA,EAAA;AACJ,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,oBAAA;AAAkB,MAAA,uBAAA,EAAO,EAChD,EACJ;AAEV,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA6B,IAAA,OAAA,EAAA,EACG,IAAA,OAAA,CAAA,EACG,IAAA,MAAA;AACjB,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AACrB,MAAA,yBAAA,IAAA,OAAA,EAAA;AACI,MAAA,oBAAA,IAAA,WAAA,EAAA;AACJ,MAAA,uBAAA,EAAM;AAEV,MAAA,yBAAA,IAAA,KAAA,IAAA,CAAA;AAAgD,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AACrD,MAAA,yBAAA,IAAA,KAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,wBAAA;AAAsB,MAAA,uBAAA,EAAI,EAC/C;AAIV,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAmC,IAAA,OAAA,EAAA,EACN,IAAA,IAAA;AACjB,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AAClB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAS;AAE9C,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,OAAA,EAAA,EACG,IAAA,OAAA,EAAA,EACI,IAAA,OAAA,EAAA;AAEjB,MAAA,oBAAA,IAAA,WAAA,EAAA;AACJ,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,IAAA;AACpB,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AACjB,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,0BAAA;AAAmB,MAAA,uBAAA,EAAO,EAC9B;AAEV,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAmC,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA,EAAO;AAErD,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,OAAA,EAAA,EACI,IAAA,OAAA,EAAA;AAEjB,MAAA,oBAAA,IAAA,WAAA,EAAA;AACJ,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,IAAA;AACpB,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACd,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,2BAAA;AAAoB,MAAA,uBAAA,EAAO,EAC/B;AAEV,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAmC,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA,EAAO,EAC/C,EACJ;AAGV,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAkC,IAAA,IAAA;AAC1B,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AACjB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,UAAA,EAAA;AAElB,MAAA,oBAAA,IAAA,WAAA,EAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA,EAAO;AAE3B,MAAA,yBAAA,IAAA,UAAA,EAAA;AACI,MAAA,oBAAA,IAAA,WAAA,EAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA,EAAO;AAExB,MAAA,yBAAA,IAAA,UAAA,EAAA;AACI,MAAA,oBAAA,IAAA,WAAA,EAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA,EAAO;AAExB,MAAA,yBAAA,IAAA,UAAA,EAAA;AACI,MAAA,oBAAA,IAAA,WAAA,EAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA,EAAO,EACnB,EACP;AAEV,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAiC,KAAA,OAAA,EAAA,EACJ,KAAA,IAAA;AACjB,MAAA,iBAAA,KAAA,gBAAA;AAAc,MAAA,uBAAA,EAAK,EACrB;AAEV,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAiC,KAAA,OAAA,EAAA,EACJ,KAAA,IAAA;AACjB,MAAA,iBAAA,KAAA,gBAAA;AAAc,MAAA,uBAAA,EAAK,EACrB,EACJ,EACJ;;oBDnHI,mBAAiB,eAAA,GAAA,QAAA,CAAA,4npBAAA,EAAA,CAAA;;;sEAIlB,oBAAkB,CAAA;UAN9B;uBACa,iBAAe,SAChB,CAAC,iBAAiB,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,yiWAAA,EAAA,CAAA;cAKE,eAAa,CAAA;UAA1C;WAAa,cAAc;MACD,cAAY,CAAA;UAAtC;WAAU,cAAc;MACO,mBAAiB,CAAA;UAAhD;WAAU,mBAAmB;MACJ,aAAW,CAAA;UAApC;WAAU,aAAa;MACI,eAAa,CAAA;UAAxC;WAAU,eAAe;;;;6EALjB,oBAAkB,EAAA,WAAA,sBAAA,UAAA,qDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}