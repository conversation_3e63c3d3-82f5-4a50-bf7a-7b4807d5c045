import { Component, AfterViewInit, ElementRef, ViewChildren, QueryList, ViewChild } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

@Component({
    selector: 'app-dashboard',
    imports: [FontAwesomeModule],
    templateUrl: './dashboard.component.html',
    styleUrl: './dashboard.component.less'
})
export class DashboardComponent {
    @ViewChildren('progressFill') progressFills!: QueryList<ElementRef>;
    @ViewChild('storageValue') storageValue!: ElementRef;
    @ViewChild('storagePercentage') storagePercentage!: ElementRef;
    @ViewChild('backupValue') backupValue!: ElementRef;
    @ViewChild('recoveryValue') recoveryValue!: ElementRef;

    ngAfterViewInit() {
        // Set progress bar width style variable
        setTimeout(() => {
            this.progressFills.forEach(element => {
                const el = element.nativeElement;
                const width = el.style.width;
                if (width) {
                    el.style.setProperty('--progress-width', width);
                    el.style.width = 'var(--progress-width)';
                }
            });

            // Add number animation with shorter duration
            this.animateCounter(this.backupValue.nativeElement, 0, 147, 700);
            this.animateCounter(this.recoveryValue.nativeElement, 0, 1234, 700);
        }, 300);
    }

    // Number counter animation - optimized for faster display
    animateCounter(element: HTMLElement, start: number, end: number, duration: number) {
        // For large numbers, reduce steps to speed up animation
        const steps = Math.min(30, end - start);
        const stepValue = (end - start) / steps;
        const stepTime = duration / steps;
        let current = start;
        let step = 0;

        // Format numbers with thousands separator
        const formatNumber = (num: number) => {
            return Math.round(num).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        };

        const timer = setInterval(() => {
            step++;

            // Use non-linear growth, slower at start and faster at end
            current = start + stepValue * step;

            // Ensure final value is displayed on last step
            if (step >= steps) {
                current = end;
                clearInterval(timer);
            }

            element.textContent = formatNumber(current);
        }, stepTime);
    }
}
