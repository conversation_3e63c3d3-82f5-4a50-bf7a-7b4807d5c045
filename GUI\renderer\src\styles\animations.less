/**
 * Animation styles for the GUI renderer.
 * <AUTHOR>
 */

// Animation keyframes
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(1.25rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-1.25rem);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes cardPop {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 .625rem 1.5rem rgba(0, 0, 0, 0.3);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 .375rem 1rem rgba(0, 0, 0, 0.2);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(239, 78, 56, 0.4);
    }
    70% {
        box-shadow: 0 0 0 .5rem rgba(239, 78, 56, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(239, 78, 56, 0);
    }
}

@keyframes focusGlow {
    0% {
        box-shadow: 0 0 0 0 rgba(239, 78, 56, 0);
    }
    70% {
        box-shadow: 0 0 1rem .375rem rgba(239, 78, 56, 0.4);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(239, 78, 56, 0);
    }
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(1.25rem);
        visibility: hidden;
    }
    1% {
        visibility: visible;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        visibility: visible;
    }
}

@keyframes progressAnimate {
    from {
        width: 0;
    }
    to {
        width: var(--progress-width, 48%);
    }
}

@keyframes zoom-in {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}
