<!-- Panel Header -->
<header (click)="expandPanel()">
    <h2>Select Recovery Destination</h2>
    @if (showDetailView()) {
        <div class="detail-header">
            <div class="mapping-mode-tabs">
                <button
                    class="tab-button"
                    [class.active]="mappingMode === 'auto'"
                    (click)="setMappingMode('auto')">
                    Auto-mapped
                </button>
                <button
                    class="tab-button"
                    [class.active]="mappingMode === 'custom'"
                    (click)="setMappingMode('custom')">
                    Custom
                </button>
            </div>
            <button class="undo-button" (click)="resetMapping()">
                <fa-icon icon="rotate-left"></fa-icon>
                <span>Undo</span>
            </button>
        </div>
    } @else {
        <div class="icon-container">
            <fa-icon icon="location-dot"></fa-icon>
        </div>
    }
</header>

<!-- Expanded Content -->
@if (isPanelExpanded()) {
    @if (!showDetailView()) {
        <section>
            <!-- Selection View -->
            @for (dest of recoveryDestinations; track dest.id) {
                <div class="item" [class.selected]="selectedDestination() === dest.id" (click)="selectDestination(dest.id)">
                    <div class="item-icon">
                        <fa-icon [icon]="dest.icon"></fa-icon>
                    </div>
                    <div class="item-content">
                        <h3>{{ dest.title }}</h3>
                        <p>{{ dest.description }}</p>
                    </div>
                </div>
            }
        </section>
    } @else {
        <!-- Detail View -->
        <section class="detail-view">
            <!-- Mapping section -->
            <div class="mapping-section">
                <!-- Use the new disk mapping component -->
                @if (mappingMode === 'auto') {
                    <app-disk-mapping
                        [sourceDisks]="sourceDisks"
                        [targetDisks]="targetDisks"
                        (resetMapping)="resetMapping()"
                        (moreOptions)="onMoreOptions($event)">
                    </app-disk-mapping>
                } @else {
                    <app-disk-mapping-custom
                        [sourceDisks]="sourceDisks"
                        [targetDisks]="targetDisks"
                        (resetMapping)="resetMapping()"
                        (moreOptions)="onMoreOptions($event)">
                    </app-disk-mapping-custom>
                }
            </div>

            <!-- Settings footer -->
            <div class="settings-footer">
                <div class="settings-options">
                    <label class="checkbox-group">
                        <input
                            type="checkbox"
                            [checked]="settings.ignoreChecksum"
                            (change)="toggleIgnoreChecksum()">
                        <span class="checkbox-custom"></span>
                        <span class="checkbox-label">Ignore Checksum during restore</span>
                    </label>

                    <div class="post-restore-group">
                        <label class="checkbox-group">
                            <input
                                type="checkbox"
                                [checked]="settings.postRestoreOperation"
                                (change)="togglePostRestoreOperation()">
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-label">Post restore operation</span>
                        </label>
                        @if (settings.postRestoreOperation) {
                            <select class="restore-action-select" [(ngModel)]="settings.postRestoreAction">
                                <option value="Reboot System">Reboot System</option>
                                <option value="Shutdown">Shutdown</option>
                                <option value="No Action">No Action</option>
                            </select>
                        }
                    </div>
                </div>

                <button class="next-button" (click)="onNextClick()">
                    Next
                </button>
            </div>
         </section>
    }

}
