@import '../../../../styles/variables.less';
@import '../../../../styles/mixins.less';

:host {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.75rem;
    overflow: hidden;
    cursor: pointer;
    padding: 1rem;

    // Panel Header
    header {
        flex: 0 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        padding-bottom: 1rem;

        h2 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
            line-height: 1.125rem;
            color: @text-color;
        }

        .icon-container {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0);
            padding: 0;
        }
    }

    // Collapsed vertical panel styling
    &.collapsed {
        header {
            writing-mode: vertical-lr;
            text-orientation: mixed;
            padding: 0;
            height: 100%;
            flex-direction: row-reverse;
            justify-content: flex-end;

            h2 {
                font-size: 1rem;
                font-weight: 600;
                line-height: 1.2;
                text-align: center;
                margin: 0;
                writing-mode: vertical-lr;
                text-orientation: mixed;
                color: @text-color;
                letter-spacing: -0.02em;
                transform: rotate(180deg);
            }

            .icon-container {
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                border-radius: 6px;
                background: rgba(255, 255, 255, 0);
                padding: 0;
            }
        }

        section {
            display: none;
        }
    }

    // Expanded content section
    section {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        display: flex;
        flex-direction: column;
        gap: 1rem;

        // Hide scrollbar for Chromium/WebKit (Electron)
        &::-webkit-scrollbar {
            display: none;
        }
        // Connection section (source & destination selection)
        .connection-section {
            .connection-row {
                display: flex;
                align-items: center;
                gap: 1rem;
                flex-wrap: wrap;

                .connection-group {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    flex: 1;
                    min-width: 200px;

                    .connection-icon {
                        color: @text-color;
                        font-size: 3rem;
                        opacity: 0.8;
                    }

                    .connection-details {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        gap: 0.25rem;

                        .connection-label {
                            font-size: 0.75rem;
                            color: @text-secondary;
                            font-weight: 500;
                            line-height: 1;
                        }

                        .connection-dropdown {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            padding: 0.5rem 0.75rem;
                            background: rgba(255, 255, 255, 0.05);
                            border: 1px solid @border-color;
                            border-radius: @border-radius-small;
                            cursor: pointer;
                            .transition(all);

                            &:hover {
                                background: rgba(255, 255, 255, 0.1);
                                border-color: rgba(255, 255, 255, 0.3);
                            }

                            .dropdown-value {
                                color: @text-color;
                                font-size: 0.875rem;
                                font-weight: 500;
                            }

                            .dropdown-arrow {
                                color: @text-secondary;
                                font-size: 0.75rem;
                            }
                        }
                    }
                }

                .disconnect-button {
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid @border-color;
                    border-radius: @border-radius-small;
                    color: @text-color;
                    padding: 0.625rem 1rem;
                    font-size: 0.875rem;
                    font-weight: 500;
                    cursor: pointer;
                    .transition(all);

                    &:hover:not(.disabled) {
                        background: rgba(255, 255, 255, 0.15);
                        border-color: rgba(255, 255, 255, 0.3);
                    }

                    &.disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                }
            }
        }

        // File selection section
        .file-selection-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;

            .section-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;

                .section-title {
                    font-size: 0.875rem;
                    color: @text-color;
                    font-weight: 500;
                }

                .destination-label {
                    font-size: 0.875rem;
                    color: @text-secondary;
                    font-weight: 500;
                }
            }

            .file-panels {
                flex: 1;
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
                overflow: hidden;

                .file-panel {
                    display: flex;
                    flex-direction: column;
                    background: rgba(255, 255, 255, 0.05);
                    border: 1px solid @border-color;
                    border-radius: @border-radius-small;
                    overflow: hidden;

                    .panel-header {
                        padding: 0.75rem 1rem;
                        background: rgba(255, 255, 255, 0.05);
                        border-bottom: 1px solid @border-color;

                        .panel-title {
                            font-size: 0.875rem;
                            font-weight: 600;
                            color: @text-color;
                        }
                    }

                    .panel-content {
                        flex: 1;
                        overflow-y: auto;
                        padding: 0.5rem;

                        &::-webkit-scrollbar {
                            width: 4px;
                        }

                        &::-webkit-scrollbar-track {
                            background: transparent;
                        }

                        &::-webkit-scrollbar-thumb {
                            background: rgba(255, 255, 255, 0.2);
                            border-radius: 2px;
                        }

                        &::-webkit-scrollbar-thumb:hover {
                            background: rgba(255, 255, 255, 0.3);
                        }
                    }

                    // Source panel specific styles
                    &.source-panel {
                        .file-tree {
                            .file-item {
                                .file-row {
                                    display: flex;
                                    align-items: center;
                                    padding: 0.375rem 0.5rem;
                                    border-radius: 4px;
                                    cursor: pointer;
                                    .transition(background-color);

                                    &:hover {
                                        background: rgba(255, 255, 255, 0.05);
                                    }

                                    .file-controls {
                                        display: flex;
                                        align-items: center;
                                        gap: 0.5rem;
                                        margin-right: 0.75rem;

                                        .expand-toggle {
                                            background: none;
                                            border: none;
                                            padding: 0;
                                            width: 16px;
                                            height: 16px;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            cursor: pointer;
                                            color: @text-secondary;
                                            .transition(transform);

                                            &.expanded {
                                                transform: rotate(180deg);
                                            }

                                            fa-icon {
                                                font-size: 0.75rem;
                                            }
                                        }

                                        .expand-spacer {
                                            width: 16px;
                                            height: 16px;
                                        }

                                        .file-checkbox {
                                            width: 16px;
                                            height: 16px;
                                            cursor: pointer;
                                        }
                                    }

                                    .file-info {
                                        display: flex;
                                        align-items: center;
                                        gap: 0.5rem;
                                        flex: 1;

                                        .file-icon {
                                            color: @text-secondary;
                                            font-size: 0.875rem;
                                        }

                                        .file-name {
                                            color: @text-color;
                                            font-size: 0.875rem;
                                            font-weight: 400;
                                        }
                                    }
                                }

                                .file-children {
                                    padding-left: 1rem;
                                    margin-top: 0.25rem;

                                    .child-item {
                                        .file-row {
                                            padding: 0.25rem 0.5rem;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Destination panel specific styles
                    &.destination-panel {
                        .empty-state {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            height: 100%;
                            gap: 0.75rem;
                            opacity: 0.6;

                            .empty-icon {
                                font-size: 2rem;
                                color: @text-secondary;
                            }

                            .empty-text {
                                font-size: 0.875rem;
                                color: @text-secondary;
                                text-align: center;
                            }
                        }
                    }
                }
            }
        }

        // Type selection section
        .type-selection-section {
            margin-bottom: 1.5rem;

            .type-options-horizontal {
                display: flex;
                flex-direction: row;
                gap: 1rem;
                height: 6rem; // 96px as per Figma design

                .type-option-horizontal {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                    padding: 0;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 0.5rem; // 8px
                    background: transparent;
                    .transition(all, 0.2s, ease);
                    position: relative;
                    overflow: hidden;
                    height: 100%;

                    .option-content-horizontal {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        gap: 0.5rem; // 8px
                        padding: 1rem 3.5rem 1rem 1rem; // 56px right padding for icon space
                        width: 100%;
                        height: 100%;

                        .option-icon-horizontal {
                            flex-shrink: 0;
                            width: 2.25rem; // 36px
                            height: 2.25rem; // 36px
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 0.5rem; // 8px

                            .type-icon-horizontal {
                                font-size: 2.25rem; // 36px
                                color: @text-color;
                            }
                        }

                        .option-text-horizontal {
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            gap: 0.25rem; // 4px

                            .option-title-horizontal {
                                font-family: 'Noto Sans', sans-serif;
                                font-weight: 500; // Medium
                                font-size: 1rem; // 16px
                                line-height: 1.25; // 20px
                                color: #ffffff;
                                text-align: left;
                                white-space: nowrap;
                            }

                            .option-description-horizontal {
                                font-family: 'Noto Sans', sans-serif;
                                font-weight: 500; // Medium
                                font-size: 0.875rem; // 14px
                                line-height: 1.25; // 20px
                                color: rgba(255, 255, 255, 0.6);
                                text-align: left;
                                white-space: nowrap;
                            }
                        }
                    }

                    // Hover state
                    &:hover {
                        border-color: rgba(255, 255, 255, 0.2);
                        background: rgba(255, 255, 255, 0.05);
                    }

                    // Selected state
                    &.selected {
                        border-color: @primary-color;
                        background: rgba(239, 78, 56, 0.08);
                        box-shadow: 0 4px 20px rgba(239, 78, 56, 0.15);

                        .option-content-horizontal {
                            .option-icon-horizontal .type-icon-horizontal {
                                color: @primary-color;
                            }

                            .option-text-horizontal {
                                .option-title-horizontal {
                                    color: #ffffff;
                                }

                                .option-description-horizontal {
                                    color: rgba(255, 255, 255, 0.8);
                                }
                            }
                        }
                    }

                    // Active/pressed state
                    &:active {
                        transform: scale(0.98);
                    }
                }
            }
        }

        // Action bar
        .action-bar {
            display: flex;
            justify-content: flex-end;
            padding-top: 1rem;
            border-top: 1px solid @border-color;

            .action-button {
                background: @primary-gradient;
                border: none;
                border-radius: @border-radius-small;
                color: white;
                padding: 0.75rem 2rem;
                font-size: 0.875rem;
                font-weight: 600;
                cursor: pointer;
                .transition(all);

                &:hover:not(.disabled) {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(239, 78, 56, 0.3);
                }

                &.disabled {
                    background: rgba(255, 255, 255, 0.1);
                    cursor: not-allowed;
                    opacity: 0.5;
                }
            }
        }

        // File Replication section - matching Figma design exactly
        .file-replication-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 1.5rem;

            // Replication type switch (two horizontal panels)
            .replication-type-switch {
                display: flex;
                flex-direction: row;
                gap: 1rem;
                height: 96px; // 6rem
                cursor: pointer;

                .type-switch-option {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    background: transparent;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 8px;
                    cursor: pointer;
                    padding: 0;
                    .transition(all, 0.2s, ease);
                    position: relative;
                    overflow: hidden;

                    .switch-option-content {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        gap: 8px;
                        padding: 16px 56px 16px 16px; // pl-14 pr-4 py-4 converted
                        width: 100%;
                        height: 100%;

                        .switch-option-icon {
                            flex-shrink: 0;
                            width: 36px;
                            height: 36px;
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            img {
                                width: 100%;
                                height: 100%;
                                display: block;
                                max-width: none;
                            }
                        }

                        .switch-option-text {
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            gap: 4px;

                            .switch-option-title {
                                font-family: 'Noto Sans', sans-serif;
                                font-weight: 500;
                                font-size: 16px;
                                line-height: 20px;
                                color: #ffffff;
                                text-align: left;
                                white-space: nowrap;
                            }

                            .switch-option-subtitle {
                                font-family: 'Noto Sans', sans-serif;
                                font-weight: 500;
                                font-size: 14px;
                                line-height: 20px;
                                color: rgba(255, 255, 255, 0.6);
                                text-align: left;
                                white-space: nowrap;
                            }
                        }
                    }

                    // Selected state with green background
                    &.selected {
                        background: rgba(16, 185, 129, 0.2);
                        border-color: rgba(16, 185, 129, 0.25);
                    }

                    &:hover:not(.selected) {
                        border-color: rgba(255, 255, 255, 0.2);
                        background: rgba(255, 255, 255, 0.05);
                    }
                }
            }

            // File table section
            .file-table-section {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 8px;
                overflow: hidden;

                .file-table-header {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    background: rgba(255, 255, 255, 0.08);
                    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                    padding: 12px 0;

                    .table-header-cell {
                        padding: 0 16px;
                        font-family: 'Noto Sans', sans-serif;
                        font-weight: 500;
                        font-size: 14px;
                        color: rgba(255, 255, 255, 0.8);
                        text-align: left;

                        &.file-name-column {
                            flex: 1;
                            width: 177px;
                        }

                        &:not(.file-name-column) {
                            flex-shrink: 0;
                            padding-left: 10px;
                        }
                    }
                }

                .file-table-row {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    overflow: hidden;
                    position: relative;
                    width: 100%;

                    .table-cell {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: flex-start;
                        padding: 0 16px;
                        height: 40px; // Approximate row height

                        &.file-name-column {
                            flex: 1;
                            width: 177px;
                        }

                        &:not(.file-name-column) {
                            flex-shrink: 0;
                            padding-left: 10px;
                        }
                    }
                }
            }

            // Action button
            .file-replication-actions {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: flex-end;
                padding: 0;
                width: 100%;

                .choose-options-button {
                    background: rgba(255, 255, 255, 0.1);
                    border: none;
                    border-radius: 8px;
                    display: flex;
                    flex-direction: row;
                    gap: 8px;
                    height: 34px;
                    align-items: center;
                    justify-content: center;
                    min-width: 194px;
                    width: 194px;
                    padding: 8px 12px;
                    cursor: pointer;
                    .transition(all, 0.2s, ease);

                    span {
                        font-family: 'Noto Sans', sans-serif;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: normal;
                        color: rgba(255, 255, 255, 0.5);
                        text-align: center;
                        white-space: pre;
                    }

                    &:not(.disabled) {
                        background: @primary-gradient;

                        span {
                            color: #ffffff;
                        }

                        &:hover {
                            transform: translateY(-1px);
                            box-shadow: 0 4px 12px rgba(239, 78, 56, 0.3);
                        }
                    }

                    &.disabled {
                        cursor: not-allowed;
                        opacity: 0.5;
                    }
                }
            }
        }

        // ActiveStandby section
        .activestandby-section {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem 2rem;
            margin-bottom: 1.5rem;

            .empty-state {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 1rem;
                text-align: center;

                .empty-icon {
                    font-size: 3rem;
                    color: @text-secondary;
                    opacity: 0.6;
                }

                .empty-text {
                    font-size: 0.875rem;
                    color: @text-secondary;
                    opacity: 0.8;
                }
            }
        }
    }
}
