import { Component, signal, Output, EventEmitter } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

@Component({
    selector: 'app-options',
    imports: [FontAwesomeModule],
    templateUrl: './options.component.html',
    styleUrl: './options.component.less'
})
export class OptionsComponent {
    // Track which option is currently selected/expanded
    selectedOption = signal<string | null>(null);

    // Events for parent component
    @Output() taskOptionsSelected = new EventEmitter<boolean>();

    selectOption(option: string): void {
        const wasSelected = this.selectedOption() === option;
        this.selectedOption.set(wasSelected ? null : option);

        // Emit event when Task Options is selected/deselected
        if (option === 'taskOptions') {
            this.taskOptionsSelected.emit(!wasSelected);
        }
    }

    isSelected(option: string): boolean {
        return this.selectedOption() === option;
    }
}
