import { Component, input, output } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { CommonModule } from '@angular/common';

export interface DiskInfo {
    id: string;
    name: string;
    type: string;
    capacity: string;
    partitions: PartitionInfo[];
}

export interface PartitionInfo {
    id: string;
    name: string;
    size: string;
    type: string;
    usage: number; // percentage 0-100
    isBoot?: boolean;
}

@Component({
    selector: 'app-disk-mapping',
    templateUrl: './disk-mapping.component.html',
    styleUrl: './disk-mapping.component.less',
    imports: [FontAwesomeModule, CommonModule],
})
export class DiskMappingComponent {
    // Input properties
    sourceDisks = input.required<DiskInfo[]>();
    targetDisks = input.required<DiskInfo[]>();

    // Output events
    moreOptions = output<string>();

    // Handle more options for partition
    onMoreOptions(partitionId: string): void {
        this.moreOptions.emit(partitionId);
    }
}
