{"version": 3, "sources": ["renderer/src/app/replication/shared/replication-schedule/replication-schedule.component.less"], "sourcesContent": ["@import '../../../../styles/variables.less';\n@import '../../../../styles/mixins.less';\n\n:host {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 0.75rem;\n    overflow: hidden;\n    cursor: pointer;\n    padding: 1rem;\n\n    // Panel Header\n    header {\n        flex: 0 0 auto;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        gap: 1rem;\n        padding-bottom: 1rem;\n\n        h2 {\n            font-size: 1.125rem;\n            font-weight: 600;\n            margin: 0;\n            line-height: 1.125rem;\n            color: @text-color;\n        }\n\n        .icon-container {\n            width: 20px;\n            height: 20px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            flex-shrink: 0;\n            border-radius: 6px;\n            background: rgba(255, 255, 255, 0);\n            padding: 0;\n        }\n    }\n\n    // Collapsed vertical panel styling\n    &.collapsed {\n        header {\n            writing-mode: vertical-lr;\n            text-orientation: mixed;\n            padding: 0;\n            height: 100%;\n            flex-direction: row-reverse;\n            justify-content: flex-end;\n\n            h2 {\n                font-size: 1rem;\n                font-weight: 600;\n                line-height: 1.2;\n                text-align: center;\n                margin: 0;\n                writing-mode: vertical-lr;\n                text-orientation: mixed;\n                color: @text-color;\n                letter-spacing: -0.02em;\n                transform: rotate(180deg);\n            }\n\n            .icon-container {\n                width: 20px;\n                height: 20px;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                flex-shrink: 0;\n                border-radius: 6px;\n                background: rgba(255, 255, 255, 0);\n                padding: 0;\n            }\n        }\n\n        section {\n            display: none;\n        }\n    }\n\n    section {\n        flex: 1;\n        overflow-y: auto;\n        overflow-x: hidden;\n\n\n    }\n}\n"], "mappings": ";AAGA;AACI,WAAA;AACA,kBAAA;AACA,UAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,YAAA;AACA,UAAA;AACA,WAAA;;AARJ,MAWI;AACI,QAAA,EAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,kBAAA;;AAjBR,MAWI,OAQI;AACI,aAAA;AACA,eAAA;AACA,UAAA;AACA,eAAA;AACA,SAAA;;AAxBZ,MAWI,OAgBI,CAAA;AACI,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;;AAKR,KAAC,CAAA,UACG;AACI,gBAAA;AACA,oBAAA;AACA,WAAA;AACA,UAAA;AACA,kBAAA;AACA,mBAAA;;AAPR,KAAC,CAAA,UACG,OAQI;AACI,aAAA;AACA,eAAA;AACA,eAAA;AACA,cAAA;AACA,UAAA;AACA,gBAAA;AACA,oBAAA;AACA,SAAA;AACA,kBAAA;AACA,aAAW,OAAA;;AAnBvB,KAAC,CAAA,UACG,OAqBI,CApCJ;AAqCQ,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;;AA/BZ,KAAC,CAAA,UAmCG;AACI,WAAA;;AA7EZ,MAiFI;AACI,QAAA;AACA,cAAA;AACA,cAAA;;", "names": []}