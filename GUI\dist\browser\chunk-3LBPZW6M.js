import {
  NavigationEnd,
  Router,
  RouterLink,
  RouterLinkActive,
  RouterModule,
  RouterOutlet
} from "./chunk-N54PRT5A.js";
import {
  FaIconComponent,
  FontAwesomeModule
} from "./chunk-N34W2DJ7.js";
import {
  Component,
  filter,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵstyleProp,
  ɵɵtext
} from "./chunk-TBQAZTY7.js";

// renderer/src/app/preferences/preferences.component.ts
var _c0 = () => ["/preferences", "general"];
var _c1 = () => ["/preferences", "proxy"];
var _c2 = () => ["/preferences", "notifications"];
var _c3 = () => ["/preferences", "backup"];
var _c4 = () => ["/preferences", "alert"];
var _c5 = () => ["/preferences", "console"];
var _c6 = () => ["/preferences", "hyperback"];
var _c7 = () => ["/preferences", "online-portal"];
var _c8 = () => ["/preferences", "quick-recovery"];
var _c9 = () => ["/preferences", "rescue-boot"];
var PreferencesComponent = class _PreferencesComponent {
  router;
  activeRoute = "general";
  constructor(router) {
    this.router = router;
  }
  ngOnInit() {
    this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe((event) => {
      const urlSegments2 = event.url.split("/");
      const lastSegment2 = urlSegments2[urlSegments2.length - 1];
      this.activeRoute = lastSegment2 || "general";
    });
    const currentUrl = this.router.url;
    const urlSegments = currentUrl.split("/");
    const lastSegment = urlSegments[urlSegments.length - 1];
    this.activeRoute = lastSegment || "general";
  }
  static \u0275fac = function PreferencesComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _PreferencesComponent)(\u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _PreferencesComponent, selectors: [["app-preferences"]], decls: 50, vars: 60, consts: [[1, "title"], ["routerLinkActive", "active", 1, "item", 3, "routerLink"], ["icon", "sliders"], ["icon", "globe"], ["icon", "bell"], ["icon", "server"], ["icon", "exclamation-triangle"], ["icon", "square-terminal"], ["icon", "rocket"], ["icon", "external-link-alt"], ["icon", "undo-alt"], ["icon", "boot"], [1, "main"]], template: function PreferencesComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "header")(1, "div", 0)(2, "h1");
      \u0275\u0275text(3, "Preferences");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p");
      \u0275\u0275text(5, "Configure Application Preferences, predefined Cloud & Hypervisors, Storage Destinations and Network connected Client Agents here");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(6, "section")(7, "nav")(8, "a", 1);
      \u0275\u0275element(9, "fa-icon", 2);
      \u0275\u0275elementStart(10, "span");
      \u0275\u0275text(11, "General Settings");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(12, "a", 1);
      \u0275\u0275element(13, "fa-icon", 3);
      \u0275\u0275elementStart(14, "span");
      \u0275\u0275text(15, "Proxy");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(16, "a", 1);
      \u0275\u0275element(17, "fa-icon", 4);
      \u0275\u0275elementStart(18, "span");
      \u0275\u0275text(19, "Notifications");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(20, "a", 1);
      \u0275\u0275element(21, "fa-icon", 5);
      \u0275\u0275elementStart(22, "span");
      \u0275\u0275text(23, "Backup");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(24, "a", 1);
      \u0275\u0275element(25, "fa-icon", 6);
      \u0275\u0275elementStart(26, "span");
      \u0275\u0275text(27, "Alert");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(28, "a", 1);
      \u0275\u0275element(29, "fa-icon", 7);
      \u0275\u0275elementStart(30, "span");
      \u0275\u0275text(31, "Console");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(32, "a", 1);
      \u0275\u0275element(33, "fa-icon", 8);
      \u0275\u0275elementStart(34, "span");
      \u0275\u0275text(35, "Hyperback");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(36, "a", 1);
      \u0275\u0275element(37, "fa-icon", 9);
      \u0275\u0275elementStart(38, "span");
      \u0275\u0275text(39, "Online Portal");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(40, "a", 1);
      \u0275\u0275element(41, "fa-icon", 10);
      \u0275\u0275elementStart(42, "span");
      \u0275\u0275text(43, "Quick Recovery");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(44, "a", 1);
      \u0275\u0275element(45, "fa-icon", 11);
      \u0275\u0275elementStart(46, "span");
      \u0275\u0275text(47, "Rescue Boot");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(48, "div", 12);
      \u0275\u0275element(49, "router-outlet");
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(8);
      \u0275\u0275styleProp("--nav-index", 0);
      \u0275\u0275classProp("active", ctx.activeRoute === "general");
      \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(50, _c0));
      \u0275\u0275advance(4);
      \u0275\u0275styleProp("--nav-index", 1);
      \u0275\u0275classProp("active", ctx.activeRoute === "proxy");
      \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(51, _c1));
      \u0275\u0275advance(4);
      \u0275\u0275styleProp("--nav-index", 2);
      \u0275\u0275classProp("active", ctx.activeRoute === "notifications");
      \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(52, _c2));
      \u0275\u0275advance(4);
      \u0275\u0275styleProp("--nav-index", 3);
      \u0275\u0275classProp("active", ctx.activeRoute === "backup");
      \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(53, _c3));
      \u0275\u0275advance(4);
      \u0275\u0275styleProp("--nav-index", 4);
      \u0275\u0275classProp("active", ctx.activeRoute === "alert");
      \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(54, _c4));
      \u0275\u0275advance(4);
      \u0275\u0275styleProp("--nav-index", 5);
      \u0275\u0275classProp("active", ctx.activeRoute === "console");
      \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(55, _c5));
      \u0275\u0275advance(4);
      \u0275\u0275styleProp("--nav-index", 6);
      \u0275\u0275classProp("active", ctx.activeRoute === "hyperback");
      \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(56, _c6));
      \u0275\u0275advance(4);
      \u0275\u0275styleProp("--nav-index", 7);
      \u0275\u0275classProp("active", ctx.activeRoute === "online-portal");
      \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(57, _c7));
      \u0275\u0275advance(4);
      \u0275\u0275styleProp("--nav-index", 8);
      \u0275\u0275classProp("active", ctx.activeRoute === "quick-recovery");
      \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(58, _c8));
      \u0275\u0275advance(4);
      \u0275\u0275styleProp("--nav-index", 9);
      \u0275\u0275classProp("active", ctx.activeRoute === "rescue-boot");
      \u0275\u0275property("routerLink", \u0275\u0275pureFunction0(59, _c9));
    }
  }, dependencies: [RouterModule, RouterOutlet, RouterLink, RouterLinkActive, FontAwesomeModule, FaIconComponent], styles: ['\n\n[_nghost-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  height: 100%;\n  padding: 1rem;\n  overflow: hidden;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 0ms;\n  animation-name: fadeInUp;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  gap: 1rem;\n  width: 100%;\n  min-width: 0;\n  padding: 1rem;\n  overflow: hidden;\n  border-radius: 0.75rem;\n  opacity: 0;\n  background: rgba(255, 255, 255, 0.1);\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 50ms;\n  animation-name: fadeInUp;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  width: 14rem;\n  gap: 0.5rem;\n  flex-shrink: 0;\n  overflow-y: auto;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]::-webkit-scrollbar {\n  display: none;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 4px 8px;\n  border-radius: 0.5rem;\n  color: #ffffff;\n  text-decoration: none;\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 500;\n  font-size: 14px;\n  line-height: 1.36;\n  width: 100%;\n  border: 1px solid transparent;\n  opacity: 0;\n  transition-property:\n    background,\n    border,\n    box-shadow;\n  transition-timing-function: ease;\n  transition-duration: 300ms;\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: calc(100ms + var(--nav-index, 0) * 25ms);\n  animation-name: fadeInLeft;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   fa-icon[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  font-size: 16px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  flex: 1;\n  text-align: left;\n  white-space: nowrap;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]:hover:not(.active) {\n  background: rgba(255, 255, 255, 0.1);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   .item.active[_ngcontent-%COMP%] {\n  background: rgba(16, 185, 129, 0.2);\n  border: 1px solid rgba(16, 185, 129, 0.5);\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 31px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n  margin: 0;\n  padding: 0;\n  display: flex;\n  align-items: center;\n  opacity: 0;\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: calc(100ms + var(--nav-index, 0) * 25ms);\n  animation-name: fadeInLeft;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 400;\n  font-size: 16px;\n  line-height: 1.36;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%] {\n  flex: 1;\n  overflow: hidden;\n}\n/*# sourceMappingURL=preferences.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PreferencesComponent, [{
    type: Component,
    args: [{ selector: "app-preferences", imports: [RouterModule, FontAwesomeModule], template: `<!-- Header Section -->\r
<header>\r
    <div class="title">\r
        <h1>Preferences</h1>\r
        <p>Configure Application Preferences, predefined Cloud & Hypervisors, Storage Destinations and Network connected Client Agents here</p>\r
    </div>\r
</header>\r
\r
<!-- Main Content Container -->\r
<section>\r
    <!-- Left Navigation Panel -->\r
    <nav>\r
        <a class="item" [style.--nav-index]="0"\r
            [routerLink]="['/preferences', 'general']"\r
            [class.active]="activeRoute === 'general'"\r
            routerLinkActive="active">\r
            <fa-icon icon="sliders"></fa-icon>\r
            <span>General Settings</span>\r
        </a>\r
\r
        <a class="item" [style.--nav-index]="1"\r
            [routerLink]="['/preferences', 'proxy']"\r
            [class.active]="activeRoute === 'proxy'"\r
            routerLinkActive="active">\r
            <fa-icon icon="globe"></fa-icon>\r
            <span>Proxy</span>\r
        </a>\r
\r
        <a class="item" [style.--nav-index]="2"\r
            [routerLink]="['/preferences', 'notifications']"\r
            [class.active]="activeRoute === 'notifications'"\r
            routerLinkActive="active">\r
            <fa-icon icon="bell"></fa-icon>\r
            <span>Notifications</span>\r
        </a>\r
\r
        <a class="item" [style.--nav-index]="3"\r
            [routerLink]="['/preferences', 'backup']"\r
            [class.active]="activeRoute === 'backup'"\r
            routerLinkActive="active">\r
            <fa-icon icon="server"></fa-icon>\r
            <span>Backup</span>\r
        </a>\r
\r
        <a class="item" [style.--nav-index]="4"\r
            [routerLink]="['/preferences', 'alert']"\r
            [class.active]="activeRoute === 'alert'"\r
            routerLinkActive="active">\r
            <fa-icon icon="exclamation-triangle"></fa-icon>\r
            <span>Alert</span>\r
        </a>\r
\r
        <a class="item" [style.--nav-index]="5"\r
            [routerLink]="['/preferences', 'console']"\r
            [class.active]="activeRoute === 'console'"\r
            routerLinkActive="active">\r
            <fa-icon icon="square-terminal"></fa-icon>\r
            <span>Console</span>\r
        </a>\r
\r
        <a class="item" [style.--nav-index]="6"\r
            [routerLink]="['/preferences', 'hyperback']"\r
            [class.active]="activeRoute === 'hyperback'"\r
            routerLinkActive="active">\r
            <fa-icon icon="rocket"></fa-icon>\r
            <span>Hyperback</span>\r
        </a>\r
\r
        <a class="item" [style.--nav-index]="7"\r
            [routerLink]="['/preferences', 'online-portal']"\r
            [class.active]="activeRoute === 'online-portal'"\r
            routerLinkActive="active">\r
            <fa-icon icon="external-link-alt"></fa-icon>\r
            <span>Online Portal</span>\r
        </a>\r
\r
        <a class="item" [style.--nav-index]="8"\r
            [routerLink]="['/preferences', 'quick-recovery']"\r
            [class.active]="activeRoute === 'quick-recovery'"\r
            routerLinkActive="active">\r
            <fa-icon icon="undo-alt"></fa-icon>\r
            <span>Quick Recovery</span>\r
        </a>\r
\r
        <a class="item" [style.--nav-index]="9"\r
            [routerLink]="['/preferences', 'rescue-boot']"\r
            [class.active]="activeRoute === 'rescue-boot'"\r
            routerLinkActive="active">\r
            <fa-icon icon="boot"></fa-icon>\r
            <span>Rescue Boot</span>\r
        </a>\r
    </nav>\r
\r
    <!-- Right Content Panel -->\r
    <div class="main">\r
        <router-outlet></router-outlet>\r
    </div>\r
</section>\r
`, styles: ['/* renderer/src/app/preferences/preferences.component.less */\n:host {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  height: 100%;\n  padding: 1rem;\n  overflow: hidden;\n}\n:host header {\n  display: flex;\n  justify-content: space-between;\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 0ms;\n  animation-name: fadeInUp;\n}\n:host header .title h1 {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  color: #ffffff;\n}\n:host header .title p {\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n}\n:host section {\n  flex: 1;\n  display: flex;\n  gap: 1rem;\n  width: 100%;\n  min-width: 0;\n  padding: 1rem;\n  overflow: hidden;\n  border-radius: 0.75rem;\n  opacity: 0;\n  background: rgba(255, 255, 255, 0.1);\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 50ms;\n  animation-name: fadeInUp;\n}\n:host section nav {\n  display: flex;\n  flex-direction: column;\n  width: 14rem;\n  gap: 0.5rem;\n  flex-shrink: 0;\n  overflow-y: auto;\n}\n:host section nav::-webkit-scrollbar {\n  display: none;\n}\n:host section nav .item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 4px 8px;\n  border-radius: 0.5rem;\n  color: #ffffff;\n  text-decoration: none;\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 500;\n  font-size: 14px;\n  line-height: 1.36;\n  width: 100%;\n  border: 1px solid transparent;\n  opacity: 0;\n  transition-property:\n    background,\n    border,\n    box-shadow;\n  transition-timing-function: ease;\n  transition-duration: 300ms;\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: calc(100ms + var(--nav-index, 0) * 25ms);\n  animation-name: fadeInLeft;\n}\n:host section nav .item fa-icon {\n  width: 16px;\n  height: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  font-size: 16px;\n}\n:host section nav .item span {\n  flex: 1;\n  text-align: left;\n  white-space: nowrap;\n}\n:host section nav .item:hover:not(.active) {\n  background: rgba(255, 255, 255, 0.1);\n}\n:host section nav .item.active {\n  background: rgba(16, 185, 129, 0.2);\n  border: 1px solid rgba(16, 185, 129, 0.5);\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n}\n:host section nav .divider {\n  width: 100%;\n  height: 31px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n  margin: 0;\n  padding: 0;\n  display: flex;\n  align-items: center;\n  opacity: 0;\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: calc(100ms + var(--nav-index, 0) * 25ms);\n  animation-name: fadeInLeft;\n}\n:host section nav .divider span {\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 400;\n  font-size: 16px;\n  line-height: 1.36;\n  color: #ffffff;\n}\n:host section .main {\n  flex: 1;\n  overflow: hidden;\n}\n/*# sourceMappingURL=preferences.component.css.map */\n'] }]
  }], () => [{ type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(PreferencesComponent, { className: "PreferencesComponent", filePath: "renderer/src/app/preferences/preferences.component.ts", lineNumber: 13 });
})();
export {
  PreferencesComponent
};
//# sourceMappingURL=chunk-3LBPZW6M.js.map
