<!-- Panel Header -->
@if (!isPanelExpanded()) {
    <header (click)="expandPanel()">
        <h2>Replication Source & Destination</h2>
        <fa-icon icon="server"></fa-icon>
    </header>
}

<!-- Expanded Content -->
@else {
    <section>
        <!-- Source & Destination Selection -->
        <div class="connection-section">
            <div class="connection-row">
                <!-- Source Selection -->
                <div class="connection-group">
                    <fa-icon icon="desktop" class="connection-icon"></fa-icon>
                    <div class="connection-details">
                        <label class="connection-label">Choose Source</label>
                        <div class="connection-dropdown" (click)="onSourceChange($event)">
                            <span class="dropdown-value">{{ selectedSource() || 'Local' }}</span>
                            <fa-icon icon="chevron-down" class="dropdown-arrow"></fa-icon>
                        </div>
                    </div>
                </div>

                <!-- Destination Selection -->
                <div class="connection-group">
                    <fa-icon icon="desktop" class="connection-icon"></fa-icon>
                    <div class="connection-details">
                        <label class="connection-label">Choose Destination</label>
                        <div class="connection-dropdown" (click)="onDestinationChange($event)">
                            <span class="dropdown-value">{{ selectedDestination() || 'Desktop001' }}</span>
                            <fa-icon icon="chevron-down" class="dropdown-arrow"></fa-icon>
                        </div>
                    </div>
                </div>

                <!-- Disconnect Button -->
                <button
                    class="disconnect-button"
                    [class.disabled]="!canEstablishConnection()"
                    [disabled]="!canEstablishConnection()"
                >
                    Disconnect
                </button>
            </div>
        </div>

        <!-- Type Selection -->
        <div class="type-selection-section">
            <div class="section-header">
                <span class="section-title">Replication Type</span>
                <span class="section-description">Choose the type of replication for your data</span>
            </div>
            <div class="type-options">
                <div class="type-option"
                     [class.selected]="selectedReplicationType() === 'ActiveStandby'"
                     (click)="setReplicationType('ActiveStandby')">
                    <div class="option-radio">
                        <div class="radio-outer">
                            <div class="radio-inner" [class.active]="selectedReplicationType() === 'ActiveStandby'"></div>
                        </div>
                    </div>
                    <div class="option-content">
                        <div class="option-title">ActiveStandby</div>
                        <div class="option-description">Real-time data protection with instant failover capability</div>
                    </div>
                    <div class="option-icon">
                        <fa-icon icon="shield" class="type-icon"></fa-icon>
                    </div>
                </div>

                <div class="type-option"
                     [class.selected]="selectedReplicationType() === 'File Replication'"
                     (click)="setReplicationType('File Replication')">
                    <div class="option-radio">
                        <div class="radio-outer">
                            <div class="radio-inner" [class.active]="selectedReplicationType() === 'File Replication'"></div>
                        </div>
                    </div>
                    <div class="option-content">
                        <div class="option-title">File Replication</div>
                        <div class="option-description">Selective file and folder synchronization</div>
                    </div>
                    <div class="option-icon">
                        <fa-icon icon="copy" class="type-icon"></fa-icon>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Selection Section -->
        @if (isFileReplication()) {
            <div class="file-selection-section">
            <div class="section-header">
                <span class="section-title">Select File Replication source & destination folders</span>
                <span class="destination-label">Destination</span>
            </div>

            <div class="file-panels">
                <!-- Source Panel -->
                <div class="file-panel source-panel">
                    <div class="panel-header">
                        <span class="panel-title">{{ selectedSource() || 'Local' }}</span>
                    </div>
                    <div class="panel-content">
                        <div class="file-tree">
                            @for (item of fileTreeItems; track item.id) {
                                <div class="file-item"
                                     [class.expanded]="expandedFolders().includes(item.id)"
                                     [class.has-children]="item.hasChildren">
                                    <div class="file-row" (click)="item.hasChildren ? toggleFolder(item.id) : null">
                                        <div class="file-controls">
                                            @if (item.hasChildren) {
                                                <button class="expand-toggle"
                                                        [class.expanded]="expandedFolders().includes(item.id)">
                                                    <fa-icon icon="chevron-down"></fa-icon>
                                                </button>
                                            } @else {
                                                <div class="expand-spacer"></div>
                                            }
                                            <input
                                                type="checkbox"
                                                class="file-checkbox"
                                                [checked]="isItemSelected(item.id)"
                                                (change)="toggleItemSelection(item.id, $event)"
                                                (click)="$event.stopPropagation()"
                                            />
                                        </div>
                                        <div class="file-info">
                                            <fa-icon [icon]="item.icon" class="file-icon"></fa-icon>
                                            <span class="file-name">{{ item.name }}</span>
                                        </div>
                                    </div>
                                    @if (item.hasChildren && expandedFolders().includes(item.id)) {
                                        <div class="file-children">
                                            @for (child of item.children; track child.id) {
                                                <div class="file-item child-item">
                                                    <div class="file-row">
                                                        <div class="file-controls">
                                                            <div class="expand-spacer"></div>
                                                            <input
                                                                type="checkbox"
                                                                class="file-checkbox"
                                                                [checked]="isItemSelected(child.id)"
                                                                (change)="toggleItemSelection(child.id, $event)"
                                                            />
                                                        </div>
                                                        <div class="file-info">
                                                            <fa-icon [icon]="child.icon" class="file-icon"></fa-icon>
                                                            <span class="file-name">{{ child.name }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Destination Panel -->
                <div class="file-panel destination-panel">
                    <div class="panel-header">
                        <span class="panel-title">{{ selectedDestination() || 'Desktop001' }}</span>
                    </div>
                    <div class="panel-content">
                        <div class="empty-state">
                            <fa-icon icon="folder" class="empty-icon"></fa-icon>
                            <span class="empty-text">Destination folders will appear here</span>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        }

        <!-- ActiveStandby Section -->
        @if (isActiveStandby()) {
            <div class="activestandby-section">
                <div class="empty-state">
                    <fa-icon icon="shield" class="empty-icon"></fa-icon>
                    <span class="empty-text">ActiveStandby configuration will be available here</span>
                </div>
            </div>
        }

        <!-- Action Bar -->
        <div class="action-bar">
            @if (isFileReplication()) {
                <button
                    class="action-button primary"
                    [class.disabled]="!hasSelectedItems()"
                    [disabled]="!hasSelectedItems()"
                    (click)="selectSource('selected-source')"
                >
                    {{ hasSelectedItems() ? 'Confirm Selection' : 'Choose options first' }}
                </button>
            }
        </div>
    </section>
}
