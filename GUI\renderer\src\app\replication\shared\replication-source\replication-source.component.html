<!-- Panel Header -->
@if (!isPanelExpanded()) {
    <header (click)="expandPanel()">
        <h2>Replication Source & Destination</h2>
        <fa-icon icon="server"></fa-icon>
    </header>
}

<!-- Expanded Content -->
@else {
    <section>
        <!-- Source & Destination Selection -->
        <div class="connection-section">
            <div class="connection-row">
                <!-- Source Selection -->
                <div class="connection-group">
                    <fa-icon icon="desktop" class="connection-icon"></fa-icon>
                    <div class="connection-details">
                        <label class="connection-label">Choose Source</label>
                        <div class="connection-dropdown" (click)="onSourceChange($event)">
                            <span class="dropdown-value">{{ selectedSource() || 'Local' }}</span>
                            <fa-icon icon="chevron-down" class="dropdown-arrow"></fa-icon>
                        </div>
                    </div>
                </div>

                <!-- Destination Selection -->
                <div class="connection-group">
                    <fa-icon icon="desktop" class="connection-icon"></fa-icon>
                    <div class="connection-details">
                        <label class="connection-label">Choose Destination</label>
                        <div class="connection-dropdown" (click)="onDestinationChange($event)">
                            <span class="dropdown-value">{{ selectedDestination() || 'Desktop001' }}</span>
                            <fa-icon icon="chevron-down" class="dropdown-arrow"></fa-icon>
                        </div>
                    </div>
                </div>

                <!-- Disconnect Button -->
                <button
                    class="disconnect-button"
                    [class.disabled]="!canEstablishConnection()"
                    [disabled]="!canEstablishConnection()"
                >
                    Disconnect
                </button>
            </div>
        </div>

        <!-- Type Selection -->
        <div class="type-selection-section">
            <div class="type-options-horizontal">
                <button class="type-option-horizontal"
                        [class.selected]="selectedReplicationType() === 'ActiveStandby'"
                        (click)="setReplicationType('ActiveStandby')">
                    <div class="option-content-horizontal">
                        <div class="option-icon-horizontal">
                            <fa-icon icon="desktop" class="type-icon-horizontal"></fa-icon>
                        </div>
                        <div class="option-text-horizontal">
                            <div class="option-title-horizontal">Hypervisor VM</div>
                            <div class="option-description-horizontal">Hypervisor VM</div>
                        </div>
                    </div>
                </button>

                <button class="type-option-horizontal"
                        [class.selected]="selectedReplicationType() === 'File Replication'"
                        (click)="setReplicationType('File Replication')">
                    <div class="option-content-horizontal">
                        <div class="option-icon-horizontal">
                            <fa-icon icon="desktop" class="type-icon-horizontal"></fa-icon>
                        </div>
                        <div class="option-text-horizontal">
                            <div class="option-title-horizontal">Hypervisor VM</div>
                            <div class="option-description-horizontal">Hypervisor VM</div>
                        </div>
                    </div>
                </button>
            </div>
        </div>

        <!-- File Selection Section -->
        @if (isFileReplication()) {
            <div class="file-replication-section">
                <div class="file-table-container">
                    <div class="file-table-header">
                        <div class="header-cell file-name-header">File Name</div>
                        <div class="header-cell">Size</div>
                        <div class="header-cell">Modified</div>
                    </div>
                    <div class="file-table-body">
                        @for (item of fileReplicationItems; track item.id) {
                            <div class="file-table-row"
                                 [class.selected]="isItemSelected(item.id)"
                                 (click)="toggleItemSelection(item.id, $event)">
                                <div class="file-cell file-name-cell">
                                    <input
                                        type="checkbox"
                                        class="file-checkbox"
                                        [checked]="isItemSelected(item.id)"
                                        (change)="toggleItemSelection(item.id, $event)"
                                        (click)="$event.stopPropagation()"
                                    />
                                    <fa-icon [icon]="item.icon" class="file-icon"></fa-icon>
                                    <span class="file-name">{{ item.name }}</span>
                                </div>
                                <div class="file-cell">{{ item.size }}</div>
                                <div class="file-cell">{{ item.modified }}</div>
                            </div>
                        }
                    </div>
                </div>

                <div class="file-replication-actions">
                    <button
                        class="action-button"
                        [class.disabled]="!hasSelectedItems()"
                        [disabled]="!hasSelectedItems()"
                        (click)="confirmFileSelection()"
                    >
                        {{ hasSelectedItems() ? 'Confirm Selection' : 'Choose options first' }}
                    </button>
                </div>
            </div>
        }

        <!-- ActiveStandby Section -->
        @if (isActiveStandby()) {
            <div class="activestandby-section">
                <div class="empty-state">
                    <fa-icon icon="shield" class="empty-icon"></fa-icon>
                    <span class="empty-text">ActiveStandby configuration will be available here</span>
                </div>
            </div>
        }

        <!-- Action Bar -->
        <div class="action-bar">
            @if (isFileReplication()) {
                <button
                    class="action-button primary"
                    [class.disabled]="!hasSelectedItems()"
                    [disabled]="!hasSelectedItems()"
                    (click)="selectSource('selected-source')"
                >
                    {{ hasSelectedItems() ? 'Confirm Selection' : 'Choose options first' }}
                </button>
            }
        </div>
    </section>
}
