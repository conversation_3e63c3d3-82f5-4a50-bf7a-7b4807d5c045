<!-- Panel Header -->
@if (!isPanelExpanded()) {
    <header (click)="expandPanel()">
        <h2>Replication Source & Destination</h2>
        <fa-icon icon="server"></fa-icon>
    </header>
}

<!-- Expanded Content -->
@else {
    <section>
        <!-- Source & Destination Selection -->
        <div class="connection-section">
            <div class="connection-row">
                <!-- Source Selection -->
                <div class="connection-group">
                    <fa-icon icon="desktop" class="connection-icon"></fa-icon>
                    <div class="connection-details">
                        <label class="connection-label">Choose Source</label>
                        <div class="connection-dropdown" (click)="onSourceChange($event)">
                            <span class="dropdown-value">{{ selectedSource() || 'Local' }}</span>
                            <fa-icon icon="chevron-down" class="dropdown-arrow"></fa-icon>
                        </div>
                    </div>
                </div>

                <!-- Destination Selection -->
                <div class="connection-group">
                    <fa-icon icon="desktop" class="connection-icon"></fa-icon>
                    <div class="connection-details">
                        <label class="connection-label">Choose Destination</label>
                        <div class="connection-dropdown" (click)="onDestinationChange($event)">
                            <span class="dropdown-value">{{ selectedDestination() || 'Desktop001' }}</span>
                            <fa-icon icon="chevron-down" class="dropdown-arrow"></fa-icon>
                        </div>
                    </div>
                </div>

                <!-- Disconnect Button -->
                <button
                    class="disconnect-button"
                    [class.disabled]="!canEstablishConnection()"
                    [disabled]="!canEstablishConnection()"
                >
                    Disconnect
                </button>
            </div>
        </div>

        <!-- Type Selection -->
        <div class="type-selection-section">
            <div class="type-options-horizontal">
                <button class="type-option-horizontal"
                        [class.selected]="selectedReplicationType() === 'ActiveStandby'"
                        (click)="setReplicationType('ActiveStandby')">
                    <div class="option-content-horizontal">
                        <div class="option-icon-horizontal">
                            <fa-icon icon="desktop" class="type-icon-horizontal"></fa-icon>
                        </div>
                        <div class="option-text-horizontal">
                            <div class="option-title-horizontal">Hypervisor VM</div>
                            <div class="option-description-horizontal">Hypervisor VM</div>
                        </div>
                    </div>
                </button>

                <button class="type-option-horizontal"
                        [class.selected]="selectedReplicationType() === 'File Replication'"
                        (click)="setReplicationType('File Replication')">
                    <div class="option-content-horizontal">
                        <div class="option-icon-horizontal">
                            <fa-icon icon="desktop" class="type-icon-horizontal"></fa-icon>
                        </div>
                        <div class="option-text-horizontal">
                            <div class="option-title-horizontal">Hypervisor VM</div>
                            <div class="option-description-horizontal">Hypervisor VM</div>
                        </div>
                    </div>
                </button>
            </div>
        </div>

        <!-- File Replication Section -->
        @if (isFileReplication()) {
            <div class="file-replication-container">
                <!-- File Table -->
                <div class="file-table-section">
                    <div class="file-table-header">
                        <div class="table-header-cell file-name-column">File Name</div>
                        <div class="table-header-cell">Size</div>
                        <div class="table-header-cell">Modified</div>
                    </div>

                    <!-- Empty rows as per Figma design -->
                    @for (row of emptyTableRows; track $index) {
                        <div class="file-table-row">
                            <div class="table-cell file-name-column"></div>
                            <div class="table-cell"></div>
                            <div class="table-cell"></div>
                        </div>
                    }
                </div>

                <!-- Action Button -->
                <div class="file-replication-actions">
                    <button class="choose-options-button"
                            [class.disabled]="!hasSelectedItems()"
                            [disabled]="!hasSelectedItems()">
                        <span>{{ hasSelectedItems() ? 'Confirm Selection' : 'Choose options first' }}</span>
                    </button>
                </div>
            </div>
        }

        <!-- ActiveStandby Section -->
        @if (isActiveStandby()) {
            <div class="activestandby-section">
                <div class="empty-state">
                    <fa-icon icon="shield" class="empty-icon"></fa-icon>
                    <span class="empty-text">ActiveStandby configuration will be available here</span>
                </div>
            </div>
        }

        <!-- Action Bar -->
        <div class="action-bar">
            @if (isFileReplication()) {
                <button
                    class="action-button primary"
                    [class.disabled]="!hasSelectedItems()"
                    [disabled]="!hasSelectedItems()"
                    (click)="selectSource('selected-source')"
                >
                    {{ hasSelectedItems() ? 'Confirm Selection' : 'Choose options first' }}
                </button>
            }
        </div>
    </section>
}
