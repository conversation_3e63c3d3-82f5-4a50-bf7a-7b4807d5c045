/**
 * Post-build script - Prepare files for production environment
 * <AUTHOR>
 */
import { resolve, dirname } from 'path';
import { existsSync, readFileSync, writeFileSync } from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Directory definitions
const rootDir = resolve(__dirname, '..');
const distDir = resolve(rootDir, 'dist');
const rendererDir = resolve(distDir, 'browser');

// Ensure renderer directory exists
if (!existsSync(rendererDir)) {
    console.error('Error: renderer directory not found. Please build the renderer first.');
    process.exit(1);
}

// Create simplified package.json
console.log('Creating production package.json...');

// Read original package.json to get version number
const originalPackageJson = JSON.parse(readFileSync(resolve(rootDir, 'package.json'), 'utf-8'));

// Create minimized package.json
const productionPackageJson = {
    name: 'backup-and-standby',
    productName: 'Actiphy Backup and Standby',
    version: originalPackageJson.version,
    description: 'Actiphy Backup and Standby Application',
    main: 'main/index.js',
    private: true,
};

// Write new package.json
writeFileSync(
    resolve(distDir, 'package.json'),
    JSON.stringify(productionPackageJson, null, 4),
    'utf-8'
);

console.log('Production package.json created successfully!');
console.log('Post-build process completed successfully!');
