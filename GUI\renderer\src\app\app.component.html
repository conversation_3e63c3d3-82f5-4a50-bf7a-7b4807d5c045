<header></header>
<main>
    <aside [class.collapsed]="isCollapsed()">
        <!-- Place logo at the top of sidebar -->
        <div class="logo" (click)="toggleSidebar()">
            <span class="title">Backup & Standby</span>
        </div>

        <nav>
            <div class="section">
                <a class="item" routerLink="/dashboard" routerLinkActive="active">
                    <fa-icon icon="gauge"></fa-icon>
                    <span>Dashboard</span>
                </a>
                <a class="item" routerLink="/activities" routerLinkActive="active">
                    <fa-icon icon="timeline"></fa-icon>
                    <span>Activities & Timeline</span>
                </a>
                <a class="item" routerLink="/tasks" routerLinkActive="active">
                    <fa-icon icon="calendar-check"></fa-icon>
                    <span>Tasks & Schedules</span>
                </a>
            </div>
            <div class="section">
                <a class="item" routerLink="/backup" routerLinkActive="active">
                    <fa-icon icon="server"></fa-icon>
                    <span>Backup</span>
                </a>
                <a class="item" routerLink="/standby" routerLinkActive="active">
                    <fa-layers [fixedWidth]="true">
                        <fa-icon icon="rotate"></fa-icon>
                        <fa-icon [inverse]="true" icon="1" transform="shrink-9"></fa-icon>
                    </fa-layers>
                    <span>Standby</span>
                </a>
                <a class="item" routerLink="/replication" routerLinkActive="active">
                    <fa-icon icon="copy"></fa-icon>
                    <span>Data Replication</span>
                </a>
                <a class="item" routerLink="/recovery" routerLinkActive="active">
                    <fa-icon icon="shield-halved"></fa-icon>
                    <span>Recovery</span>
                </a>
            </div>
            <div class="section">
                <a class="item" routerLink="/preferences" routerLinkActive="active">
                    <fa-icon icon="gear"></fa-icon>
                    <span>Preferences</span>
                </a>
                <a class="item" routerLink="/tools" routerLinkActive="active">
                    <fa-icon icon="wrench"></fa-icon>
                    <span>Tools</span>
                </a>
            </div>

            <div class="info">
                @if (isCollapsed()) {
                    <fa-icon icon="info"></fa-icon>
                }
                @else {
                    <h4>Statistics</h4>
                    <p>
                        IP Address: *************<br>
                        item 02<br>
                        item 03<br>
                    </p>
                }
            </div>
        </nav>
    </aside>
    <section>
        <router-outlet></router-outlet>
    </section>
</main>

@if (isDashboard$ | async) {
    <footer [@slideUpDown]>
        <div class="container">
            <div class="left">
                <div class="logo">
                    <img src="icons/logo-full.svg" alt="Actiphy Logo">
                </div>
                <div class="copyright">
                    <span>© 2025 Actiphy. All rights reserved.</span>
                </div>
            </div>
            <div class="links">
                <a href="#">Privacy Policy</a>
                <a href="#">Terms of Service</a>
                <a href="#">Contact Support</a>
            </div>
        </div>
    </footer>
}
