import { Component, signal } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { ReplicationSourceComponent } from './shared/replication-source/replication-source.component';
import { ReplicationScheduleComponent, ScheduleConfigType } from './shared/replication-schedule/replication-schedule.component';

@Component({
    selector: 'app-replication',
    imports: [
        FontAwesomeModule,
        ReplicationSourceComponent,
        ReplicationScheduleComponent
    ],
    templateUrl: './replication.component.html',
    styleUrl: './replication.component.less'
})
export class ReplicationComponent {
    // State signals
    selectedSource = signal<string | null>(null);
    selectedDestination = signal<string | null>(null);
    selectedScheduleConfig = signal<ScheduleConfigType | null>(null);
    expandedPanel = signal<'source' | 'schedule' | null>('source');

    // Button state
    isButtonEnabled(): boolean {
        return this.selectedSource() !== null &&
               this.selectedScheduleConfig() !== null;
    }

    // Event handlers
    onSourceSelected(sourceId: string): void {
        this.selectedSource.set(sourceId);
        this.expandedPanel.set('schedule');
    }

    onDestinationSelected(destinationId: string): void {
        this.selectedDestination.set(destinationId);
    }

    onScheduleConfigChanged(config: ScheduleConfigType): void {
        this.selectedScheduleConfig.set(config);
        // Keep schedule panel expanded or collapse all
        // this.expandedPanel.set(null);
    }

    onPanelExpand(panel: 'source' | 'schedule'): void {
        this.expandedPanel.set(panel);
    }

    resetSelections(): void {
        this.selectedSource.set(null);
        this.selectedDestination.set(null);
        this.selectedScheduleConfig.set(null);
        this.expandedPanel.set('source');
    }
}
