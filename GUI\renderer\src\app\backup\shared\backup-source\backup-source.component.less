@import '../../../../styles/variables.less';

:host {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 1rem;
    height: 100%;
    width: 5rem;
    border-radius: .75rem;
    opacity: 0;
    overflow: hidden; // Prevent content overflow
    background: rgba(255, 255, 255, 0.1);

    // Panel Header
    header {
        flex: 0 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        padding-bottom: 1rem;

        h2 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
            line-height: 1.125rem;
            color: @text-color;
        }
    }

    // Panel Content
    section {
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: repeat(3, 1fr);
        gap: 1rem;
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        grid-auto-rows: min-content;
        min-height: 0; // Allow component to shrink

        // Hide scrollbar for Chromium/WebKit (Electron)
        &::-webkit-scrollbar {
            display: none;
        }

        // Panel Items
        .item {
            display: flex;
            align-items: center;
            gap: .75rem;
            background: rgba(255, 255, 255, 0);
            padding: .75rem;
            border-radius: .5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: background 0.25s ease, border-color 0.25s ease;
            flex-shrink: 0;

            &:hover {
                background: rgba(255, 255, 255, 0.05);
                border-color: rgba(255, 255, 255, 0.2);
            }

            &.selected {
                border-color: rgba(16, 185, 129, 0.6);
                background: rgba(16, 185, 129, 0.08);
                box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);

                // Expanded state styling for source panel
                &.expanded {
                    flex-direction: column;
                    align-items: flex-start;
                    height: 100%;
                    min-height: 200px;
                    padding: 10px 24px 24px 10px;
                    transform: none;
                    display: flex;
                    flex: 1;
                    grid-column: 1 / span 2;
                    grid-row: 1;

                    .item-header-row {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        width: 100%;
                        padding: 10px 0;

                        .item-icon {
                            width: 40px;
                            height: 40px;
                        }

                        .item-content {
                            padding: 0;
                            gap: 0;

                            h3 {
                                font-size: 16px;
                                font-weight: 500;
                                margin: 0;
                                line-height: 1.25;
                            }
                        }
                    }

                    .expanded-form {
                        width: 100%;
                        padding-left: 40px;

                        .form-row {
                            margin-bottom: 16px;

                            .dropdown-select {
                                background: rgba(255, 255, 255, 0.2);
                                border: 1px solid rgba(255, 255, 255, 0.1);
                                border-radius: 4px;
                                padding: 5px 10px;
                                color: @text-color;
                                font-size: 12px;
                                line-height: 1.67;
                                width: 223px;
                                height: 29px;
                                box-sizing: border-box;
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                cursor: pointer;

                                .dropdown-text {
                                    color: @text-color;
                                    font-weight: 500;
                                }

                                .dropdown-arrow {
                                    width: 10px;
                                    height: 9px;
                                    background: #D9D9D9;
                                    clip-path: polygon(50% 70%, 0% 0%, 100% 0%);
                                }
                            }
                        }

                        .file-select-table {
                            background: rgba(16, 185, 129, 0.2);
                            border: 1px solid rgba(16, 185, 129, 0.5);
                            border-radius: 8px;
                            padding: 0;
                            width: 100%;
                            min-height: 150px;
                            overflow: hidden;

                            .table-header {
                                background: rgba(255, 255, 255, 0.05);
                                padding: 0;
                                display: flex;
                                border-bottom: 1px solid rgba(16, 185, 129, 0.5);

                                .header-cell {
                                    padding: 0 0 0 10px;
                                    font-size: 12px;
                                    line-height: 1.36;
                                    color: @text-color;
                                    text-align: left;
                                    height: 31px;
                                    display: flex;
                                    align-items: center;
                                    font-weight: 400;

                                    &:first-child {
                                        width: 175px;
                                        justify-content: flex-start;
                                    }

                                    &:nth-child(2) {
                                        width: 175px;
                                        border-left: 1px solid rgba(16, 185, 129, 0.5);
                                    }

                                    &:nth-child(3) {
                                        width: 138px;
                                        border-left: 1px solid rgba(16, 185, 129, 0.5);
                                    }

                                    &:nth-child(4) {
                                        width: 135px;
                                        border-left: 1px solid rgba(16, 185, 129, 0.5);
                                    }

                                    &:last-child {
                                        flex: 1;
                                        border-left: 1px solid rgba(16, 185, 129, 0.5);
                                    }
                                }
                            }

                            .table-row {
                                display: flex;
                                border-bottom: 1px solid rgba(16, 185, 129, 0.5);

                                &:last-child {
                                    border-bottom: none;
                                }

                                .row-cell {
                                    padding: 0 0 0 10px;
                                    font-size: 12px;
                                    line-height: 1.36;
                                    color: @text-color;
                                    height: 31px;
                                    display: flex;
                                    align-items: center;
                                    font-weight: 400;

                                    &:first-child {
                                        width: 177px;

                                        .expand-icon {
                                            width: 15px;
                                            height: 15px;
                                            background: rgba(255, 255, 255, 0.2);
                                            border-radius: 3px;
                                            margin-right: 10px;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            cursor: pointer;
                                            flex-shrink: 0;

                                            &::after {
                                                content: '';
                                                width: 12px;
                                                height: 12px;
                                                background: #D9D9D9;
                                                border-radius: 2px;
                                                clip-path: polygon(50% 20%, 20% 80%, 80% 80%);
                                            }
                                        }
                                    }

                                    &:nth-child(2) {
                                        width: 175px;
                                        border-left: 1px solid rgba(16, 185, 129, 0.5);
                                    }

                                    &:nth-child(3) {
                                        width: 140px;
                                        border-left: 1px solid rgba(16, 185, 129, 0.5);
                                    }

                                    &:last-child {
                                        border-left: 1px solid rgba(16, 185, 129, 0.5);
                                    }
                                }
                            }
                        }

                        .form-actions {
                            display: flex;
                            justify-content: flex-end;
                            padding-top: 16px;

                            .save-button {
                                background: @primary-gradient;
                                border: none;
                                border-radius: 8px;
                                color: @text-color;
                                font-family: 'Noto Sans', sans-serif;
                                font-size: 14px;
                                font-weight: 400;
                                line-height: 1.36;
                                text-align: center;
                                padding: 8px 12px;
                                width: 194px;
                                height: 34px;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                box-sizing: border-box;

                                &:hover {
                                    box-shadow: 0 4px 8px rgba(239, 78, 56, 0.3);
                                }

                                &:active {
                                    box-shadow: 0 2px 4px rgba(239, 78, 56, 0.3);
                                }

                                &:focus {
                                    outline: 2px solid rgba(239, 78, 56, 0.5);
                                    outline-offset: 2px;
                                }
                            }
                        }
                    }
                }
            }

            .item-icon {
                font-size: 3rem;
                padding: 1rem;
            }

            .item-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                gap: .5em;

                .item-header {
                    h3 {
                        font-size: 1rem;
                        font-weight: 500;
                        margin: 0;
                        line-height: 1.375;
                        color: @text-color;
                    }

                    p {
                        font-size: 0.875rem;
                        color: @text-secondary;
                        margin: 0;
                        line-height: 1.375;
                        opacity: 0.8;
                        margin-top: .25rem;
                    }
                }
            }
        }

        // Compact style for non-selected items when one is expanded
        .item.compact {
            height: 74px;

            .item-icon {
                width: 52px;
                height: 52px;
            }

            .item-content {
                padding: 8px 0;
                gap: 4px;

                .item-header {
                    h3 {
                        font-size: 16px;
                        font-weight: 500;
                        line-height: 1.36;
                    }

                    p {
                        font-size: 13px;
                        line-height: 1.36;
                        margin-top: 0;
                    }
                }
            }
        }
    }

    // Collapsed vertical panel styling
    &.collapsed {
        header {
            writing-mode: vertical-lr;
            text-orientation: mixed;
            padding: 0;
            height: 100%;
            flex-direction: row-reverse;
            justify-content: flex-end;

            h2 {
                font-size: 1rem;
                font-weight: 600;
                line-height: 1.2;
                text-align: center;
                margin: 0;
                writing-mode: vertical-lr;
                text-orientation: mixed;
                color: @text-color;
                letter-spacing: -0.02em;
                transform: rotate(180deg);
            }

            .icon-container {
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                border-radius: 6px;
                background: rgba(255, 255, 255, 0);
                padding: 0;
            }
        }

        section {
            display: none;
        }
    }
}
