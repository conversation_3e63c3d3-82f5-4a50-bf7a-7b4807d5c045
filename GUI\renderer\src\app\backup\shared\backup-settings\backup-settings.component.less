@import '../../../../styles/variables.less';

:host {
    flex: 1;
    height: 100%;
    width: 5rem;
    border-radius: .75rem;
    opacity: 1;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.1);

    .container {
        padding: 1rem;
        display: flex;
        flex-direction: column;
        height: 100%;

        // Panel Header
        header {
            flex: 0 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
            padding-bottom: 1rem;

            h2 {
                font-size: 1.125rem;
                font-weight: 600;
                margin: 0;
                line-height: 1.125rem;
                color: @text-color;
            }
        }

        section {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-self: stretch;
            overflow-y: auto;
            min-height: 0;
            container-type: inline-size;

            // Hide scrollbar for Electron (Chromium-based)
            &::-webkit-scrollbar {
                display: none;
            }

            .columns {
                display: grid;
                grid-template-columns: 1fr;
                gap: 1rem;
                align-self: stretch;

                .column {
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: .5rem;
                    padding: 1rem;

                    h3 {
                        font-weight: 400;
                        font-size: 1rem;
                        margin-bottom: 1rem;
                        text-transform: uppercase;
                    }
                }

            }

            // Container query: two columns when container is 56rem or wider
            @container (min-width: 56rem) {
                .columns {
                    grid-template-columns: repeat(2, 1fr);
                }
            }

            .compression,
            .password-protection,
            .destination-isolation,
            .effective-dates,
            .execution-settings,
            .retention-policy,
            .reconcile-image,
            .notification,
            .execution-priority {
                margin-bottom: 1rem;

                .form-group {
                    margin-bottom: .5rem;
                }

                .options {
                    padding-left: 1.5rem;
                }

                input[type="number"] {
                    width: 5rem;
                }
            }

            .dedup-slider .form-range-labels:last-child {
                order: -1;
            }
        }

        footer {
            flex: 0 0 auto;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 1rem;
            padding-top: 1rem;

            .form-button {
                min-width: 12rem;
            }
        }
    }
}
