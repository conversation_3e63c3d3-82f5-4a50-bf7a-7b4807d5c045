<!-- Header Section -->
<header>
    <div class="title">
        <h1>Preferences</h1>
        <p>Configure Application Preferences, predefined Cloud & Hypervisors, Storage Destinations and Network connected Client Agents here</p>
    </div>
</header>

<!-- Main Content Container -->
<section>
    <!-- Left Navigation Panel -->
    <nav>
        <a class="item" [style.--nav-index]="0"
            [routerLink]="['/preferences', 'general']"
            [class.active]="activeRoute === 'general'"
            routerLinkActive="active">
            <fa-icon icon="sliders"></fa-icon>
            <span>General Settings</span>
        </a>

        <a class="item" [style.--nav-index]="1"
            [routerLink]="['/preferences', 'proxy']"
            [class.active]="activeRoute === 'proxy'"
            routerLinkActive="active">
            <fa-icon icon="globe"></fa-icon>
            <span>Proxy</span>
        </a>

        <a class="item" [style.--nav-index]="2"
            [routerLink]="['/preferences', 'notifications']"
            [class.active]="activeRoute === 'notifications'"
            routerLinkActive="active">
            <fa-icon icon="bell"></fa-icon>
            <span>Notifications</span>
        </a>

        <a class="item" [style.--nav-index]="3"
            [routerLink]="['/preferences', 'backup']"
            [class.active]="activeRoute === 'backup'"
            routerLinkActive="active">
            <fa-icon icon="server"></fa-icon>
            <span>Backup</span>
        </a>

        <a class="item" [style.--nav-index]="4"
            [routerLink]="['/preferences', 'alert']"
            [class.active]="activeRoute === 'alert'"
            routerLinkActive="active">
            <fa-icon icon="exclamation-triangle"></fa-icon>
            <span>Alert</span>
        </a>

        <a class="item" [style.--nav-index]="5"
            [routerLink]="['/preferences', 'console']"
            [class.active]="activeRoute === 'console'"
            routerLinkActive="active">
            <fa-icon icon="square-terminal"></fa-icon>
            <span>Console</span>
        </a>

        <a class="item" [style.--nav-index]="6"
            [routerLink]="['/preferences', 'hyperback']"
            [class.active]="activeRoute === 'hyperback'"
            routerLinkActive="active">
            <fa-icon icon="rocket"></fa-icon>
            <span>Hyperback</span>
        </a>

        <a class="item" [style.--nav-index]="7"
            [routerLink]="['/preferences', 'online-portal']"
            [class.active]="activeRoute === 'online-portal'"
            routerLinkActive="active">
            <fa-icon icon="external-link-alt"></fa-icon>
            <span>Online Portal</span>
        </a>

        <a class="item" [style.--nav-index]="8"
            [routerLink]="['/preferences', 'quick-recovery']"
            [class.active]="activeRoute === 'quick-recovery'"
            routerLinkActive="active">
            <fa-icon icon="undo-alt"></fa-icon>
            <span>Quick Recovery</span>
        </a>

        <a class="item" [style.--nav-index]="9"
            [routerLink]="['/preferences', 'rescue-boot']"
            [class.active]="activeRoute === 'rescue-boot'"
            routerLinkActive="active">
            <fa-icon icon="boot"></fa-icon>
            <span>Rescue Boot</span>
        </a>
    </nav>

    <!-- Right Content Panel -->
    <div class="main">
        <router-outlet></router-outlet>
    </div>
</section>
