{"version": 3, "sources": ["renderer/src/app/backup/shared/backup-settings/backup-settings.component.less"], "sourcesContent": ["@import '../../../../styles/variables.less';\n\n:host {\n    flex: 1;\n    height: 100%;\n    width: 5rem;\n    border-radius: .75rem;\n    opacity: 1;\n    overflow: hidden;\n    background: rgba(255, 255, 255, 0.1);\n\n    .container {\n        padding: 1rem;\n        display: flex;\n        flex-direction: column;\n        height: 100%;\n\n        // Panel Header\n        header {\n            flex: 0 0 auto;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            gap: 1rem;\n            padding-bottom: 1rem;\n\n            h2 {\n                font-size: 1.125rem;\n                font-weight: 600;\n                margin: 0;\n                line-height: 1.125rem;\n                color: @text-color;\n            }\n        }\n\n        section {\n            flex: 1 1 auto;\n            display: flex;\n            flex-direction: column;\n            gap: 1rem;\n            align-self: stretch;\n            overflow-y: auto;\n            min-height: 0;\n            container-type: inline-size;\n\n            // Hide scrollbar for Electron (Chromium-based)\n            &::-webkit-scrollbar {\n                display: none;\n            }\n\n            .columns {\n                display: grid;\n                grid-template-columns: 1fr;\n                gap: 1rem;\n                align-self: stretch;\n\n                .column {\n                    border: 1px solid rgba(255, 255, 255, 0.1);\n                    border-radius: .5rem;\n                    padding: 1rem;\n\n                    h3 {\n                        font-weight: 400;\n                        font-size: 1rem;\n                        margin-bottom: 1rem;\n                        text-transform: uppercase;\n                    }\n                }\n\n            }\n\n            // Container query: two columns when container is 56rem or wider\n            @container (min-width: 56rem) {\n                .columns {\n                    grid-template-columns: repeat(2, 1fr);\n                }\n            }\n\n            .compression,\n            .password-protection,\n            .destination-isolation,\n            .effective-dates,\n            .execution-settings,\n            .retention-policy,\n            .reconcile-image,\n            .notification,\n            .execution-priority {\n                margin-bottom: 1rem;\n\n                .form-group {\n                    margin-bottom: .5rem;\n                }\n\n                .options {\n                    padding-left: 1.5rem;\n                }\n\n                input[type=\"number\"] {\n                    width: 5rem;\n                }\n            }\n\n            .dedup-slider .form-range-labels:last-child {\n                order: -1;\n            }\n        }\n\n        footer {\n            flex: 0 0 auto;\n            display: flex;\n            justify-content: flex-end;\n            align-items: center;\n            gap: 1rem;\n            padding-top: 1rem;\n\n            .form-button {\n                min-width: 12rem;\n            }\n        }\n    }\n}\n"], "mappings": ";AAEA;AACI,QAAA;AACA,UAAA;AACA,SAAA;AACA,iBAAA;AACA,WAAA;AACA,YAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAPJ,MASI,CAAA;AACI,WAAA;AACA,WAAA;AACA,kBAAA;AACA,UAAA;;AAbR,MASI,CAAA,UAOI;AACI,QAAA,EAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,kBAAA;;AAtBZ,MASI,CAAA,UAOI,OAQI;AACI,aAAA;AACA,eAAA;AACA,UAAA;AACA,eAAA;AACA,SAAA;;AA7BhB,MASI,CAAA,UAwBI;AACI,QAAA,EAAA,EAAA;AACA,WAAA;AACA,kBAAA;AACA,OAAA;AACA,cAAA;AACA,cAAA;AACA,cAAA;AACA,kBAAA;;AAGA,MAnCR,CAAA,UAwBI,OAWK;AACG,WAAA;;AA7ChB,MASI,CAAA,UAwBI,QAeI,CAAA;AACI,WAAA;AACA,yBAAA;AACA,OAAA;AACA,cAAA;;AApDhB,MASI,CAAA,UAwBI,QAeI,CAAA,QAMI,CAAA;AACI,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,WAAA;;AAzDpB,MASI,CAAA,UAwBI,QAeI,CAAA,QAMI,CAAA,OAKI;AACI,eAAA;AACA,aAAA;AACA,iBAAA;AACA,kBAAA;;AAOZ,WAAA,CAA8B,SAAA,EAAA;AAA9B,QA7DR,CAAA,UAwBI,QAsCQ,CAvBJ;AAwBQ,2BAAuB,OAAA,CAAA,EAAA;;;AAxE3C,MASI,CAAA,UAwBI,QA2CI,CAAA;AA5EZ,MASI,CAAA,UAwBI,QA4CI,CAAA;AA7EZ,MASI,CAAA,UAwBI,QA6CI,CAAA;AA9EZ,MASI,CAAA,UAwBI,QA8CI,CAAA;AA/EZ,MASI,CAAA,UAwBI,QA+CI,CAAA;AAhFZ,MASI,CAAA,UAwBI,QAgDI,CAAA;AAjFZ,MASI,CAAA,UAwBI,QAiDI,CAAA;AAlFZ,MASI,CAAA,UAwBI,QAkDI,CAAA;AAnFZ,MASI,CAAA,UAwBI,QAmDI,CAAA;AACI,iBAAA;;AArFhB,MASI,CAAA,UAwBI,QA2CI,CAAA,YAWI,CAAA;AAvFhB,MASI,CAAA,UAwBI,QA4CI,CAAA,oBAUI,CAAA;AAvFhB,MASI,CAAA,UAwBI,QA6CI,CAAA,sBASI,CAAA;AAvFhB,MASI,CAAA,UAwBI,QA8CI,CAAA,gBAQI,CAAA;AAvFhB,MASI,CAAA,UAwBI,QA+CI,CAAA,mBAOI,CAAA;AAvFhB,MASI,CAAA,UAwBI,QAgDI,CAAA,iBAMI,CAAA;AAvFhB,MASI,CAAA,UAwBI,QAiDI,CAAA,gBAKI,CAAA;AAvFhB,MASI,CAAA,UAwBI,QAkDI,CAAA,aAII,CAAA;AAvFhB,MASI,CAAA,UAwBI,QAmDI,CAAA,mBAGI,CAAA;AACI,iBAAA;;AAxFpB,MASI,CAAA,UAwBI,QA2CI,CAAA,YAeI,CAAA;AA3FhB,MASI,CAAA,UAwBI,QA4CI,CAAA,oBAcI,CAAA;AA3FhB,MASI,CAAA,UAwBI,QA6CI,CAAA,sBAaI,CAAA;AA3FhB,MASI,CAAA,UAwBI,QA8CI,CAAA,gBAYI,CAAA;AA3FhB,MASI,CAAA,UAwBI,QA+CI,CAAA,mBAWI,CAAA;AA3FhB,MASI,CAAA,UAwBI,QAgDI,CAAA,iBAUI,CAAA;AA3FhB,MASI,CAAA,UAwBI,QAiDI,CAAA,gBASI,CAAA;AA3FhB,MASI,CAAA,UAwBI,QAkDI,CAAA,aAQI,CAAA;AA3FhB,MASI,CAAA,UAwBI,QAmDI,CAAA,mBAOI,CAAA;AACI,gBAAA;;AA5FpB,MASI,CAAA,UAwBI,QA2CI,CAAA,YAmBI,KAAK,CAAA;AA/FrB,MASI,CAAA,UAwBI,QA4CI,CAAA,oBAkBI,KAAK,CAAA;AA/FrB,MASI,CAAA,UAwBI,QA6CI,CAAA,sBAiBI,KAAK,CAAA;AA/FrB,MASI,CAAA,UAwBI,QA8CI,CAAA,gBAgBI,KAAK,CAAA;AA/FrB,MASI,CAAA,UAwBI,QA+CI,CAAA,mBAeI,KAAK,CAAA;AA/FrB,MASI,CAAA,UAwBI,QAgDI,CAAA,iBAcI,KAAK,CAAA;AA/FrB,MASI,CAAA,UAwBI,QAiDI,CAAA,gBAaI,KAAK,CAAA;AA/FrB,MASI,CAAA,UAwBI,QAkDI,CAAA,aAYI,KAAK,CAAA;AA/FrB,MASI,CAAA,UAwBI,QAmDI,CAAA,mBAWI,KAAK,CAAA;AACD,SAAA;;AAhGpB,MASI,CAAA,UAwBI,QAmEI,CAAA,aAAc,CAAA,iBAAkB;AAC5B,SAAA;;AArGhB,MASI,CAAA,UAgGI;AACI,QAAA,EAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,eAAA;;AA/GZ,MASI,CAAA,UAgGI,OAQI,CAAA;AACI,aAAA;;", "names": []}