{"version": 3, "sources": ["renderer/src/app/backup/shared/backup-schedule/backup-schedule.component.less"], "sourcesContent": ["@import '../../../../styles/variables.less';\n\n:host {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    padding: 1rem;\n    height: 100%;\n    width: 5rem;\n    border-radius: .75rem;\n    opacity: 0;\n    overflow: hidden; // Prevent content overflow\n    background: rgba(255, 255, 255, 0.1);\n\n    // Panel Header\n    header {\n        flex: 0 0 auto;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        gap: 1rem;\n        padding-bottom: 1rem;\n\n        h2 {\n            font-size: 1.125rem;\n            font-weight: 600;\n            margin: 0;\n            line-height: 1.125rem;\n            color: @text-color;\n        }\n    }\n\n    // Panel Content\n    section {\n        display: grid;\n        grid-template-columns: 1fr 1fr;\n        grid-template-rows: 1fr 1fr;\n        gap: 1rem;\n        flex: 1;\n        overflow-y: auto;\n        overflow-x: hidden;\n        padding-top: 4px;\n        grid-auto-rows: min-content;\n        min-height: 0; // Allow component to shrink\n\n        .item {\n            background: #ffffff0d;\n            border-radius: .5rem;\n            border: 1px solid rgba(255, 255, 255, 0.1);\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            padding: .75rem;\n            gap: 0.75em;\n            justify-content: center;\n            cursor: pointer;\n            transition: background 0.25s ease, border-color 0.25s ease;\n\n            &:hover {\n                background: rgba(255, 255, 255, 0.08);\n                border-color: rgba(255, 255, 255, 0.2);\n            }\n\n            &.selected {\n                border-color: rgba(16, 185, 129, 0.6);\n                background: rgba(16, 185, 129, 0.08);\n                box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n            }\n\n            .icon {\n                font-size: 3rem;\n            }\n\n            .name {\n                font-size: 1rem;\n                font-weight: 500;\n                color: @text-color;\n                text-align: center;\n                line-height: 1.25;\n                margin: 0;\n                white-space: nowrap;\n            }\n        }\n    }\n\n    // Collapsed vertical panel styling\n    &.collapsed {\n        header {\n            writing-mode: vertical-lr;\n            text-orientation: mixed;\n            padding: 0;\n            height: 100%;\n            flex-direction: row-reverse;\n            justify-content: flex-end;\n\n            h2 {\n                font-size: 1rem;\n                font-weight: 600;\n                line-height: 1.2;\n                text-align: center;\n                margin: 0;\n                writing-mode: vertical-lr;\n                text-orientation: mixed;\n                color: @text-color;\n                letter-spacing: -0.02em;\n                transform: rotate(180deg);\n            }\n\n            .icon-container {\n                width: 20px;\n                height: 20px;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                flex-shrink: 0;\n                border-radius: 6px;\n                background: rgba(255, 255, 255, 0);\n                padding: 0;\n            }\n        }\n\n        section {\n            display: none;\n        }\n    }\n}\n"], "mappings": ";AAEA;AACI,QAAA;AACA,WAAA;AACA,kBAAA;AACA,WAAA;AACA,UAAA;AACA,SAAA;AACA,iBAAA;AACA,WAAA;AACA,YAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAVJ,MAaI;AACI,QAAA,EAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,kBAAA;;AAnBR,MAaI,OAQI;AACI,aAAA;AACA,eAAA;AACA,UAAA;AACA,eAAA;AACA,SAAA;;AA1BZ,MA+BI;AACI,WAAA;AACA,yBAAA,IAAA;AACA,sBAAA,IAAA;AACA,OAAA;AACA,QAAA;AACA,cAAA;AACA,cAAA;AACA,eAAA;AACA,kBAAA;AACA,cAAA;;AAzCR,MA+BI,QAYI,CAAA;AACI,cAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;AACA,kBAAA;AACA,eAAA;AACA,WAAA;AACA,OAAA;AACA,mBAAA;AACA,UAAA;AACA,cAAA,WAAA,MAAA,IAAA,EAAA,aAAA,MAAA;;AAEA,MAzBR,QAYI,CAAA,IAaK;AACG,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,gBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,MA9BR,QAYI,CAAA,IAkBK,CAAA;AACG,gBAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAhEhB,MA+BI,QAYI,CAAA,KAwBI,CAAA;AACI,aAAA;;AApEhB,MA+BI,QAYI,CAAA,KA4BI,CAAA;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,cAAA;AACA,eAAA;AACA,UAAA;AACA,eAAA;;AAMZ,KAAC,CAAA,UACG;AACI,gBAAA;AACA,oBAAA;AACA,WAAA;AACA,UAAA;AACA,kBAAA;AACA,mBAAA;;AAPR,KAAC,CAAA,UACG,OAQI;AACI,aAAA;AACA,eAAA;AACA,eAAA;AACA,cAAA;AACA,UAAA;AACA,gBAAA;AACA,oBAAA;AACA,SAAA;AACA,kBAAA;AACA,aAAW,OAAA;;AAnBvB,KAAC,CAAA,UACG,OAqBI,CAAA;AACI,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;;AA/BZ,KAAC,CAAA,UAmCG;AACI,WAAA;;", "names": []}