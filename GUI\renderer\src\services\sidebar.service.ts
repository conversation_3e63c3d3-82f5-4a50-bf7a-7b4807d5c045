import { Injectable, signal } from '@angular/core';

/**
 * Service to manage sidebar collapse/expand state using Angular signals.
 * This service provides a reactive way to control sidebar visibility across the entire application.
 */
@Injectable({
    providedIn: 'root'
})
export class SidebarService {
    private readonly collapsed = signal(false);

    /**
     * Read-only signal for components to subscribe to sidebar state changes.
     * Use this in templates with isCollapsed() or in computed signals.
     */
    readonly isCollapsed = this.collapsed.asReadonly();

    /**
     * Toggle the sidebar collapsed state
     */
    toggle(): void {
        this.collapsed.update(current => !current);
    }

    /**
     * Set the sidebar collapsed state
     * @param collapsed - Whether the sidebar should be collapsed
     */
    setCollapsed(collapsed: boolean): void {
        this.collapsed.set(collapsed);
    }

    /**
     * Collapse the sidebar
     */
    collapse(): void {
        this.collapsed.set(true);
    }

    /**
     * Expand the sidebar
     */
    expand(): void {
        this.collapsed.set(false);
    }
}
