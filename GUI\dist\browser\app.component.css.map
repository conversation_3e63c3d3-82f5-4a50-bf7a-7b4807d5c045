{"version": 3, "sources": ["renderer/src/app/app.component.less", "renderer/src/styles/variables.less", "renderer/src/styles/mixins.less"], "sourcesContent": ["@import '../styles/variables.less';\n@import '../styles/mixins.less';\n\n:host {\n    width: 100vw;\n    height: 100vh;\n    display: flex;\n    flex-direction: column;\n    background: @dark-bg;\n    color: @text-color;\n    font-family: @font-family;\n    overflow: hidden;\n\n    header {\n        height: 39px;\n        background-color: rgba(0, 0, 0, 0.15);\n        -webkit-app-region: drag; // Enable window dragging\n        -webkit-user-select: none; // Disable text selection\n        user-select: none; // Disable text selection for other browsers\n\n        // If you have any interactive elements inside header, apply this class to them\n        .no-drag {\n            -webkit-app-region: no-drag;\n        }\n    }\n\n    main {\n        display: flex;\n        width: 100%;\n        flex: 1;\n        overflow: hidden;\n        min-height: 0; // Ensure flex items can shrink properly\n\n        aside {\n            width: 16.5rem;\n            background: transparent;\n            border-right: .0625rem solid @border-color;\n            display: flex;\n            flex-direction: column;\n            overflow-y: auto; // Add scrolling capability\n            flex-shrink: 0; // Prevent sidebar from being compressed\n            padding: 1rem 0;\n            overflow: hidden;\n            transition: width 0.3s ease;\n\n            .logo {\n                display: flex;\n                align-items: center;\n                padding: 1rem;\n\n                .title {\n                    font-family: 'Noto Sans', sans-serif;\n                    font-weight: 300;\n                    font-size: 1.625rem;\n                    padding-bottom: 1rem;\n                    line-height: 1;\n                    color: @text-color;\n                    white-space: nowrap;\n                    transition: opacity 0.3s ease;\n                    cursor: pointer;\n                }\n            }\n\n            nav {\n                display: flex;\n                flex-direction: column;\n                gap: .75rem;\n                padding: 0 1rem;\n                flex: 1;\n\n                .section {\n                    padding-bottom: .75rem;\n                    border-bottom: 0.0625rem solid rgba(255, 255, 255, 0.2);\n                    display: flex;\n                    flex-direction: column;\n                    gap: 0.5rem;\n                    opacity: 0;\n\n                    .animation(fade-in-left);\n\n                    &:nth-child(1) {\n                        animation-delay: 0.05s;\n                    }\n\n                    &:nth-child(2) {\n                        animation-delay: 0.1s;\n                    }\n\n                    &:nth-child(3) {\n                        animation-delay: 0.15s;\n                    }\n\n                    &:last-child {\n                        border-bottom: none;\n                    }\n\n                    .item {\n                        display: flex;\n                        align-items: center;\n                        gap: .75rem;\n                        padding: .5rem .75rem;\n                        border-radius: @border-radius-small;\n                        cursor: pointer;\n                        position: relative;\n                        overflow: hidden;\n                        color: @text-color;\n                        text-decoration: none;\n                        outline: none;\n                        transition: background-color 0.3s ease, color 0.3s ease;\n\n                        &:hover {\n                            background-color: rgba(255, 255, 255, 0.15);\n                            text-decoration: none;\n                            color: @text-color;\n                        }\n\n                        &.active {\n                            background-color: rgba(255, 255, 255, 0.2);\n                            color: @text-color;\n\n                            &::before {\n                                content: '';\n                                position: absolute;\n                                left: 0;\n                                top: 0;\n                                height: 100%;\n                                width: 0.1875rem;\n                                background: @primary-color;\n                            }\n                        }\n\n                        span {\n                            font-family: 'Noto Sans', sans-serif;\n                            font-size: 1rem;\n                            font-weight: 400;\n                            white-space: nowrap;\n                            transition: opacity 0.3s ease;\n                        }\n                    }\n                }\n\n                .info {\n                    margin-top: auto;\n                    background-color: @light-overlay;\n                    padding: 1rem;\n                    border-radius: @border-radius-small;\n\n                    h4 {\n                        font-size: 1.125rem;\n                        font-weight: 400;\n                        margin: 0 0 .375rem 0;\n                    }\n\n                    p {\n                        font-size: 0.875rem;\n                        color: @text-secondary;\n                        line-height: 1.36;\n                        margin: 0;\n                    }\n\n                    fa-icon {\n                        display: none;\n                    }\n                }\n            }\n\n            &.collapsed {\n                width: 4.75rem;\n\n                .logo {\n                    overflow: hidden;\n\n                    .title {\n                        opacity: 0;\n                    }\n                }\n\n                nav {\n                    .section {\n                        overflow: hidden;\n\n                        .item {\n                            span {\n                                opacity: 0;\n                            }\n                        }\n                    }\n\n                    .info {\n                        display: flex;\n                        align-items: center;\n                        justify-content: center;\n\n                        fa-icon {\n                            display: inline-block;\n                        }\n                    }\n                }\n            }\n        }\n\n        section {\n            flex: 1;\n            overflow-y: auto;\n            min-height: 0; // Ensure proper scrolling\n        }\n    }\n\n    footer {\n        background-color: @light-overlay;\n        width: 100%;\n        height: 3.75rem;\n        flex-shrink: 0; // Prevent footer from being compressed\n        overflow: hidden;\n\n        .container {\n            padding: 1rem;\n            border-top: .0625rem solid @border-color;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n\n            .left {\n                display: flex;\n                align-items: center;\n                gap: 1rem;\n\n                .logo {\n                    width: 6rem;\n                    height: 1.5625rem;\n                    display: flex;\n                    align-items: center;\n\n                    img {\n                        width: 100%;\n                        height: auto;\n                        object-fit: contain;\n                    }\n                }\n\n                .copyright {\n                    font-size: 0.875rem;\n                    color: @text-secondary;\n                }\n            }\n\n            .links {\n                display: flex;\n                gap: 1.5rem;\n\n                a {\n                    color: @text-secondary;\n                    text-decoration: none;\n                    font-size: 0.875rem;\n\n                    &:hover {\n                        color: @text-color;\n                        text-decoration: underline;\n                    }\n                }\n            }\n        }\n    }\n}\n", "// variables\r\n@primary-color: #ef4e38;\r\n@primary-gradient: linear-gradient(90deg, #ef4e38 0%, #b61e0e 100%);\r\n@success-color: #34d399;\r\n@success-bg: rgba(16, 185, 129, 0.2);\r\n@dark-bg: linear-gradient(284deg, #152b34 0%, #254b5c 100%);\r\n@background-dark: #1a2a33;\r\n@bg-overlay: rgba(255, 255, 255, 0.1);\r\n@light-overlay: rgba(255, 255, 255, 0.05);\r\n@border-color: rgba(255, 255, 255, 0.1);\r\n@text-color: rgba(255, 255, 255, 1);\r\n@text-secondary: rgba(255, 255, 255, 0.5);\r\n@border-radius: .75rem;\r\n@border-radius-small: .5rem;\r\n@font-family: 'inter', 'noto sans', sans-serif;\r\n", "/**\n * Mixin\n * <AUTHOR>\n */\n\n@import \"variables.less\";\n\n// Transition\n.transition(...) {\n    & when (length(@arguments) = 0) {\n        transition: none;\n    }\n\n    & when (length(@arguments) = 1) {\n        transition: @arguments 300ms ease;\n    }\n\n    & when (length(@arguments) > 1) {\n        @properties: replace(\"@{arguments}\", \" \", \", \", \"g\");\n        transition-property: ~\"@{properties}\";\n        transition-timing-function: ease;\n        transition-duration: 300ms;\n    }\n}\n\n// Animations\n.animation(@name, @delay: 0ms) {\n    animation-duration: 500ms;\n    animation-fill-mode: forwards;\n    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n    animation-delay: @delay;\n\n    & when (@name = fade-in-up) {\n        animation-name: fadeInUp;\n    }\n\n    & when (@name = fade-in-left) {\n        animation-name: fadeInLeft;\n    }\n\n    & when (@name = fade-in) {\n        animation-name: fadeIn;\n    }\n}\n"], "mappings": ";AAGA;AACI,SAAA;AACA,UAAA;AACA,WAAA;AACA,kBAAA;AACA;ICHC;MAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;ADID,SAAA;AACA;ICIJ,OAAA;IAAS,WAAA;IAAA;ADHL,YAAA;;AARJ,MAUI;AACI,UAAA;AACA,oBAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,sBAAA;AACA,uBAAA;AACA,eAAA;;AAfR,MAUI,OAQI,CAAA;AACI,sBAAA;;AAnBZ,MAuBI;AACI,WAAA;AACA,SAAA;AACA,QAAA;AACA,YAAA;AACA,cAAA;;AA5BR,MAuBI,KAOI;AACI,SAAA;AACA,cAAA;AACA,gBAAA,UAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;AACA,kBAAA;AACA,cAAA;AACA,eAAA;AACA,WAAA,KAAA;AACA,YAAA;AACA,cAAA,MAAA,KAAA;;AAxCZ,MAuBI,KAOI,MAYI,CAAA;AACI,WAAA;AACA,eAAA;AACA,WAAA;;AA7ChB,MAuBI,KAOI,MAYI,CAAA,KAKI,CAAA;AACI,eAAa,WAAA,EAAA;AACb,eAAA;AACA,aAAA;AACA,kBAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;AACA,cAAA,QAAA,KAAA;AACA,UAAA;;AAxDpB,MAuBI,KAOI,MA8BI;AACI,WAAA;AACA,kBAAA;AACA,OAAA;AACA,WAAA,EAAA;AACA,QAAA;;AAjEhB,MAuBI,KAOI,MA8BI,IAOI,CAAA;AACI,kBAAA;AACA,iBAAA,UAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;AACA,kBAAA;AACA,OAAA;AACA,WAAA;AEjDhB,sBAAA;AACA,uBAAA;AACA,6BAA2B,aAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA;AAC3B,mBAAA;AAOI,kBAAA;;AF2CY,MAtDhB,KAOI,MA8BI,IAOI,CAAA,OAUK;AACG,mBAAA;;AAGJ,MA1DhB,KAOI,MA8BI,IAOI,CAAA,OAcK;AACG,mBAAA;;AAGJ,MA9DhB,KAOI,MA8BI,IAOI,CAAA,OAkBK;AACG,mBAAA;;AAGJ,MAlEhB,KAOI,MA8BI,IAOI,CAAA,OAsBK;AACG,iBAAA;;AA1FxB,MAuBI,KAOI,MA8BI,IAOI,CAAA,QA0BI,CAAA;AACI,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA,OAAA;AACA,iBAAA;AACA,UAAA;AACA,YAAA;AACA,YAAA;AACA,SAAA;AACA,mBAAA;AACA,WAAA;AACA,cAAA,iBAAA,KAAA,IAAA,EAAA,MAAA,KAAA;;AAEA,MApFpB,KAOI,MA8BI,IAOI,CAAA,QA0BI,CAAA,IAcK;AACG,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,mBAAA;AACA,SAAA;;AAGJ,MA1FpB,KAOI,MA8BI,IAOI,CAAA,QA0BI,CAAA,IAoBK,CAAA;AACG,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,SAAA;;AAEA,MA9FxB,KAOI,MA8BI,IAOI,CAAA,QA0BI,CAAA,IAoBK,CAAA,MAII;AACG,WAAS;AACT,YAAA;AACA,QAAA;AACA,OAAA;AACA,UAAA;AACA,SAAA;AACA,cAAA;;AA5HhC,MAuBI,KAOI,MA8BI,IAOI,CAAA,QA0BI,CAAA,KAmCI;AACI,eAAa,WAAA,EAAA;AACb,aAAA;AACA,eAAA;AACA,eAAA;AACA,cAAA,QAAA,KAAA;;AArI5B,MAuBI,KAOI,MA8BI,IA8EI,CAAA;AACI,cAAA;AACA,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;AACA,iBAAA;;AA9IpB,MAuBI,KAOI,MA8BI,IA8EI,CAAA,KAMI;AACI,aAAA;AACA,eAAA;AACA,UAAA,EAAA,EAAA,SAAA;;AAnJxB,MAuBI,KAOI,MA8BI,IA8EI,CAAA,KAYI;AACI,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,eAAA;AACA,UAAA;;AA1JxB,MAuBI,KAOI,MA8BI,IA8EI,CAAA,KAmBI;AACI,WAAA;;AAKZ,MA5IR,KAOI,KAqIK,CAAA;AACG,SAAA;;AADJ,MA5IR,KAOI,KAqIK,CAAA,UAGG,CA5HJ;AA6HQ,YAAA;;AAJR,MA5IR,KAOI,KAqIK,CAAA,UAGG,CA5HJ,KA+HQ,CA1HJ;AA2HQ,WAAA;;AAPZ,MA5IR,KAOI,KAqIK,CAAA,UAWG,IACI,CA5GJ;AA6GQ,YAAA;;AAbZ,MA5IR,KAOI,KAqIK,CAAA,UAWG,IACI,CA5GJ,QA+GQ,CArFJ,KAsFQ;AACI,WAAA;;AAjBpB,MA5IR,KAOI,KAqIK,CAAA,UAWG,IAWI,CA/CJ;AAgDQ,WAAA;AACA,eAAA;AACA,mBAAA;;AAzBZ,MA5IR,KAOI,KAqIK,CAAA,UAWG,IAWI,CA/CJ,KAoDQ;AACI,WAAA;;AA/L5B,MAuBI,KA+KI;AACI,QAAA;AACA,cAAA;AACA,cAAA;;AAzMZ,MA6MI;AACI,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,SAAA;AACA,UAAA;AACA,eAAA;AACA,YAAA;;AAlNR,MA6MI,OAOI,CAAA;AACI,WAAA;AACA,cAAA,UAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;;AAzNZ,MA6MI,OAOI,CAAA,UAOI,CAAA;AACI,WAAA;AACA,eAAA;AACA,OAAA;;AA9NhB,MA6MI,OAOI,CAAA,UAOI,CAAA,KAKI,CAtLJ;AAuLQ,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;;AApOpB,MA6MI,OAOI,CAAA,UAOI,CAAA,KAKI,CAtLJ,KA4LQ;AACI,SAAA;AACA,UAAA;AACA,cAAA;;AAzOxB,MA6MI,OAOI,CAAA,UAOI,CAAA,KAkBI,CAAA;AACI,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AA/OpB,MA6MI,OAOI,CAAA,UA+BI,CAAA;AACI,WAAA;AACA,OAAA;;AArPhB,MA6MI,OAOI,CAAA,UA+BI,CAAA,MAII;AACI,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,mBAAA;AACA,aAAA;;AAEA,MA/ChB,OAOI,CAAA,UA+BI,CAAA,MAII,CAKK;AACG,SAAA;AACA,mBAAA;;", "names": []}