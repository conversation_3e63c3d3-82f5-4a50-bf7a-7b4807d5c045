@import '../../../../styles/variables.less';
@import '../../../../styles/mixins.less';

:host {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.75rem;
    overflow: hidden;
    cursor: pointer;
    padding: 1rem;

    // Panel Header
    header {
        flex: 0 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        padding-bottom: 1rem;

        h2 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
            line-height: 1.125rem;
            color: @text-color;
        }

        .icon-container {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0);
            padding: 0;
        }

        // Header with mapping mode selection
        .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .mapping-mode-tabs {
                display: flex;
                gap: 1rem;

                .tab-button {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0.5rem 0.5rem;
                    background: rgba(16, 185, 129, 0.2);
                    border: none;
                    border-radius: 0.5rem;
                    color: @text-color;
                    font-size: 1.125rem;
                    font-weight: 400;
                    cursor: pointer;

                    .transition(background, color);

                    &.active {
                        background: rgba(16, 185, 129, 0.5);
                        color: @text-color;
                    }

                    &:hover {
                        background: rgba(16, 185, 129, 0.3);
                    }
                }
            }

            .undo-button {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem;
                background: rgba(255, 255, 255, 0);
                border: none;
                border-radius: 0.5rem;
                color: @text-color;
                font-size: 1.125rem;
                cursor: pointer;

                .transition(background);

                &:hover {
                    background: rgba(255, 255, 255, 0.1);
                }

                fa-icon {
                    font-size: 1rem;
                }
            }
        }
    }

    // Collapsed vertical panel styling
    &.collapsed {
        header {
            writing-mode: vertical-lr;
            text-orientation: mixed;
            padding: 0;
            height: 100%;
            flex-direction: row-reverse;
            justify-content: flex-end;

            h2 {
                font-size: 1rem;
                font-weight: 600;
                line-height: 1.2;
                text-align: center;
                margin: 0;
                writing-mode: vertical-lr;
                text-orientation: mixed;
                color: @text-color;
                letter-spacing: -0.02em;
                transform: rotate(180deg);
            }

            .icon-container {
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                border-radius: 6px;
                background: rgba(255, 255, 255, 0);
                padding: 0;
            }
        }

        section {
            display: none;
        }
    }

    section {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;

        // Hide scrollbar for Chromium/WebKit (Electron)
        &::-webkit-scrollbar {
            display: none;
        }

        .item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
            cursor: pointer;
            overflow: hidden;

            .transition(background, border-color, box-shadow);

            &:hover {
                background: rgba(255, 255, 255, 0.08);
                border-color: rgba(255, 255, 255, 0.2);
            }

            &.selected {
                background: rgba(255, 255, 255, 0.1);
                border-color: @primary-color;
                box-shadow: 0 0 0 2px rgba(239, 78, 56, 0.3);

                .item-icon {
                    color: @primary-color;
                }
            }

            .item-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 3.25rem;
                height: 2.75rem;
                font-size: 3rem;
                color: @text-secondary;

                .transition(color);

                fa-icon {
                    font-size: 3rem;
                    color: inherit;
                }
            }

            .item-content {
                flex: 1;

                h3 {
                    font-size: 1rem;
                    font-weight: 500;
                    color: @text-color;
                    margin: 0 0 0.5rem 0;
                    line-height: 1.3;
                }

                p {
                    font-size: 0.8125rem;
                    color: @text-secondary;
                    margin: 0;
                    line-height: 1.4;
                }
            }
        }

        // Detail View Styles
        &.detail-view {
            display: flex;
            flex-direction: column;
            height: 100%;
            gap: 1rem;

            // Mapping section
            .mapping-section {
                flex: 1;
                display: flex;
                flex-direction: column;
            }

            // Settings footer
            .settings-footer {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                gap: 1rem;
                padding-top: 1rem;

                .settings-options {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;

                    .checkbox-group {
                        display: flex;
                        align-items: center;
                        gap: 1rem;
                        cursor: pointer;

                        input[type="checkbox"] {
                            display: none;
                        }

                        .checkbox-custom {
                            width: 0.9375rem;
                            height: 0.9375rem;
                            background: rgba(255, 255, 255, 0.2);
                            border-radius: 0.1875rem;
                            position: relative;

                            .transition(background);

                            &::after {
                                content: '';
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                width: 0.5rem;
                                height: 0.25rem;
                                border: 2px solid @text-color;
                                border-top: none;
                                border-right: none;
                                transform: translate(-50%, -60%) rotate(-45deg);
                                opacity: 0;

                                .transition(opacity);
                            }
                        }

                        input[type="checkbox"]:checked + .checkbox-custom {
                            background: rgba(255, 255, 255, 0.2);

                            &::after {
                                opacity: 1;
                            }
                        }

                        .checkbox-label {
                            font-size: 0.875rem;
                            font-weight: 500;
                            color: @text-color;
                            line-height: 1.43;
                        }
                    }

                    .post-restore-group {
                        display: flex;
                        align-items: center;
                        gap: 1rem;

                        .restore-action-select {
                            padding: 0.3125rem 0.625rem;
                            background: rgba(255, 255, 255, 0.2);
                            border: none;
                            border-radius: 0.25rem;
                            color: @text-color;
                            font-size: 0.75rem;
                            font-weight: 500;
                            line-height: 1.67;
                            cursor: pointer;

                            .transition(background);

                            &:hover {
                                background: rgba(255, 255, 255, 0.3);
                            }

                            option {
                                background: @background-dark;
                                color: @text-color;
                            }
                        }
                    }
                }

                .next-button {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 0.5rem 0.75rem;
                    background: linear-gradient(to right, #ef4e38 0%, #b61e0e 100%);
                    border: none;
                    border-radius: 0.5rem;
                    color: @text-color;
                    font-size: 0.875rem;
                    font-weight: 400;
                    line-height: 1.36;
                    cursor: pointer;
                    min-width: 12rem;

                    .transition(opacity);

                    &:hover {
                        opacity: 0.9;
                    }

                    &:active {
                        transform: translateY(1px);
                    }
                }
            }
        }
    }
}
