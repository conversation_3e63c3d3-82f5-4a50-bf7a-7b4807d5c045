import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-TBQAZTY7.js";

// renderer/src/app/standby/standby.component.ts
var StandbyComponent = class _StandbyComponent {
  static \u0275fac = function StandbyComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _StandbyComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _StandbyComponent, selectors: [["app-standby"]], decls: 2, vars: 0, template: function StandbyComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "p");
      \u0275\u0275text(1, "standby works!");
      \u0275\u0275elementEnd();
    }
  }, encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(StandbyComponent, [{
    type: Component,
    args: [{ selector: "app-standby", imports: [], template: "<p>standby works!</p>\r\n" }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(StandbyComponent, { className: "StandbyComponent", filePath: "renderer/src/app/standby/standby.component.ts", lineNumber: 9 });
})();
export {
  StandbyComponent
};
//# sourceMappingURL=chunk-KOW6IISC.js.map
