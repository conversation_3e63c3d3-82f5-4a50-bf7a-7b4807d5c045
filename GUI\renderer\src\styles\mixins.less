/**
 * Mixin
 * <AUTHOR>
 */

@import "variables.less";

// Transition
.transition(...) {
    & when (length(@arguments) = 0) {
        transition: none;
    }

    & when (length(@arguments) = 1) {
        transition: @arguments 300ms ease;
    }

    & when (length(@arguments) > 1) {
        @properties: replace("@{arguments}", " ", ", ", "g");
        transition-property: ~"@{properties}";
        transition-timing-function: ease;
        transition-duration: 300ms;
    }
}

// Animations
.animation(@name, @delay: 0ms) {
    animation-duration: 500ms;
    animation-fill-mode: forwards;
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-delay: @delay;

    & when (@name = fade-in-up) {
        animation-name: fadeInUp;
    }

    & when (@name = fade-in-left) {
        animation-name: fadeInLeft;
    }

    & when (@name = fade-in) {
        animation-name: fadeIn;
    }
}
