
@import '../../styles/variables.less';
@import '../../styles/mixins.less';

:host {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    height: 100%;
    padding: 1rem;
    overflow: hidden;

    // Header Section
    header {
        display: flex;
        justify-content: space-between;

        .animation(fade-in-up);

        .title {
            h1 {
                font-size: 1.5rem;
                font-weight: 700;
                margin: 0 0 .5rem 0;
                color: @text-color;
            }

            p {
                font-size: 1rem;
                color: @text-secondary;
                margin: 0;
            }
        }

        .actions {
            display: flex;
            gap: 1rem;
            align-items: center;

            button {
                .transition(color, background, box-shadow);

                border: none;
                padding: 1rem;
                border-radius: 0.75rem;
                font-size: 1rem;
                color: white;
                cursor: pointer;

                &.process {
                    min-width: 16.25rem;
                    background: @primary-gradient;

                    &:disabled {
                        cursor: not-allowed;
                        color: rgba(255, 255, 255, 0.5);
                        background: rgba(255, 255, 255, 0.1);
                    }

                    &:hover:not(:disabled) {
                        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
                    }
                }

                &.reset {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: rgba(255, 255, 255, 0.1);

                    &:hover {
                        background: rgba(255, 255, 255, 0.2);
                    }
                }
            }
        }
    }

    section {
        flex: 1;
        display: flex;
        gap: 1rem;
        width: 100%;
        min-width: 0; // Prevent grid from expanding beyond container
        overflow: hidden; // Prevent overflow of grid items
    }
}
