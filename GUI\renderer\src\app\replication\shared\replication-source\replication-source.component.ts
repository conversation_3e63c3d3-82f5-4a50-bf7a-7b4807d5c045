import { Component, input, output, computed, signal } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

interface FileTreeItem {
    id: string;
    name: string;
    icon: string;
    hasChildren: boolean;
    children?: FileTreeItem[];
}

interface FileReplicationItem {
    id: string;
    name: string;
    icon: string;
    size: string;
    modified: string;
}

type ReplicationType = 'ActiveStandby' | 'File Replication';

@Component({
    selector: 'app-replication-source',
    imports: [FontAwesomeModule],
    templateUrl: './replication-source.component.html',
    styleUrls: ['./replication-source.component.less']
})
export class ReplicationSourceComponent {
    // Input signals
    selectedSource = input<string | null>(null);
    selectedDestination = input<string | null>(null);
    expandedPanel = input<string | null>(null);

    // Output events
    sourceSelected = output<string>();
    destinationSelected = output<string>();
    onPanelExpand = output<'source' | 'schedule'>();

    // Internal state
    expandedFolders = signal<string[]>([]);
    selectedItems = signal<string[]>([]);
    selectedReplicationType = signal<ReplicationType>('File Replication');

    // File tree data
    fileTreeItems: FileTreeItem[] = [
        {
            id: 'system-c',
            name: 'System (C:)',
            icon: 'hard-drive',
            hasChildren: true,
            children: [
                { id: 'windows', name: 'Windows', icon: 'folder', hasChildren: false },
                { id: 'program-files', name: 'Program Files', icon: 'folder', hasChildren: false },
                { id: 'users', name: 'Users', icon: 'folder', hasChildren: false }
            ]
        },
        {
            id: 'd-drive',
            name: 'D:',
            icon: 'hard-drive',
            hasChildren: true,
            children: [
                { id: 'data', name: 'Data', icon: 'folder', hasChildren: false },
                { id: 'projects', name: 'Projects', icon: 'folder', hasChildren: false }
            ]
        },
        {
            id: 'e-drive',
            name: 'E:',
            icon: 'hard-drive',
            hasChildren: false
        },
        {
            id: 'desktop',
            name: 'Desktop',
            icon: 'desktop',
            hasChildren: true,
            children: [
                { id: 'shortcuts', name: 'Shortcuts', icon: 'folder', hasChildren: false },
                { id: 'files', name: 'Files', icon: 'folder', hasChildren: false }
            ]
        },
        {
            id: 'documents',
            name: 'My Documents',
            icon: 'folder',
            hasChildren: true,
            children: [
                { id: 'work', name: 'Work', icon: 'folder', hasChildren: false },
                { id: 'personal', name: 'Personal', icon: 'folder', hasChildren: false }
            ]
        }
    ];

    // File replication table data
    fileReplicationItems: FileReplicationItem[] = [
        {
            id: 'file-1',
            name: 'Documents',
            icon: 'folder',
            size: '2.5 GB',
            modified: '2024-01-15'
        },
        {
            id: 'file-2',
            name: 'Pictures',
            icon: 'folder',
            size: '1.8 GB',
            modified: '2024-01-14'
        },
        {
            id: 'file-3',
            name: 'Videos',
            icon: 'folder',
            size: '5.2 GB',
            modified: '2024-01-13'
        },
        {
            id: 'file-4',
            name: 'Music',
            icon: 'folder',
            size: '800 MB',
            modified: '2024-01-12'
        },
        {
            id: 'file-5',
            name: 'Desktop',
            icon: 'folder',
            size: '150 MB',
            modified: '2024-01-11'
        },
        {
            id: 'file-6',
            name: 'Downloads',
            icon: 'folder',
            size: '3.1 GB',
            modified: '2024-01-10'
        },
        {
            id: 'file-7',
            name: 'System Files',
            icon: 'folder',
            size: '12.5 GB',
            modified: '2024-01-09'
        },
        {
            id: 'file-8',
            name: 'Program Files',
            icon: 'folder',
            size: '8.7 GB',
            modified: '2024-01-08'
        }
    ];

    // Computed properties
    isPanelExpanded = computed(() => this.expandedPanel() === 'source');
    isPanelCollapsed = computed(() => {
        const expanded = this.expandedPanel();
        return expanded !== null && expanded !== 'source';
    });

    // Methods
    selectSource(sourceId: string): void {
        this.sourceSelected.emit(sourceId);
    }

    selectDestination(destinationId: string): void {
        this.destinationSelected.emit(destinationId);
    }

    expandPanel(): void {
        this.onPanelExpand.emit('source');
    }

    onSourceChange(event: Event): void {
        const target = event.target as HTMLSelectElement;
        if (target.value) {
            this.sourceSelected.emit(target.value);
        }
    }

    onDestinationChange(event: Event): void {
        const target = event.target as HTMLSelectElement;
        if (target.value) {
            this.destinationSelected.emit(target.value);
        }
    }

    canEstablishConnection(): boolean {
        return this.selectedSource() !== null && this.selectedDestination() !== null;
    }

    toggleFolder(folderId: string): void {
        const currentExpanded = this.expandedFolders();
        if (currentExpanded.includes(folderId)) {
            this.expandedFolders.set(currentExpanded.filter(id => id !== folderId));
        } else {
            this.expandedFolders.set([...currentExpanded, folderId]);
        }
    }

    isItemSelected(itemId: string): boolean {
        return this.selectedItems().includes(itemId);
    }

    toggleItemSelection(itemId: string, event: Event): void {
        event.stopPropagation(); // Prevent folder toggle when clicking checkbox
        const target = event.target as HTMLInputElement;
        const currentSelected = this.selectedItems();

        if (target.checked) {
            this.selectedItems.set([...currentSelected, itemId]);
        } else {
            this.selectedItems.set(currentSelected.filter(id => id !== itemId));
        }
    }

    hasSelectedItems(): boolean {
        return this.selectedItems().length > 0;
    }

    // Replication Type methods
    setReplicationType(type: ReplicationType): void {
        this.selectedReplicationType.set(type);
    }

    isFileReplication(): boolean {
        return this.selectedReplicationType() === 'File Replication';
    }

    isActiveStandby(): boolean {
        return this.selectedReplicationType() === 'ActiveStandby';
    }

    // File replication methods
    confirmFileSelection(): void {
        const selectedFiles = this.selectedItems();
        if (selectedFiles.length > 0) {
            console.log('Selected files for replication:', selectedFiles);
            // Here you would typically emit an event or call a service
            // to handle the file selection confirmation
        }
    }
}
