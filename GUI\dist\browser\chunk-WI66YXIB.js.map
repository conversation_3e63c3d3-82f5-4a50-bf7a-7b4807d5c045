{"version": 3, "sources": ["renderer/src/app/preferences/quick-recovery/quick-recovery.component.ts", "renderer/src/app/preferences/quick-recovery/quick-recovery.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\n\r\n@Component({\r\n    selector: 'app-quick-recovery',\r\n    imports: [FontAwesomeModule],\r\n    templateUrl: './quick-recovery.component.html',\r\n    styleUrl: './quick-recovery.component.less'\r\n})\r\nexport class QuickRecoveryComponent {\r\n\r\n}\r\n", "<header>\r\n    <div class=\"icon\">\r\n        <fa-icon icon=\"lightning-bolt\"></fa-icon>\r\n    </div>\r\n    <h2>Quick Recovery</h2>\r\n</header>\r\n\r\n<section>\r\n    <p>Quick recovery settings will be implemented here.</p>\r\n</section>\r\n\r\n<footer>\r\n    <span class=\"notice\">0 Configuration changes waiting to be applied</span>\r\n    <div class=\"buttons\">\r\n        <button type=\"button\" class=\"button secondary\">Cancel</button>\r\n        <button type=\"button\" class=\"button primary\">Apply</button>\r\n    </div>\r\n</footer>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AASM,IAAO,yBAAP,MAAO,wBAAsB;;qCAAtB,yBAAsB;EAAA;yEAAtB,yBAAsB,WAAA,CAAA,CAAA,oBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,gBAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,WAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,SAAA,CAAA,GAAA,UAAA,SAAA,gCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACTnC,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,OAAA,CAAA;AAEA,MAAA,oBAAA,GAAA,WAAA,CAAA;AACJ,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,gBAAA;AAAc,MAAA,uBAAA,EAAK;AAG3B,MAAA,yBAAA,GAAA,SAAA,EAAS,GAAA,GAAA;AACF,MAAA,iBAAA,GAAA,mDAAA;AAAiD,MAAA,uBAAA,EAAI;AAG5D,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,QAAA,CAAA;AACiB,MAAA,iBAAA,IAAA,+CAAA;AAA6C,MAAA,uBAAA;AAClE,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,UAAA,CAAA;AAC8B,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AACrD,MAAA,yBAAA,IAAA,UAAA,CAAA;AAA6C,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAS,EACzD;;oBDXI,mBAAiB,eAAA,GAAA,QAAA,CAAA,49CAAA,EAAA,CAAA;;;sEAIlB,wBAAsB,CAAA;UANlC;uBACa,sBAAoB,SACrB,CAAC,iBAAiB,GAAC,UAAA,kiBAAA,QAAA,CAAA,utCAAA,EAAA,CAAA;;;;6EAInB,wBAAsB,EAAA,WAAA,0BAAA,UAAA,2EAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}