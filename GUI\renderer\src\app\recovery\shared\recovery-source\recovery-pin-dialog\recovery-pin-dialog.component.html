<div class="dialog-container">
    <h2 cdk-dialog-title>Enter Recovery PIN</h2>
    <div cdk-dialog-content>
        <p>Please enter the Download PIN number issued by Actiphy Backup Cloud Service Space Administrative portal, or
            click o the URI issued by the portal.</p>
        <div class="pin-input-grid">
            <div class="pin-input-row">
                <input #pinInput type="text" maxlength="6" pattern="[0-9]*" inputmode="numeric" [(ngModel)]="pin[0]"
                    (input)="onInput($event, 0)" (keydown)="onKeyDown($event, 0)" id="pin-input-0"
                    placeholder="000000" />
                <span class="separator">-</span>
                <input #pinInput type="text" maxlength="6" pattern="[0-9]*" inputmode="numeric" [(ngModel)]="pin[1]"
                    (input)="onInput($event, 1)" (keydown)="onKeyDown($event, 1)" id="pin-input-1"
                    placeholder="000000" />
            </div>
            <div class="pin-input-row">
                <input #pinInput type="text" maxlength="6" pattern="[0-9]*" inputmode="numeric" [(ngModel)]="pin[2]"
                    (input)="onInput($event, 2)" (keydown)="onKeyDown($event, 2)" id="pin-input-2"
                    placeholder="000000" />
                <span class="separator">-</span>
                <input #pinInput type="text" maxlength="6" pattern="[0-9]*" inputmode="numeric" [(ngModel)]="pin[3]"
                    (input)="onInput($event, 3)" (keydown)="onKeyDown($event, 3)" id="pin-input-3"
                    placeholder="000000" />
            </div>
        </div>
    </div>
    <div cdk-dialog-actions>
        <button class="secondary" (click)="dialogRef.close()">Cancel</button>
        <button [disabled]="!isPinComplete" (click)="dialogRef.close(pin.join(''))">Enter PIN First</button>
    </div>
</div>
