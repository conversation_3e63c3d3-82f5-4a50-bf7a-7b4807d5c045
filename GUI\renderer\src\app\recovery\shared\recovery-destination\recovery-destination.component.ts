import { Component, input, output, computed, signal } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DiskMappingComponent, DiskInfo, PartitionInfo } from './shared/disk-mapping/disk-mapping.component';
import { DiskMappingCustomComponent } from './shared/disk-mapping-custom/disk-mapping-custom.component';

export type MappingMode = 'auto' | 'custom';

export interface RecoveryDestination {
    id: string;
    title: string;
    description: string;
    icon: string;
}

@Component({
    selector: 'app-recovery-destination',
    templateUrl: './recovery-destination.component.html',
    styleUrl: './recovery-destination.component.less',
    imports: [FontAwesomeModule, CommonModule, FormsModule, DiskMappingComponent, DiskMappingCustomComponent],
})
export class RecoveryDestinationComponent {
    // Inputs from parent component
    selectedDestination = input<string | null>(null);
    expandedPanel = input<string | null>(null);

    // Outputs to parent component
    destinationSelected = output<string>();
    onPanelExpand = output<'destination'>();

    // State for showing detail view in section
    showDetailView = signal(false);

    // Destination options
    recoveryDestinations: RecoveryDestination[] = [
        {
            id: 'disk-volume-this',
            title: 'Disk / Volume recovery on this computer',
            description: 'Use this to recover this computer into previously safe state',
            icon: 'hard-drive'
        },
        {
            id: 'disk-volume-another',
            title: 'Disk / Volume recovery on another computer',
            description: 'Use this to recover another computer into previously safe state',
            icon: 'hard-drive'
        },
        {
            id: 'file-this',
            title: 'File recovery on this computer',
            description: 'Use this to retrieve individual files from backup image and recover to this computer',
            icon: 'file'
        },
        {
            id: 'file-another',
            title: 'File recovery on another computer',
            description: 'Use this to retrieve individual files from backup image and recover to another computer',
            icon: 'file'
        },
        {
            id: 'hyper-recovery',
            title: 'HyperRecovery',
            description: 'Use this to recover to a Hypervisor VM on a HyperVisor',
            icon: 'server'
        },
        {
            id: 'hyper-recovery-live',
            title: 'HyperRecovery LIVE!',
            description: 'Use this to recover to a Hypervisor VM on a HyperVisor with zero downtime.',
            icon: 'server'
        }
    ];

    // Detail view data (shown when item is selected)
    mappingMode: MappingMode = 'auto';

    // Recovery information for detail view
    recoveryInfo = {
        type: 'Local Disk / Volume',
        timestamp: '2025-04-11 6:00:00',
        location: 'original location'
    };

    // Source disks from backup image for detail view
    sourceDisks: DiskInfo[] = [
        {
            id: 'disk0-source',
            name: 'Disk 0',
            type: 'Basic (GPT)',
            capacity: 'Boot',
            partitions: [
                {
                    id: 'partition1',
                    name: '100 MB FAT32 Boot',
                    size: '100 MB',
                    type: 'FAT32',
                    usage: 100,
                    isBoot: true
                },
                {
                    id: 'partition2',
                    name: '99.2 GB NTFS',
                    size: '99.2 GB',
                    type: 'NTFS',
                    usage: 75
                },
                {
                    id: 'partition3',
                    name: '704 MB NTFS',
                    size: '704 MB',
                    type: 'NTFS',
                    usage: 45
                }
            ]
        }
    ];

    // Target disks for recovery
    targetDisks: DiskInfo[] = [
        {
            id: 'disk0-target',
            name: 'Disk 0',
            type: 'Basic (GPT)',
            capacity: 'Boot',
            partitions: [
                {
                    id: 'partition1',
                    name: '100 MB FAT32 Boot',
                    size: '100 MB',
                    type: 'FAT32',
                    usage: 100,
                    isBoot: true
                },
                {
                    id: 'partition2',
                    name: '99.2 GB NTFS',
                    size: '99.2 GB',
                    type: 'NTFS',
                    usage: 75
                },
                {
                    id: 'partition3',
                    name: '704 MB NTFS',
                    size: '704 MB',
                    type: 'NTFS',
                    usage: 45
                }
            ]
        }
    ];

    // Settings for detail view
    settings = {
        ignoreChecksum: false,
        postRestoreOperation: false,
        postRestoreAction: 'Reboot System'
    };

    // Check if panel is expanded
    isPanelExpanded = computed(() => this.expandedPanel() === 'destination');

    // Check if panel is collapsed
    isPanelCollapsed = computed(() => {
        const expanded = this.expandedPanel();
        return expanded !== null && expanded !== 'destination';
    });

    // Handle destination selection
    selectDestination(destinationId: string): void {
        this.destinationSelected.emit(destinationId);
        // Ensure panel stays expanded when showing detail view
        if (!this.isPanelExpanded()) {
            this.onPanelExpand.emit('destination');
        }
        // Show detail view in section
        this.showDetailView.set(true);
    }

    // Handle panel expand
    expandPanel(): void {
        this.onPanelExpand.emit('destination');
    }

    // Handle back from detail view
    backToSelection(): void {
        this.showDetailView.set(false);
    }

    // Detail view methods
    setMappingMode(mode: MappingMode): void {
        this.mappingMode = mode;
    }

    toggleIgnoreChecksum(): void {
        this.settings.ignoreChecksum = !this.settings.ignoreChecksum;
    }

    togglePostRestoreOperation(): void {
        this.settings.postRestoreOperation = !this.settings.postRestoreOperation;
    }

    resetMapping(): void {
        // Reset mapping logic
        console.log('Reset mapping');
    }

    onMoreOptions(partitionId: string): void {
        // Handle more options for partition
        console.log('More options for:', partitionId);
    }

    onNextClick(): void {
        // Handle next step
        console.log('Next step in recovery process');
    }
}
