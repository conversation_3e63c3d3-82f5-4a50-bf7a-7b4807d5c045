import {
  CommonModule,
  FaIconComponent,
  FontAwesomeModule
} from "./chunk-N34W2DJ7.js";
import {
  Component,
  computed,
  input,
  output,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵconditionalCreate,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵrepeater,
  ɵɵrepeaterCreate,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-TBQAZTY7.js";

// renderer/src/app/replication/shared/replication-source/replication-source.component.ts
var _forTrack0 = ($index, $item) => $item.id;
function ReplicationSourceComponent_Conditional_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "header", 0);
    \u0275\u0275listener("click", function ReplicationSourceComponent_Conditional_0_Template_header_click_0_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.expandPanel());
    });
    \u0275\u0275elementStart(1, "h2");
    \u0275\u0275text(2, "Replication Source & Destination");
    \u0275\u0275elementEnd();
    \u0275\u0275element(3, "fa-icon", 1);
    \u0275\u0275elementEnd();
  }
}
function ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "button", 49);
    \u0275\u0275element(1, "fa-icon", 50);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r5 = \u0275\u0275nextContext().$implicit;
    const ctx_r1 = \u0275\u0275nextContext(3);
    \u0275\u0275classProp("expanded", ctx_r1.expandedFolders().includes(item_r5.id));
  }
}
function ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "div", 43);
  }
}
function ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Conditional_10_For_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 51)(1, "div", 52)(2, "div", 41);
    \u0275\u0275element(3, "div", 43);
    \u0275\u0275elementStart(4, "input", 53);
    \u0275\u0275listener("change", function ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Conditional_10_For_2_Template_input_change_4_listener($event) {
      const child_r7 = \u0275\u0275restoreView(_r6).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(5);
      return \u0275\u0275resetView(ctx_r1.toggleItemSelection(child_r7.id, $event));
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "div", 45);
    \u0275\u0275element(6, "fa-icon", 46);
    \u0275\u0275elementStart(7, "span", 47);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const child_r7 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(5);
    \u0275\u0275advance(4);
    \u0275\u0275property("checked", ctx_r1.isItemSelected(child_r7.id));
    \u0275\u0275advance(2);
    \u0275\u0275property("icon", child_r7.icon);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(child_r7.name);
  }
}
function ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Conditional_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 48);
    \u0275\u0275repeaterCreate(1, ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Conditional_10_For_2_Template, 9, 3, "div", 51, _forTrack0);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r5 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275repeater(item_r5.children);
  }
}
function ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 39)(1, "div", 40);
    \u0275\u0275listener("click", function ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Template_div_click_1_listener() {
      const item_r5 = \u0275\u0275restoreView(_r4).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(item_r5.hasChildren ? ctx_r1.toggleFolder(item_r5.id) : null);
    });
    \u0275\u0275elementStart(2, "div", 41);
    \u0275\u0275conditionalCreate(3, ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Conditional_3_Template, 2, 2, "button", 42)(4, ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Conditional_4_Template, 1, 0, "div", 43);
    \u0275\u0275elementStart(5, "input", 44);
    \u0275\u0275listener("change", function ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Template_input_change_5_listener($event) {
      const item_r5 = \u0275\u0275restoreView(_r4).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r1.toggleItemSelection(item_r5.id, $event));
    })("click", function ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Template_input_click_5_listener($event) {
      \u0275\u0275restoreView(_r4);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 45);
    \u0275\u0275element(7, "fa-icon", 46);
    \u0275\u0275elementStart(8, "span", 47);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd()()();
    \u0275\u0275conditionalCreate(10, ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Conditional_10_Template, 3, 0, "div", 48);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r5 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(3);
    \u0275\u0275classProp("expanded", ctx_r1.expandedFolders().includes(item_r5.id))("has-children", item_r5.hasChildren);
    \u0275\u0275advance(3);
    \u0275\u0275conditional(item_r5.hasChildren ? 3 : 4);
    \u0275\u0275advance(2);
    \u0275\u0275property("checked", ctx_r1.isItemSelected(item_r5.id));
    \u0275\u0275advance(2);
    \u0275\u0275property("icon", item_r5.icon);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(item_r5.name);
    \u0275\u0275advance();
    \u0275\u0275conditional(item_r5.hasChildren && ctx_r1.expandedFolders().includes(item_r5.id) ? 10 : -1);
  }
}
function ReplicationSourceComponent_Conditional_1_Conditional_43_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 21)(1, "div", 25)(2, "span", 26);
    \u0275\u0275text(3, "Select File Replication source & destination folders");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 27);
    \u0275\u0275text(5, "Destination");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 28)(7, "div", 29)(8, "div", 30)(9, "span", 31);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(11, "div", 32)(12, "div", 33);
    \u0275\u0275repeaterCreate(13, ReplicationSourceComponent_Conditional_1_Conditional_43_For_14_Template, 11, 9, "div", 34, _forTrack0);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(15, "div", 35)(16, "div", 30)(17, "span", 31);
    \u0275\u0275text(18);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(19, "div", 32)(20, "div", 36);
    \u0275\u0275element(21, "fa-icon", 37);
    \u0275\u0275elementStart(22, "span", 38);
    \u0275\u0275text(23, "Destination folders will appear here");
    \u0275\u0275elementEnd()()()()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(10);
    \u0275\u0275textInterpolate(ctx_r1.selectedSource() || "Local");
    \u0275\u0275advance(3);
    \u0275\u0275repeater(ctx_r1.fileTreeItems);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.selectedDestination() || "Desktop001");
  }
}
function ReplicationSourceComponent_Conditional_1_Conditional_44_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 22)(1, "div", 36);
    \u0275\u0275element(2, "fa-icon", 54);
    \u0275\u0275elementStart(3, "span", 38);
    \u0275\u0275text(4, "ActiveStandby configuration will be available here");
    \u0275\u0275elementEnd()()();
  }
}
function ReplicationSourceComponent_Conditional_1_Conditional_46_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 55);
    \u0275\u0275listener("click", function ReplicationSourceComponent_Conditional_1_Conditional_46_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r8);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.selectSource("selected-source"));
    });
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275classProp("disabled", !ctx_r1.hasSelectedItems());
    \u0275\u0275property("disabled", !ctx_r1.hasSelectedItems());
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.hasSelectedItems() ? "Confirm Selection" : "Choose options first", " ");
  }
}
function ReplicationSourceComponent_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "section")(1, "div", 2)(2, "div", 3)(3, "div", 4);
    \u0275\u0275element(4, "fa-icon", 5);
    \u0275\u0275elementStart(5, "div", 6)(6, "label", 7);
    \u0275\u0275text(7, "Choose Source");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "div", 8);
    \u0275\u0275listener("click", function ReplicationSourceComponent_Conditional_1_Template_div_click_8_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onSourceChange($event));
    });
    \u0275\u0275elementStart(9, "span", 9);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275element(11, "fa-icon", 10);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(12, "div", 4);
    \u0275\u0275element(13, "fa-icon", 5);
    \u0275\u0275elementStart(14, "div", 6)(15, "label", 7);
    \u0275\u0275text(16, "Choose Destination");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "div", 8);
    \u0275\u0275listener("click", function ReplicationSourceComponent_Conditional_1_Template_div_click_17_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onDestinationChange($event));
    });
    \u0275\u0275elementStart(18, "span", 9);
    \u0275\u0275text(19);
    \u0275\u0275elementEnd();
    \u0275\u0275element(20, "fa-icon", 10);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(21, "button", 11);
    \u0275\u0275text(22, " Disconnect ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(23, "div", 12)(24, "div", 13)(25, "button", 14);
    \u0275\u0275listener("click", function ReplicationSourceComponent_Conditional_1_Template_button_click_25_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.setReplicationType("ActiveStandby"));
    });
    \u0275\u0275elementStart(26, "div", 15)(27, "div", 16);
    \u0275\u0275element(28, "fa-icon", 17);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(29, "div", 18)(30, "div", 19);
    \u0275\u0275text(31, "Hypervisor VM");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(32, "div", 20);
    \u0275\u0275text(33, "Hypervisor VM");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(34, "button", 14);
    \u0275\u0275listener("click", function ReplicationSourceComponent_Conditional_1_Template_button_click_34_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.setReplicationType("File Replication"));
    });
    \u0275\u0275elementStart(35, "div", 15)(36, "div", 16);
    \u0275\u0275element(37, "fa-icon", 17);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(38, "div", 18)(39, "div", 19);
    \u0275\u0275text(40, "Hypervisor VM");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(41, "div", 20);
    \u0275\u0275text(42, "Hypervisor VM");
    \u0275\u0275elementEnd()()()()()();
    \u0275\u0275conditionalCreate(43, ReplicationSourceComponent_Conditional_1_Conditional_43_Template, 24, 2, "div", 21);
    \u0275\u0275conditionalCreate(44, ReplicationSourceComponent_Conditional_1_Conditional_44_Template, 5, 0, "div", 22);
    \u0275\u0275elementStart(45, "div", 23);
    \u0275\u0275conditionalCreate(46, ReplicationSourceComponent_Conditional_1_Conditional_46_Template, 2, 4, "button", 24);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(10);
    \u0275\u0275textInterpolate(ctx_r1.selectedSource() || "Local");
    \u0275\u0275advance(9);
    \u0275\u0275textInterpolate(ctx_r1.selectedDestination() || "Desktop001");
    \u0275\u0275advance(2);
    \u0275\u0275classProp("disabled", !ctx_r1.canEstablishConnection());
    \u0275\u0275property("disabled", !ctx_r1.canEstablishConnection());
    \u0275\u0275advance(4);
    \u0275\u0275classProp("selected", ctx_r1.selectedReplicationType() === "ActiveStandby");
    \u0275\u0275advance(9);
    \u0275\u0275classProp("selected", ctx_r1.selectedReplicationType() === "File Replication");
    \u0275\u0275advance(9);
    \u0275\u0275conditional(ctx_r1.isFileReplication() ? 43 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r1.isActiveStandby() ? 44 : -1);
    \u0275\u0275advance(2);
    \u0275\u0275conditional(ctx_r1.isFileReplication() ? 46 : -1);
  }
}
var ReplicationSourceComponent = class _ReplicationSourceComponent {
  // Input signals
  selectedSource = input(null);
  selectedDestination = input(null);
  expandedPanel = input(null);
  // Output events
  sourceSelected = output();
  destinationSelected = output();
  onPanelExpand = output();
  // Internal state
  expandedFolders = signal([]);
  selectedItems = signal([]);
  selectedReplicationType = signal("File Replication");
  // File tree data
  fileTreeItems = [
    {
      id: "system-c",
      name: "System (C:)",
      icon: "hard-drive",
      hasChildren: true,
      children: [
        { id: "windows", name: "Windows", icon: "folder", hasChildren: false },
        { id: "program-files", name: "Program Files", icon: "folder", hasChildren: false },
        { id: "users", name: "Users", icon: "folder", hasChildren: false }
      ]
    },
    {
      id: "d-drive",
      name: "D:",
      icon: "hard-drive",
      hasChildren: true,
      children: [
        { id: "data", name: "Data", icon: "folder", hasChildren: false },
        { id: "projects", name: "Projects", icon: "folder", hasChildren: false }
      ]
    },
    {
      id: "e-drive",
      name: "E:",
      icon: "hard-drive",
      hasChildren: false
    },
    {
      id: "desktop",
      name: "Desktop",
      icon: "desktop",
      hasChildren: true,
      children: [
        { id: "shortcuts", name: "Shortcuts", icon: "folder", hasChildren: false },
        { id: "files", name: "Files", icon: "folder", hasChildren: false }
      ]
    },
    {
      id: "documents",
      name: "My Documents",
      icon: "folder",
      hasChildren: true,
      children: [
        { id: "work", name: "Work", icon: "folder", hasChildren: false },
        { id: "personal", name: "Personal", icon: "folder", hasChildren: false }
      ]
    }
  ];
  // Computed properties
  isPanelExpanded = computed(() => this.expandedPanel() === "source");
  isPanelCollapsed = computed(() => {
    const expanded = this.expandedPanel();
    return expanded !== null && expanded !== "source";
  });
  // Methods
  selectSource(sourceId) {
    this.sourceSelected.emit(sourceId);
  }
  selectDestination(destinationId) {
    this.destinationSelected.emit(destinationId);
  }
  expandPanel() {
    this.onPanelExpand.emit("source");
  }
  onSourceChange(event) {
    const target = event.target;
    if (target.value) {
      this.sourceSelected.emit(target.value);
    }
  }
  onDestinationChange(event) {
    const target = event.target;
    if (target.value) {
      this.destinationSelected.emit(target.value);
    }
  }
  canEstablishConnection() {
    return this.selectedSource() !== null && this.selectedDestination() !== null;
  }
  toggleFolder(folderId) {
    const currentExpanded = this.expandedFolders();
    if (currentExpanded.includes(folderId)) {
      this.expandedFolders.set(currentExpanded.filter((id) => id !== folderId));
    } else {
      this.expandedFolders.set([...currentExpanded, folderId]);
    }
  }
  isItemSelected(itemId) {
    return this.selectedItems().includes(itemId);
  }
  toggleItemSelection(itemId, event) {
    event.stopPropagation();
    const target = event.target;
    const currentSelected = this.selectedItems();
    if (target.checked) {
      this.selectedItems.set([...currentSelected, itemId]);
    } else {
      this.selectedItems.set(currentSelected.filter((id) => id !== itemId));
    }
  }
  hasSelectedItems() {
    return this.selectedItems().length > 0;
  }
  // Replication Type methods
  setReplicationType(type) {
    this.selectedReplicationType.set(type);
  }
  isFileReplication() {
    return this.selectedReplicationType() === "File Replication";
  }
  isActiveStandby() {
    return this.selectedReplicationType() === "ActiveStandby";
  }
  static \u0275fac = function ReplicationSourceComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ReplicationSourceComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ReplicationSourceComponent, selectors: [["app-replication-source"]], inputs: { selectedSource: [1, "selectedSource"], selectedDestination: [1, "selectedDestination"], expandedPanel: [1, "expandedPanel"] }, outputs: { sourceSelected: "sourceSelected", destinationSelected: "destinationSelected", onPanelExpand: "onPanelExpand" }, decls: 2, vars: 1, consts: [[3, "click"], ["icon", "server"], [1, "connection-section"], [1, "connection-row"], [1, "connection-group"], ["icon", "desktop", 1, "connection-icon"], [1, "connection-details"], [1, "connection-label"], [1, "connection-dropdown", 3, "click"], [1, "dropdown-value"], ["icon", "chevron-down", 1, "dropdown-arrow"], [1, "disconnect-button", 3, "disabled"], [1, "type-selection-section"], [1, "type-options-horizontal"], [1, "type-option-horizontal", 3, "click"], [1, "option-content-horizontal"], [1, "option-icon-horizontal"], ["icon", "desktop", 1, "type-icon-horizontal"], [1, "option-text-horizontal"], [1, "option-title-horizontal"], [1, "option-description-horizontal"], [1, "file-selection-section"], [1, "activestandby-section"], [1, "action-bar"], [1, "action-button", "primary", 3, "disabled"], [1, "section-header"], [1, "section-title"], [1, "destination-label"], [1, "file-panels"], [1, "file-panel", "source-panel"], [1, "panel-header"], [1, "panel-title"], [1, "panel-content"], [1, "file-tree"], [1, "file-item", 3, "expanded", "has-children"], [1, "file-panel", "destination-panel"], [1, "empty-state"], ["icon", "folder", 1, "empty-icon"], [1, "empty-text"], [1, "file-item"], [1, "file-row", 3, "click"], [1, "file-controls"], [1, "expand-toggle", 3, "expanded"], [1, "expand-spacer"], ["type", "checkbox", 1, "file-checkbox", 3, "change", "click", "checked"], [1, "file-info"], [1, "file-icon", 3, "icon"], [1, "file-name"], [1, "file-children"], [1, "expand-toggle"], ["icon", "chevron-down"], [1, "file-item", "child-item"], [1, "file-row"], ["type", "checkbox", 1, "file-checkbox", 3, "change", "checked"], ["icon", "shield", 1, "empty-icon"], [1, "action-button", "primary", 3, "click", "disabled"]], template: function ReplicationSourceComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275conditionalCreate(0, ReplicationSourceComponent_Conditional_0_Template, 4, 0, "header")(1, ReplicationSourceComponent_Conditional_1_Template, 47, 12, "section");
    }
    if (rf & 2) {
      \u0275\u0275conditional(!ctx.isPanelExpanded() ? 0 : 1);
    }
  }, dependencies: [FontAwesomeModule, FaIconComponent], styles: ['\n\n[_nghost-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 0.75rem;\n  overflow: hidden;\n  cursor: pointer;\n  padding: 1rem;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-bottom: 1rem;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0;\n  line-height: 1.125rem;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  padding: 0;\n  height: 100%;\n  flex-direction: row-reverse;\n  justify-content: flex-end;\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-align: center;\n  margin: 0;\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  color: #ffffff;\n  letter-spacing: -0.02em;\n  transform: rotate(180deg);\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n.collapsed[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  display: none;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]::-webkit-scrollbar {\n  display: none;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .connection-section[_ngcontent-%COMP%]   .connection-row[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .connection-section[_ngcontent-%COMP%]   .connection-row[_ngcontent-%COMP%]   .connection-group[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  flex: 1;\n  min-width: 200px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .connection-section[_ngcontent-%COMP%]   .connection-row[_ngcontent-%COMP%]   .connection-group[_ngcontent-%COMP%]   .connection-icon[_ngcontent-%COMP%] {\n  color: #ffffff;\n  font-size: 3rem;\n  opacity: 0.8;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .connection-section[_ngcontent-%COMP%]   .connection-row[_ngcontent-%COMP%]   .connection-group[_ngcontent-%COMP%]   .connection-details[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .connection-section[_ngcontent-%COMP%]   .connection-row[_ngcontent-%COMP%]   .connection-group[_ngcontent-%COMP%]   .connection-details[_ngcontent-%COMP%]   .connection-label[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: rgba(255, 255, 255, 0.5);\n  font-weight: 500;\n  line-height: 1;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .connection-section[_ngcontent-%COMP%]   .connection-row[_ngcontent-%COMP%]   .connection-group[_ngcontent-%COMP%]   .connection-details[_ngcontent-%COMP%]   .connection-dropdown[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0.5rem 0.75rem;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  cursor: pointer;\n  transition: all 300ms ease;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .connection-section[_ngcontent-%COMP%]   .connection-row[_ngcontent-%COMP%]   .connection-group[_ngcontent-%COMP%]   .connection-details[_ngcontent-%COMP%]   .connection-dropdown[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.1);\n  border-color: rgba(255, 255, 255, 0.3);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .connection-section[_ngcontent-%COMP%]   .connection-row[_ngcontent-%COMP%]   .connection-group[_ngcontent-%COMP%]   .connection-details[_ngcontent-%COMP%]   .connection-dropdown[_ngcontent-%COMP%]   .dropdown-value[_ngcontent-%COMP%] {\n  color: #ffffff;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .connection-section[_ngcontent-%COMP%]   .connection-row[_ngcontent-%COMP%]   .connection-group[_ngcontent-%COMP%]   .connection-details[_ngcontent-%COMP%]   .connection-dropdown[_ngcontent-%COMP%]   .dropdown-arrow[_ngcontent-%COMP%] {\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 0.75rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .connection-section[_ngcontent-%COMP%]   .connection-row[_ngcontent-%COMP%]   .disconnect-button[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  color: #ffffff;\n  padding: 0.625rem 1rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 300ms ease;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .connection-section[_ngcontent-%COMP%]   .connection-row[_ngcontent-%COMP%]   .disconnect-button[_ngcontent-%COMP%]:hover:not(.disabled) {\n  background: rgba(255, 255, 255, 0.15);\n  border-color: rgba(255, 255, 255, 0.3);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .connection-section[_ngcontent-%COMP%]   .connection-row[_ngcontent-%COMP%]   .disconnect-button.disabled[_ngcontent-%COMP%] {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: #ffffff;\n  font-weight: 500;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .destination-label[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: rgba(255, 255, 255, 0.5);\n  font-weight: 500;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%] {\n  flex: 1;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1rem;\n  overflow: hidden;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  overflow: hidden;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%] {\n  padding: 0.75rem 1rem;\n  background: rgba(255, 255, 255, 0.05);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .panel-title[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%] {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0.5rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]::-webkit-scrollbar {\n  width: 4px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\n  background: transparent;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 255, 255, 0.3);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.source-panel[_ngcontent-%COMP%]   .file-tree[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-row[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 0.375rem 0.5rem;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: background-color 300ms ease;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.source-panel[_ngcontent-%COMP%]   .file-tree[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-row[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.05);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.source-panel[_ngcontent-%COMP%]   .file-tree[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-row[_ngcontent-%COMP%]   .file-controls[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-right: 0.75rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.source-panel[_ngcontent-%COMP%]   .file-tree[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-row[_ngcontent-%COMP%]   .file-controls[_ngcontent-%COMP%]   .expand-toggle[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  padding: 0;\n  width: 16px;\n  height: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  color: rgba(255, 255, 255, 0.5);\n  transition: transform 300ms ease;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.source-panel[_ngcontent-%COMP%]   .file-tree[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-row[_ngcontent-%COMP%]   .file-controls[_ngcontent-%COMP%]   .expand-toggle.expanded[_ngcontent-%COMP%] {\n  transform: rotate(180deg);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.source-panel[_ngcontent-%COMP%]   .file-tree[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-row[_ngcontent-%COMP%]   .file-controls[_ngcontent-%COMP%]   .expand-toggle[_ngcontent-%COMP%]   fa-icon[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.source-panel[_ngcontent-%COMP%]   .file-tree[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-row[_ngcontent-%COMP%]   .file-controls[_ngcontent-%COMP%]   .expand-spacer[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.source-panel[_ngcontent-%COMP%]   .file-tree[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-row[_ngcontent-%COMP%]   .file-controls[_ngcontent-%COMP%]   .file-checkbox[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  cursor: pointer;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.source-panel[_ngcontent-%COMP%]   .file-tree[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-row[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  flex: 1;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.source-panel[_ngcontent-%COMP%]   .file-tree[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-row[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-icon[_ngcontent-%COMP%] {\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 0.875rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.source-panel[_ngcontent-%COMP%]   .file-tree[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-row[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%] {\n  color: #ffffff;\n  font-size: 0.875rem;\n  font-weight: 400;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.source-panel[_ngcontent-%COMP%]   .file-tree[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-children[_ngcontent-%COMP%] {\n  padding-left: 1rem;\n  margin-top: 0.25rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.source-panel[_ngcontent-%COMP%]   .file-tree[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-children[_ngcontent-%COMP%]   .child-item[_ngcontent-%COMP%]   .file-row[_ngcontent-%COMP%] {\n  padding: 0.25rem 0.5rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.destination-panel[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  gap: 0.75rem;\n  opacity: 0.6;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.destination-panel[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\n  font-size: 2rem;\n  color: rgba(255, 255, 255, 0.5);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .file-selection-section[_ngcontent-%COMP%]   .file-panels[_ngcontent-%COMP%]   .file-panel.destination-panel[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-text[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: rgba(255, 255, 255, 0.5);\n  text-align: center;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  gap: 1rem;\n  height: 6rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%]   .type-option-horizontal[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  padding: 0;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  background: transparent;\n  transition-property:\n    all,\n    0.2s,\n    ease;\n  transition-timing-function: ease;\n  transition-duration: 300ms;\n  position: relative;\n  overflow: hidden;\n  height: 100%;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%]   .type-option-horizontal[_ngcontent-%COMP%]   .option-content-horizontal[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem 3.5rem 1rem 1rem;\n  width: 100%;\n  height: 100%;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%]   .type-option-horizontal[_ngcontent-%COMP%]   .option-content-horizontal[_ngcontent-%COMP%]   .option-icon-horizontal[_ngcontent-%COMP%] {\n  flex-shrink: 0;\n  width: 2.25rem;\n  height: 2.25rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 0.5rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%]   .type-option-horizontal[_ngcontent-%COMP%]   .option-content-horizontal[_ngcontent-%COMP%]   .option-icon-horizontal[_ngcontent-%COMP%]   .type-icon-horizontal[_ngcontent-%COMP%] {\n  font-size: 2.25rem;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%]   .type-option-horizontal[_ngcontent-%COMP%]   .option-content-horizontal[_ngcontent-%COMP%]   .option-text-horizontal[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%]   .type-option-horizontal[_ngcontent-%COMP%]   .option-content-horizontal[_ngcontent-%COMP%]   .option-text-horizontal[_ngcontent-%COMP%]   .option-title-horizontal[_ngcontent-%COMP%] {\n  font-family: "Noto Sans", sans-serif;\n  font-weight: 500;\n  font-size: 1rem;\n  line-height: 1.25;\n  color: #ffffff;\n  text-align: left;\n  white-space: nowrap;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%]   .type-option-horizontal[_ngcontent-%COMP%]   .option-content-horizontal[_ngcontent-%COMP%]   .option-text-horizontal[_ngcontent-%COMP%]   .option-description-horizontal[_ngcontent-%COMP%] {\n  font-family: "Noto Sans", sans-serif;\n  font-weight: 500;\n  font-size: 0.875rem;\n  line-height: 1.25;\n  color: rgba(255, 255, 255, 0.6);\n  text-align: left;\n  white-space: nowrap;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%]   .type-option-horizontal[_ngcontent-%COMP%]:hover {\n  border-color: rgba(255, 255, 255, 0.2);\n  background: rgba(255, 255, 255, 0.05);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%]   .type-option-horizontal.selected[_ngcontent-%COMP%] {\n  border-color: #ef4e38;\n  background: rgba(239, 78, 56, 0.08);\n  box-shadow: 0 4px 20px rgba(239, 78, 56, 0.15);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%]   .type-option-horizontal.selected[_ngcontent-%COMP%]   .option-content-horizontal[_ngcontent-%COMP%]   .option-icon-horizontal[_ngcontent-%COMP%]   .type-icon-horizontal[_ngcontent-%COMP%] {\n  color: #ef4e38;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%]   .type-option-horizontal.selected[_ngcontent-%COMP%]   .option-content-horizontal[_ngcontent-%COMP%]   .option-text-horizontal[_ngcontent-%COMP%]   .option-title-horizontal[_ngcontent-%COMP%] {\n  color: #ffffff;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%]   .type-option-horizontal.selected[_ngcontent-%COMP%]   .option-content-horizontal[_ngcontent-%COMP%]   .option-text-horizontal[_ngcontent-%COMP%]   .option-description-horizontal[_ngcontent-%COMP%] {\n  color: rgba(255, 255, 255, 0.8);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .type-selection-section[_ngcontent-%COMP%]   .type-options-horizontal[_ngcontent-%COMP%]   .type-option-horizontal[_ngcontent-%COMP%]:active {\n  transform: scale(0.98);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .action-bar[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  padding-top: 1rem;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .action-bar[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      90deg,\n      #ef4e38 0%,\n      #b61e0e 100%);\n  border: none;\n  border-radius: 0.5rem;\n  color: white;\n  padding: 0.75rem 2rem;\n  font-size: 0.875rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 300ms ease;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .action-bar[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover:not(.disabled) {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(239, 78, 56, 0.3);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .action-bar[_ngcontent-%COMP%]   .action-button.disabled[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.1);\n  cursor: not-allowed;\n  opacity: 0.5;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .activestandby-section[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 3rem 2rem;\n  margin-bottom: 1.5rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .activestandby-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n  text-align: center;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .activestandby-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  color: rgba(255, 255, 255, 0.5);\n  opacity: 0.6;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .activestandby-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-text[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: rgba(255, 255, 255, 0.5);\n  opacity: 0.8;\n}\n/*# sourceMappingURL=replication-source.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReplicationSourceComponent, [{
    type: Component,
    args: [{ selector: "app-replication-source", imports: [FontAwesomeModule], template: `<!-- Panel Header -->
@if (!isPanelExpanded()) {
    <header (click)="expandPanel()">
        <h2>Replication Source & Destination</h2>
        <fa-icon icon="server"></fa-icon>
    </header>
}

<!-- Expanded Content -->
@else {
    <section>
        <!-- Source & Destination Selection -->
        <div class="connection-section">
            <div class="connection-row">
                <!-- Source Selection -->
                <div class="connection-group">
                    <fa-icon icon="desktop" class="connection-icon"></fa-icon>
                    <div class="connection-details">
                        <label class="connection-label">Choose Source</label>
                        <div class="connection-dropdown" (click)="onSourceChange($event)">
                            <span class="dropdown-value">{{ selectedSource() || 'Local' }}</span>
                            <fa-icon icon="chevron-down" class="dropdown-arrow"></fa-icon>
                        </div>
                    </div>
                </div>

                <!-- Destination Selection -->
                <div class="connection-group">
                    <fa-icon icon="desktop" class="connection-icon"></fa-icon>
                    <div class="connection-details">
                        <label class="connection-label">Choose Destination</label>
                        <div class="connection-dropdown" (click)="onDestinationChange($event)">
                            <span class="dropdown-value">{{ selectedDestination() || 'Desktop001' }}</span>
                            <fa-icon icon="chevron-down" class="dropdown-arrow"></fa-icon>
                        </div>
                    </div>
                </div>

                <!-- Disconnect Button -->
                <button
                    class="disconnect-button"
                    [class.disabled]="!canEstablishConnection()"
                    [disabled]="!canEstablishConnection()"
                >
                    Disconnect
                </button>
            </div>
        </div>

        <!-- Type Selection -->
        <div class="type-selection-section">
            <div class="type-options-horizontal">
                <button class="type-option-horizontal"
                        [class.selected]="selectedReplicationType() === 'ActiveStandby'"
                        (click)="setReplicationType('ActiveStandby')">
                    <div class="option-content-horizontal">
                        <div class="option-icon-horizontal">
                            <fa-icon icon="desktop" class="type-icon-horizontal"></fa-icon>
                        </div>
                        <div class="option-text-horizontal">
                            <div class="option-title-horizontal">Hypervisor VM</div>
                            <div class="option-description-horizontal">Hypervisor VM</div>
                        </div>
                    </div>
                </button>

                <button class="type-option-horizontal"
                        [class.selected]="selectedReplicationType() === 'File Replication'"
                        (click)="setReplicationType('File Replication')">
                    <div class="option-content-horizontal">
                        <div class="option-icon-horizontal">
                            <fa-icon icon="desktop" class="type-icon-horizontal"></fa-icon>
                        </div>
                        <div class="option-text-horizontal">
                            <div class="option-title-horizontal">Hypervisor VM</div>
                            <div class="option-description-horizontal">Hypervisor VM</div>
                        </div>
                    </div>
                </button>
            </div>
        </div>

        <!-- File Selection Section -->
        @if (isFileReplication()) {
            <div class="file-selection-section">
            <div class="section-header">
                <span class="section-title">Select File Replication source & destination folders</span>
                <span class="destination-label">Destination</span>
            </div>

            <div class="file-panels">
                <!-- Source Panel -->
                <div class="file-panel source-panel">
                    <div class="panel-header">
                        <span class="panel-title">{{ selectedSource() || 'Local' }}</span>
                    </div>
                    <div class="panel-content">
                        <div class="file-tree">
                            @for (item of fileTreeItems; track item.id) {
                                <div class="file-item"
                                     [class.expanded]="expandedFolders().includes(item.id)"
                                     [class.has-children]="item.hasChildren">
                                    <div class="file-row" (click)="item.hasChildren ? toggleFolder(item.id) : null">
                                        <div class="file-controls">
                                            @if (item.hasChildren) {
                                                <button class="expand-toggle"
                                                        [class.expanded]="expandedFolders().includes(item.id)">
                                                    <fa-icon icon="chevron-down"></fa-icon>
                                                </button>
                                            } @else {
                                                <div class="expand-spacer"></div>
                                            }
                                            <input
                                                type="checkbox"
                                                class="file-checkbox"
                                                [checked]="isItemSelected(item.id)"
                                                (change)="toggleItemSelection(item.id, $event)"
                                                (click)="$event.stopPropagation()"
                                            />
                                        </div>
                                        <div class="file-info">
                                            <fa-icon [icon]="item.icon" class="file-icon"></fa-icon>
                                            <span class="file-name">{{ item.name }}</span>
                                        </div>
                                    </div>
                                    @if (item.hasChildren && expandedFolders().includes(item.id)) {
                                        <div class="file-children">
                                            @for (child of item.children; track child.id) {
                                                <div class="file-item child-item">
                                                    <div class="file-row">
                                                        <div class="file-controls">
                                                            <div class="expand-spacer"></div>
                                                            <input
                                                                type="checkbox"
                                                                class="file-checkbox"
                                                                [checked]="isItemSelected(child.id)"
                                                                (change)="toggleItemSelection(child.id, $event)"
                                                            />
                                                        </div>
                                                        <div class="file-info">
                                                            <fa-icon [icon]="child.icon" class="file-icon"></fa-icon>
                                                            <span class="file-name">{{ child.name }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Destination Panel -->
                <div class="file-panel destination-panel">
                    <div class="panel-header">
                        <span class="panel-title">{{ selectedDestination() || 'Desktop001' }}</span>
                    </div>
                    <div class="panel-content">
                        <div class="empty-state">
                            <fa-icon icon="folder" class="empty-icon"></fa-icon>
                            <span class="empty-text">Destination folders will appear here</span>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        }

        <!-- ActiveStandby Section -->
        @if (isActiveStandby()) {
            <div class="activestandby-section">
                <div class="empty-state">
                    <fa-icon icon="shield" class="empty-icon"></fa-icon>
                    <span class="empty-text">ActiveStandby configuration will be available here</span>
                </div>
            </div>
        }

        <!-- Action Bar -->
        <div class="action-bar">
            @if (isFileReplication()) {
                <button
                    class="action-button primary"
                    [class.disabled]="!hasSelectedItems()"
                    [disabled]="!hasSelectedItems()"
                    (click)="selectSource('selected-source')"
                >
                    {{ hasSelectedItems() ? 'Confirm Selection' : 'Choose options first' }}
                </button>
            }
        </div>
    </section>
}
`, styles: ['/* renderer/src/app/replication/shared/replication-source/replication-source.component.less */\n:host {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 0.75rem;\n  overflow: hidden;\n  cursor: pointer;\n  padding: 1rem;\n}\n:host header {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-bottom: 1rem;\n}\n:host header h2 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0;\n  line-height: 1.125rem;\n  color: #ffffff;\n}\n:host header .icon-container {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n:host.collapsed header {\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  padding: 0;\n  height: 100%;\n  flex-direction: row-reverse;\n  justify-content: flex-end;\n}\n:host.collapsed header h2 {\n  font-size: 1rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-align: center;\n  margin: 0;\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  color: #ffffff;\n  letter-spacing: -0.02em;\n  transform: rotate(180deg);\n}\n:host.collapsed header .icon-container {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n:host.collapsed section {\n  display: none;\n}\n:host section {\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n:host section::-webkit-scrollbar {\n  display: none;\n}\n:host section .connection-section .connection-row {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n:host section .connection-section .connection-row .connection-group {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  flex: 1;\n  min-width: 200px;\n}\n:host section .connection-section .connection-row .connection-group .connection-icon {\n  color: #ffffff;\n  font-size: 3rem;\n  opacity: 0.8;\n}\n:host section .connection-section .connection-row .connection-group .connection-details {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n:host section .connection-section .connection-row .connection-group .connection-details .connection-label {\n  font-size: 0.75rem;\n  color: rgba(255, 255, 255, 0.5);\n  font-weight: 500;\n  line-height: 1;\n}\n:host section .connection-section .connection-row .connection-group .connection-details .connection-dropdown {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0.5rem 0.75rem;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  cursor: pointer;\n  transition: all 300ms ease;\n}\n:host section .connection-section .connection-row .connection-group .connection-details .connection-dropdown:hover {\n  background: rgba(255, 255, 255, 0.1);\n  border-color: rgba(255, 255, 255, 0.3);\n}\n:host section .connection-section .connection-row .connection-group .connection-details .connection-dropdown .dropdown-value {\n  color: #ffffff;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n:host section .connection-section .connection-row .connection-group .connection-details .connection-dropdown .dropdown-arrow {\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 0.75rem;\n}\n:host section .connection-section .connection-row .disconnect-button {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  color: #ffffff;\n  padding: 0.625rem 1rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 300ms ease;\n}\n:host section .connection-section .connection-row .disconnect-button:hover:not(.disabled) {\n  background: rgba(255, 255, 255, 0.15);\n  border-color: rgba(255, 255, 255, 0.3);\n}\n:host section .connection-section .connection-row .disconnect-button.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n:host section .file-selection-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n:host section .file-selection-section .section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n:host section .file-selection-section .section-header .section-title {\n  font-size: 0.875rem;\n  color: #ffffff;\n  font-weight: 500;\n}\n:host section .file-selection-section .section-header .destination-label {\n  font-size: 0.875rem;\n  color: rgba(255, 255, 255, 0.5);\n  font-weight: 500;\n}\n:host section .file-selection-section .file-panels {\n  flex: 1;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1rem;\n  overflow: hidden;\n}\n:host section .file-selection-section .file-panels .file-panel {\n  display: flex;\n  flex-direction: column;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  overflow: hidden;\n}\n:host section .file-selection-section .file-panels .file-panel .panel-header {\n  padding: 0.75rem 1rem;\n  background: rgba(255, 255, 255, 0.05);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n:host section .file-selection-section .file-panels .file-panel .panel-header .panel-title {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #ffffff;\n}\n:host section .file-selection-section .file-panels .file-panel .panel-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 0.5rem;\n}\n:host section .file-selection-section .file-panels .file-panel .panel-content::-webkit-scrollbar {\n  width: 4px;\n}\n:host section .file-selection-section .file-panels .file-panel .panel-content::-webkit-scrollbar-track {\n  background: transparent;\n}\n:host section .file-selection-section .file-panels .file-panel .panel-content::-webkit-scrollbar-thumb {\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 2px;\n}\n:host section .file-selection-section .file-panels .file-panel .panel-content::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 255, 255, 0.3);\n}\n:host section .file-selection-section .file-panels .file-panel.source-panel .file-tree .file-item .file-row {\n  display: flex;\n  align-items: center;\n  padding: 0.375rem 0.5rem;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: background-color 300ms ease;\n}\n:host section .file-selection-section .file-panels .file-panel.source-panel .file-tree .file-item .file-row:hover {\n  background: rgba(255, 255, 255, 0.05);\n}\n:host section .file-selection-section .file-panels .file-panel.source-panel .file-tree .file-item .file-row .file-controls {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-right: 0.75rem;\n}\n:host section .file-selection-section .file-panels .file-panel.source-panel .file-tree .file-item .file-row .file-controls .expand-toggle {\n  background: none;\n  border: none;\n  padding: 0;\n  width: 16px;\n  height: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  color: rgba(255, 255, 255, 0.5);\n  transition: transform 300ms ease;\n}\n:host section .file-selection-section .file-panels .file-panel.source-panel .file-tree .file-item .file-row .file-controls .expand-toggle.expanded {\n  transform: rotate(180deg);\n}\n:host section .file-selection-section .file-panels .file-panel.source-panel .file-tree .file-item .file-row .file-controls .expand-toggle fa-icon {\n  font-size: 0.75rem;\n}\n:host section .file-selection-section .file-panels .file-panel.source-panel .file-tree .file-item .file-row .file-controls .expand-spacer {\n  width: 16px;\n  height: 16px;\n}\n:host section .file-selection-section .file-panels .file-panel.source-panel .file-tree .file-item .file-row .file-controls .file-checkbox {\n  width: 16px;\n  height: 16px;\n  cursor: pointer;\n}\n:host section .file-selection-section .file-panels .file-panel.source-panel .file-tree .file-item .file-row .file-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  flex: 1;\n}\n:host section .file-selection-section .file-panels .file-panel.source-panel .file-tree .file-item .file-row .file-info .file-icon {\n  color: rgba(255, 255, 255, 0.5);\n  font-size: 0.875rem;\n}\n:host section .file-selection-section .file-panels .file-panel.source-panel .file-tree .file-item .file-row .file-info .file-name {\n  color: #ffffff;\n  font-size: 0.875rem;\n  font-weight: 400;\n}\n:host section .file-selection-section .file-panels .file-panel.source-panel .file-tree .file-item .file-children {\n  padding-left: 1rem;\n  margin-top: 0.25rem;\n}\n:host section .file-selection-section .file-panels .file-panel.source-panel .file-tree .file-item .file-children .child-item .file-row {\n  padding: 0.25rem 0.5rem;\n}\n:host section .file-selection-section .file-panels .file-panel.destination-panel .empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  gap: 0.75rem;\n  opacity: 0.6;\n}\n:host section .file-selection-section .file-panels .file-panel.destination-panel .empty-state .empty-icon {\n  font-size: 2rem;\n  color: rgba(255, 255, 255, 0.5);\n}\n:host section .file-selection-section .file-panels .file-panel.destination-panel .empty-state .empty-text {\n  font-size: 0.875rem;\n  color: rgba(255, 255, 255, 0.5);\n  text-align: center;\n}\n:host section .type-selection-section {\n  margin-bottom: 1.5rem;\n}\n:host section .type-selection-section .type-options-horizontal {\n  display: flex;\n  flex-direction: row;\n  gap: 1rem;\n  height: 6rem;\n}\n:host section .type-selection-section .type-options-horizontal .type-option-horizontal {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  padding: 0;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  background: transparent;\n  transition-property:\n    all,\n    0.2s,\n    ease;\n  transition-timing-function: ease;\n  transition-duration: 300ms;\n  position: relative;\n  overflow: hidden;\n  height: 100%;\n}\n:host section .type-selection-section .type-options-horizontal .type-option-horizontal .option-content-horizontal {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 1rem 3.5rem 1rem 1rem;\n  width: 100%;\n  height: 100%;\n}\n:host section .type-selection-section .type-options-horizontal .type-option-horizontal .option-content-horizontal .option-icon-horizontal {\n  flex-shrink: 0;\n  width: 2.25rem;\n  height: 2.25rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 0.5rem;\n}\n:host section .type-selection-section .type-options-horizontal .type-option-horizontal .option-content-horizontal .option-icon-horizontal .type-icon-horizontal {\n  font-size: 2.25rem;\n  color: #ffffff;\n}\n:host section .type-selection-section .type-options-horizontal .type-option-horizontal .option-content-horizontal .option-text-horizontal {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n:host section .type-selection-section .type-options-horizontal .type-option-horizontal .option-content-horizontal .option-text-horizontal .option-title-horizontal {\n  font-family: "Noto Sans", sans-serif;\n  font-weight: 500;\n  font-size: 1rem;\n  line-height: 1.25;\n  color: #ffffff;\n  text-align: left;\n  white-space: nowrap;\n}\n:host section .type-selection-section .type-options-horizontal .type-option-horizontal .option-content-horizontal .option-text-horizontal .option-description-horizontal {\n  font-family: "Noto Sans", sans-serif;\n  font-weight: 500;\n  font-size: 0.875rem;\n  line-height: 1.25;\n  color: rgba(255, 255, 255, 0.6);\n  text-align: left;\n  white-space: nowrap;\n}\n:host section .type-selection-section .type-options-horizontal .type-option-horizontal:hover {\n  border-color: rgba(255, 255, 255, 0.2);\n  background: rgba(255, 255, 255, 0.05);\n}\n:host section .type-selection-section .type-options-horizontal .type-option-horizontal.selected {\n  border-color: #ef4e38;\n  background: rgba(239, 78, 56, 0.08);\n  box-shadow: 0 4px 20px rgba(239, 78, 56, 0.15);\n}\n:host section .type-selection-section .type-options-horizontal .type-option-horizontal.selected .option-content-horizontal .option-icon-horizontal .type-icon-horizontal {\n  color: #ef4e38;\n}\n:host section .type-selection-section .type-options-horizontal .type-option-horizontal.selected .option-content-horizontal .option-text-horizontal .option-title-horizontal {\n  color: #ffffff;\n}\n:host section .type-selection-section .type-options-horizontal .type-option-horizontal.selected .option-content-horizontal .option-text-horizontal .option-description-horizontal {\n  color: rgba(255, 255, 255, 0.8);\n}\n:host section .type-selection-section .type-options-horizontal .type-option-horizontal:active {\n  transform: scale(0.98);\n}\n:host section .action-bar {\n  display: flex;\n  justify-content: flex-end;\n  padding-top: 1rem;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n:host section .action-bar .action-button {\n  background:\n    linear-gradient(\n      90deg,\n      #ef4e38 0%,\n      #b61e0e 100%);\n  border: none;\n  border-radius: 0.5rem;\n  color: white;\n  padding: 0.75rem 2rem;\n  font-size: 0.875rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 300ms ease;\n}\n:host section .action-bar .action-button:hover:not(.disabled) {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(239, 78, 56, 0.3);\n}\n:host section .action-bar .action-button.disabled {\n  background: rgba(255, 255, 255, 0.1);\n  cursor: not-allowed;\n  opacity: 0.5;\n}\n:host section .activestandby-section {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 3rem 2rem;\n  margin-bottom: 1.5rem;\n}\n:host section .activestandby-section .empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n  text-align: center;\n}\n:host section .activestandby-section .empty-state .empty-icon {\n  font-size: 3rem;\n  color: rgba(255, 255, 255, 0.5);\n  opacity: 0.6;\n}\n:host section .activestandby-section .empty-state .empty-text {\n  font-size: 0.875rem;\n  color: rgba(255, 255, 255, 0.5);\n  opacity: 0.8;\n}\n/*# sourceMappingURL=replication-source.component.css.map */\n'] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ReplicationSourceComponent, { className: "ReplicationSourceComponent", filePath: "renderer/src/app/replication/shared/replication-source/replication-source.component.ts", lineNumber: 20 });
})();

// renderer/src/app/replication/shared/replication-schedule/replication-schedule.component.ts
function ReplicationScheduleComponent_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "section");
  }
}
var ReplicationScheduleComponent = class _ReplicationScheduleComponent {
  // Input signals
  expandedPanel = input(null);
  // Output events
  onPanelExpand = output();
  onScheduleConfigChange = output();
  // Local state signals
  selectedScheduleConfig = signal("auto");
  // Computed properties
  isPanelExpanded = computed(() => this.expandedPanel() === "schedule");
  isPanelCollapsed = computed(() => {
    const expanded = this.expandedPanel();
    return expanded !== null && expanded !== "schedule";
  });
  // Methods
  expandPanel() {
    this.onPanelExpand.emit("schedule");
  }
  selectScheduleConfig(config) {
    this.selectedScheduleConfig.set(config);
    this.onScheduleConfigChange.emit(config);
  }
  confirmSchedule() {
    if (this.selectedScheduleConfig()) {
      this.onScheduleConfigChange.emit(this.selectedScheduleConfig());
    }
  }
  onChooseOptionsFirst() {
    console.log("Please complete configuration first");
  }
  static \u0275fac = function ReplicationScheduleComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ReplicationScheduleComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ReplicationScheduleComponent, selectors: [["app-replication-schedule"]], inputs: { expandedPanel: [1, "expandedPanel"] }, outputs: { onPanelExpand: "onPanelExpand", onScheduleConfigChange: "onScheduleConfigChange" }, decls: 5, vars: 1, consts: [[3, "click"], ["icon", "calendar"]], template: function ReplicationScheduleComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "header", 0);
      \u0275\u0275listener("click", function ReplicationScheduleComponent_Template_header_click_0_listener() {
        return ctx.expandPanel();
      });
      \u0275\u0275elementStart(1, "h2");
      \u0275\u0275text(2, "Replication Schedule");
      \u0275\u0275elementEnd();
      \u0275\u0275element(3, "fa-icon", 1);
      \u0275\u0275elementEnd();
      \u0275\u0275conditionalCreate(4, ReplicationScheduleComponent_Conditional_4_Template, 1, 0, "section");
    }
    if (rf & 2) {
      \u0275\u0275advance(4);
      \u0275\u0275conditional(ctx.isPanelExpanded() ? 4 : -1);
    }
  }, dependencies: [FontAwesomeModule, FaIconComponent, CommonModule], styles: ["\n\n[_nghost-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 0.75rem;\n  overflow: hidden;\n  cursor: pointer;\n  padding: 1rem;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-bottom: 1rem;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0;\n  line-height: 1.125rem;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  padding: 0;\n  height: 100%;\n  flex-direction: row-reverse;\n  justify-content: flex-end;\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-align: center;\n  margin: 0;\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  color: #ffffff;\n  letter-spacing: -0.02em;\n  transform: rotate(180deg);\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n.collapsed[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  display: none;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n/*# sourceMappingURL=replication-schedule.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReplicationScheduleComponent, [{
    type: Component,
    args: [{ selector: "app-replication-schedule", imports: [FontAwesomeModule, CommonModule], template: '<!-- Panel Header -->\n<header (click)="expandPanel()">\n    <h2>Replication Schedule</h2>\n    <fa-icon icon="calendar"></fa-icon>\n</header>\n\n<!-- Expanded Content -->\n@if (isPanelExpanded()) {\n    <section>\n\n    </section>\n}\n', styles: ["/* renderer/src/app/replication/shared/replication-schedule/replication-schedule.component.less */\n:host {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 0.75rem;\n  overflow: hidden;\n  cursor: pointer;\n  padding: 1rem;\n}\n:host header {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-bottom: 1rem;\n}\n:host header h2 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0;\n  line-height: 1.125rem;\n  color: #ffffff;\n}\n:host header .icon-container {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n:host.collapsed header {\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  padding: 0;\n  height: 100%;\n  flex-direction: row-reverse;\n  justify-content: flex-end;\n}\n:host.collapsed header h2 {\n  font-size: 1rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-align: center;\n  margin: 0;\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  color: #ffffff;\n  letter-spacing: -0.02em;\n  transform: rotate(180deg);\n}\n:host.collapsed header .icon-container {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n:host.collapsed section {\n  display: none;\n}\n:host section {\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n/*# sourceMappingURL=replication-schedule.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ReplicationScheduleComponent, { className: "ReplicationScheduleComponent", filePath: "renderer/src/app/replication/shared/replication-schedule/replication-schedule.component.ts", lineNumber: 13 });
})();

// renderer/src/app/replication/replication.component.ts
var ReplicationComponent = class _ReplicationComponent {
  // State signals
  selectedSource = signal(null);
  selectedDestination = signal(null);
  selectedScheduleConfig = signal(null);
  expandedPanel = signal("source");
  // Button state
  isButtonEnabled() {
    return this.selectedSource() !== null && this.selectedScheduleConfig() !== null;
  }
  // Event handlers
  onSourceSelected(sourceId) {
    this.selectedSource.set(sourceId);
    this.expandedPanel.set("schedule");
  }
  onDestinationSelected(destinationId) {
    this.selectedDestination.set(destinationId);
  }
  onScheduleConfigChanged(config) {
    this.selectedScheduleConfig.set(config);
  }
  onPanelExpand(panel) {
    this.expandedPanel.set(panel);
  }
  resetSelections() {
    this.selectedSource.set(null);
    this.selectedDestination.set(null);
    this.selectedScheduleConfig.set(null);
    this.expandedPanel.set("source");
  }
  static \u0275fac = function ReplicationComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ReplicationComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ReplicationComponent, selectors: [["app-replication"]], decls: 14, vars: 14, consts: [[1, "title"], [1, "actions"], [1, "process", 3, "disabled"], [1, "reset", 3, "click"], ["icon", "rotate-left"], [1, "panel", "replication-source-panel", 3, "sourceSelected", "destinationSelected", "onPanelExpand", "selectedSource", "selectedDestination", "expandedPanel"], [1, "panel", "replication-schedule-panel", 3, "onScheduleConfigChange", "onPanelExpand", "expandedPanel"]], template: function ReplicationComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "header")(1, "div", 0)(2, "h1");
      \u0275\u0275text(3, "Data Replication");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p");
      \u0275\u0275text(5, "What, Where & When");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 1)(7, "button", 2);
      \u0275\u0275text(8);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(9, "button", 3);
      \u0275\u0275listener("click", function ReplicationComponent_Template_button_click_9_listener() {
        return ctx.resetSelections();
      });
      \u0275\u0275element(10, "fa-icon", 4);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(11, "section")(12, "app-replication-source", 5);
      \u0275\u0275listener("sourceSelected", function ReplicationComponent_Template_app_replication_source_sourceSelected_12_listener($event) {
        return ctx.onSourceSelected($event);
      })("destinationSelected", function ReplicationComponent_Template_app_replication_source_destinationSelected_12_listener($event) {
        return ctx.onDestinationSelected($event);
      })("onPanelExpand", function ReplicationComponent_Template_app_replication_source_onPanelExpand_12_listener($event) {
        return ctx.onPanelExpand($event);
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "app-replication-schedule", 6);
      \u0275\u0275listener("onScheduleConfigChange", function ReplicationComponent_Template_app_replication_schedule_onScheduleConfigChange_13_listener($event) {
        return ctx.onScheduleConfigChanged($event);
      })("onPanelExpand", function ReplicationComponent_Template_app_replication_schedule_onPanelExpand_13_listener($event) {
        return ctx.onPanelExpand($event);
      });
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275property("disabled", !ctx.isButtonEnabled());
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.isButtonEnabled() ? "Start Replication" : "Choose options first", " ");
      \u0275\u0275advance(4);
      \u0275\u0275classProp("expanded", ctx.expandedPanel() === "source")("collapsed", ctx.expandedPanel() !== null && ctx.expandedPanel() !== "source");
      \u0275\u0275property("selectedSource", ctx.selectedSource())("selectedDestination", ctx.selectedDestination())("expandedPanel", ctx.expandedPanel());
      \u0275\u0275advance();
      \u0275\u0275classProp("expanded", ctx.expandedPanel() === "schedule")("collapsed", ctx.expandedPanel() !== null && ctx.expandedPanel() !== "schedule");
      \u0275\u0275property("expandedPanel", ctx.expandedPanel());
    }
  }, dependencies: [
    FontAwesomeModule,
    FaIconComponent,
    ReplicationSourceComponent,
    ReplicationScheduleComponent
  ], styles: ["\n\n[_nghost-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  height: 100%;\n  padding: 1rem;\n  overflow: hidden;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 0ms;\n  animation-name: fadeInUp;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  transition-property:\n    color,\n    background,\n    box-shadow;\n  transition-timing-function: ease;\n  transition-duration: 300ms;\n  border: none;\n  padding: 1rem;\n  border-radius: 0.75rem;\n  font-size: 1rem;\n  color: white;\n  cursor: pointer;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   button.process[_ngcontent-%COMP%] {\n  min-width: 16.25rem;\n  background:\n    linear-gradient(\n      90deg,\n      #ef4e38 0%,\n      #b61e0e 100%);\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   button.process[_ngcontent-%COMP%]:disabled {\n  cursor: not-allowed;\n  color: rgba(255, 255, 255, 0.5);\n  background: rgba(255, 255, 255, 0.1);\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   button.process[_ngcontent-%COMP%]:hover:not(:disabled) {\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   button.reset[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.1);\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   button.reset[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  gap: 1rem;\n  width: 100%;\n  min-width: 0;\n  overflow: hidden;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .panel[_ngcontent-%COMP%] {\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 50ms;\n  animation-name: fadeInUp;\n  transition-property: flex, box-shadow;\n  transition-timing-function: ease;\n  transition-duration: 300ms;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .panel[_ngcontent-%COMP%]:nth-child(2) {\n  animation-delay: 100ms;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .panel[_ngcontent-%COMP%]:nth-child(3) {\n  animation-delay: 150ms;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .panel.expanded[_ngcontent-%COMP%] {\n  flex: 1;\n  cursor: default;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .panel.collapsed[_ngcontent-%COMP%] {\n  flex: 0 0 5rem;\n  width: 5rem;\n  align-items: center;\n  justify-content: flex-start;\n  position: relative;\n}\n/*# sourceMappingURL=replication.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReplicationComponent, [{
    type: Component,
    args: [{ selector: "app-replication", imports: [
      FontAwesomeModule,
      ReplicationSourceComponent,
      ReplicationScheduleComponent
    ], template: `<!-- Header Section -->\r
<header>\r
    <div class="title">\r
        <h1>Data Replication</h1>\r
        <p>What, Where & When</p>\r
    </div>\r
    <div class="actions">\r
        <button class="process" [disabled]="!isButtonEnabled()">\r
            {{ isButtonEnabled() ? 'Start Replication' : 'Choose options first' }}\r
        </button>\r
        <button class="reset" (click)="resetSelections()">\r
            <fa-icon icon="rotate-left"></fa-icon>\r
        </button>\r
    </div>\r
</header>\r
\r
<!-- Main Content Container -->\r
<section>\r
    <!-- Replication Source & Destination Panel -->\r
    <app-replication-source class="panel replication-source-panel"\r
        [selectedSource]="selectedSource()"\r
        [selectedDestination]="selectedDestination()"\r
        [expandedPanel]="expandedPanel()"\r
        [class.expanded]="expandedPanel() === 'source'"\r
        [class.collapsed]="expandedPanel() !== null && expandedPanel() !== 'source'"\r
        (sourceSelected)="onSourceSelected($event)"\r
        (destinationSelected)="onDestinationSelected($event)"\r
        (onPanelExpand)="onPanelExpand($event)"\r
    ></app-replication-source>\r
\r
    <!-- Replication Schedule Panel -->\r
    <app-replication-schedule class="panel replication-schedule-panel"\r
        [expandedPanel]="expandedPanel()"\r
        [class.expanded]="expandedPanel() === 'schedule'"\r
        [class.collapsed]="expandedPanel() !== null && expandedPanel() !== 'schedule'"\r
        (onScheduleConfigChange)="onScheduleConfigChanged($event)"\r
        (onPanelExpand)="onPanelExpand($event)"\r
    ></app-replication-schedule>\r
</section>\r
`, styles: ["/* renderer/src/app/replication/replication.component.less */\n:host {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  height: 100%;\n  padding: 1rem;\n  overflow: hidden;\n}\n:host header {\n  display: flex;\n  justify-content: space-between;\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 0ms;\n  animation-name: fadeInUp;\n}\n:host header .title h1 {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  color: #ffffff;\n}\n:host header .title p {\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n}\n:host header .actions {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n:host header .actions button {\n  transition-property:\n    color,\n    background,\n    box-shadow;\n  transition-timing-function: ease;\n  transition-duration: 300ms;\n  border: none;\n  padding: 1rem;\n  border-radius: 0.75rem;\n  font-size: 1rem;\n  color: white;\n  cursor: pointer;\n}\n:host header .actions button.process {\n  min-width: 16.25rem;\n  background:\n    linear-gradient(\n      90deg,\n      #ef4e38 0%,\n      #b61e0e 100%);\n}\n:host header .actions button.process:disabled {\n  cursor: not-allowed;\n  color: rgba(255, 255, 255, 0.5);\n  background: rgba(255, 255, 255, 0.1);\n}\n:host header .actions button.process:hover:not(:disabled) {\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\n}\n:host header .actions button.reset {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.1);\n}\n:host header .actions button.reset:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n:host section {\n  flex: 1;\n  display: flex;\n  gap: 1rem;\n  width: 100%;\n  min-width: 0;\n  overflow: hidden;\n}\n:host section .panel {\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 50ms;\n  animation-name: fadeInUp;\n  transition-property: flex, box-shadow;\n  transition-timing-function: ease;\n  transition-duration: 300ms;\n}\n:host section .panel:nth-child(2) {\n  animation-delay: 100ms;\n}\n:host section .panel:nth-child(3) {\n  animation-delay: 150ms;\n}\n:host section .panel.expanded {\n  flex: 1;\n  cursor: default;\n}\n:host section .panel.collapsed {\n  flex: 0 0 5rem;\n  width: 5rem;\n  align-items: center;\n  justify-content: flex-start;\n  position: relative;\n}\n/*# sourceMappingURL=replication.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ReplicationComponent, { className: "ReplicationComponent", filePath: "renderer/src/app/replication/replication.component.ts", lineNumber: 16 });
})();
export {
  ReplicationComponent
};
//# sourceMappingURL=chunk-IFJPI6LA.js.map
