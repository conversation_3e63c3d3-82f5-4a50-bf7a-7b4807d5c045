{"version": 3, "file": "index.js", "sources": ["../../main/src/defaults.ts", "../../main/src/app.ts", "../../main/src/main.ts"], "sourcesContent": ["/**\n * Default configs\n * <AUTHOR>\n */\n\nclass Defaults {\n    // App\n    readonly appId = 'com.actiphy.backup-and-standby';\n    readonly title = 'Actiphy Backup and Standby';\n    readonly bgColor = '#f0f0f0';\n\n    // Main Window\n    readonly mainWindowWidth = 1440;\n    readonly mainWindowHeight = 780;\n}\n\nexport const defaults = new Defaults();\n", "/**\r\n * Application\r\n * <AUTHOR>\r\n */\r\n\r\nimport { app, Menu, BrowserWindow, shell } from 'electron';\r\nimport { defaults } from './defaults';\r\n\r\nexport class App {\r\n    // Keep a global reference of the window object, if you don't, the window will\r\n    // be closed automatically when the JavaScript object is garbage collected.\r\n    private window?: BrowserWindow;\r\n\r\n    constructor(private platform: NodeJS.Platform) { }\r\n\r\n    run(): void {\r\n        // Single instance lock\r\n        if (!app.requestSingleInstanceLock()) {\r\n            app.quit();\r\n        }\r\n\r\n        app.on('second-instance', () => this.openMainWindow());\r\n\r\n        // This method will be called when Electron has finished\r\n        // initialization and is ready to create browser windows.\r\n        // Some APIs can only be used after this event occurs.\r\n        app.on('ready', () => {\r\n            if (this.platform === 'win32') {\r\n                app.setAppUserModelId(defaults.appId);\r\n                // hidden menu bar\r\n                Menu.setApplicationMenu(null);\r\n            }\r\n\r\n            // Create main window\r\n            this.createMainWindow();\r\n        });\r\n\r\n        app.on('activate', () => {\r\n            // On macOS it's common to re-create a window in the app when the\r\n            // dock icon is clicked and there are no other windows open.\r\n            if (!this.window) {\r\n                this.createMainWindow();\r\n            }\r\n        });\r\n\r\n        // Quit when all windows are closed.\r\n        app.on('window-all-closed', () => {\r\n            // On macOS it is common for applications and their menu bar\r\n            // to stay active until the user quits explicitly with Cmd + Q\r\n            if (this.platform !== 'darwin') {\r\n                app.quit();\r\n            }\r\n        });\r\n\r\n        app.on('will-quit', () => {\r\n            if (this.platform === 'darwin') {\r\n                app.dock?.hide();\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Create Main window\r\n     */\r\n    private createMainWindow(): void {\r\n        // Create the browser window.\r\n        this.window = new BrowserWindow({\r\n            show: false,\r\n            frame: false,\r\n            resizable: true,\r\n            width: defaults.mainWindowWidth,\r\n            height: defaults.mainWindowHeight,\r\n            title: defaults.title,\r\n            backgroundColor: defaults.bgColor,\r\n            titleBarStyle: 'hidden',\r\n            titleBarOverlay: {\r\n                color: \"#142a33\",\r\n                symbolColor: \"#ffffff\",\r\n                height: 39,\r\n            },\r\n            webPreferences: {\r\n                devTools: !process.env['prod'],\r\n                spellcheck: false,\r\n                enableWebSQL: false,\r\n            },\r\n        });\r\n\r\n        this.window.once('ready-to-show', () => this.window?.show());\r\n\r\n        // Emitted when the window is closed.\r\n        this.window.once('closed', () => {\r\n            // Dereference the window object, usually you would store windows\r\n            // in an array if your app supports multi windows, this is the time\r\n            // when you should delete the corresponding element.\r\n            this.window = undefined;\r\n        });\r\n\r\n        // And load the index.html of the app.\r\n        this.window.loadFile('browser/index.html');\r\n\r\n        this.window.webContents.setWindowOpenHandler(details => {\r\n            shell.openExternal(details.url);\r\n            return { action: 'deny' };\r\n        });\r\n\r\n        if (!process.env['prod']) {\r\n            this.window.webContents.openDevTools({ mode: 'undocked' });\r\n        }\r\n    }\r\n\r\n    private openMainWindow(): void {\r\n        if (this.platform === 'darwin') {\r\n            app.dock?.show();\r\n        }\r\n\r\n        if (this.window) {\r\n            if (this.window.isMinimized()) {\r\n                this.window.restore();\r\n            }\r\n\r\n            this.window.focus();\r\n            this.window.show();\r\n        } else {\r\n            this.createMainWindow();\r\n        }\r\n    }\r\n}\r\n", "/**\n * Main process\n * <AUTHOR>\n */\n\nimport { App } from './app';\n\n// Create App\nconst app = new App(process.platform);\n\n// Run\napp.run();\n"], "names": ["De<PERSON>ults", "defaults", "App", "platform", "app", "<PERSON><PERSON>", "BrowserWindow", "details", "shell"], "mappings": "yCAKA,MAAMA,CAAS,CAEF,MAAQ,iCACR,MAAQ,6BACR,QAAU,UAGV,gBAAkB,KAClB,iBAAmB,GAChC,CAEa,MAAAC,EAAW,IAAID,ECRrB,MAAME,CAAI,CAKb,YAAoBC,EAA2B,CAA3B,KAAA,SAAAA,CAAA,CAFZ,OAIR,KAAY,CAEHC,EAAAA,IAAI,6BACLA,EAAAA,IAAI,KAAK,EAGbA,EAAAA,IAAI,GAAG,kBAAmB,IAAM,KAAK,gBAAgB,EAKjDA,MAAA,GAAG,QAAS,IAAM,CACd,KAAK,WAAa,UACdA,MAAA,kBAAkBH,EAAS,KAAK,EAEpCI,EAAA,KAAK,mBAAmB,IAAI,GAIhC,KAAK,iBAAiB,CAAA,CACzB,EAEGD,MAAA,GAAG,WAAY,IAAM,CAGhB,KAAK,QACN,KAAK,iBAAiB,CAC1B,CACH,EAGGA,MAAA,GAAG,oBAAqB,IAAM,CAG1B,KAAK,WAAa,UAClBA,EAAAA,IAAI,KAAK,CACb,CACH,EAEGA,MAAA,GAAG,YAAa,IAAM,CAClB,KAAK,WAAa,UAClBA,EAAA,IAAI,MAAM,KAAK,CACnB,CACH,CAAA,CAMG,kBAAyB,CAExB,KAAA,OAAS,IAAIE,gBAAc,CAC5B,KAAM,GACN,MAAO,GACP,UAAW,GACX,MAAOL,EAAS,gBAChB,OAAQA,EAAS,iBACjB,MAAOA,EAAS,MAChB,gBAAiBA,EAAS,QAC1B,cAAe,SACf,gBAAiB,CACb,MAAO,UACP,YAAa,UACb,OAAQ,EACZ,EACA,eAAgB,CACZ,SAAU,CAAC,QAAQ,IAAI,KACvB,WAAY,GACZ,aAAc,EAAA,CAClB,CACH,EAED,KAAK,OAAO,KAAK,gBAAiB,IAAM,KAAK,QAAQ,MAAM,EAGtD,KAAA,OAAO,KAAK,SAAU,IAAM,CAI7B,KAAK,OAAS,MAAA,CACjB,EAGI,KAAA,OAAO,SAAS,oBAAoB,EAEpC,KAAA,OAAO,YAAY,qBAAgCM,IAC9CC,QAAA,aAAaD,EAAQ,GAAG,EACvB,CAAE,OAAQ,MAAO,EAC3B,EAEI,QAAQ,IAAI,MACb,KAAK,OAAO,YAAY,aAAa,CAAE,KAAM,WAAY,CAC7D,CAGI,gBAAuB,CACvB,KAAK,WAAa,UAClBH,EAAA,IAAI,MAAM,KAAK,EAGf,KAAK,QACD,KAAK,OAAO,eACZ,KAAK,OAAO,QAAQ,EAGxB,KAAK,OAAO,MAAM,EAClB,KAAK,OAAO,KAAK,GAEjB,KAAK,iBAAiB,CAC1B,CAER,CCtHA,MAAMA,EAAM,IAAIF,EAAI,QAAQ,QAAQ,EAGpCE,EAAI,IAAI"}