import { Component, input, output, computed, signal } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { CommonModule } from '@angular/common';

export type ScheduleConfigType = 'auto' | 'manual';

@Component({
    selector: 'app-replication-schedule',
    imports: [FontAwesomeModule, CommonModule],
    templateUrl: './replication-schedule.component.html',
    styleUrls: ['./replication-schedule.component.less']
})
export class ReplicationScheduleComponent {
    // Input signals
    expandedPanel = input<string | null>(null);

    // Output events
    onPanelExpand = output<'schedule' | 'source'>();
    onScheduleConfigChange = output<ScheduleConfigType>();

    // Local state signals
    selectedScheduleConfig = signal<ScheduleConfigType>('auto');

    // Computed properties
    isPanelExpanded = computed(() => this.expandedPanel() === 'schedule');
    isPanelCollapsed = computed(() => {
        const expanded = this.expandedPanel();
        return expanded !== null && expanded !== 'schedule';
    });

    // Methods
    expandPanel(): void {
        this.onPanelExpand.emit('schedule');
    }

    selectScheduleConfig(config: ScheduleConfigType): void {
        this.selectedScheduleConfig.set(config);
        this.onScheduleConfigChange.emit(config);
    }

    confirmSchedule(): void {
        if (this.selectedScheduleConfig()) {
            this.onScheduleConfigChange.emit(this.selectedScheduleConfig());
        }
    }

    onChooseOptionsFirst(): void {
        // Handle the disabled button click - this is just a placeholder
        console.log('Please complete configuration first');
    }
}
