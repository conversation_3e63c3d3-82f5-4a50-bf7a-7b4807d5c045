{"version": 3, "sources": ["renderer/src/app/preferences/shared/preferences.less", "renderer/src/styles/mixins.less", "renderer/src/styles/variables.less"], "sourcesContent": ["@import \"../../../styles/variables.less\";\n@import \"../../../styles/mixins.less\";\n\n:host {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    gap: 1rem;\n    overflow: hidden;\n    padding: 16px;\n    border-radius: @border-radius-small;\n    border: 1px solid rgba(255, 255, 255, 0.2);\n    background: rgba(255, 255, 255, 0.1);\n\n    .animation(fade-in-up);\n\n    header {\n        display: flex;\n        align-items: center;\n        gap: .5rem;\n        border-radius: @border-radius-small;\n\n        .icon {\n            font-size: 1rem;\n        }\n\n        h2 {\n            margin: 0;\n            font-family: @font-family;\n            font-weight: 500;\n            font-size: 1rem;\n            line-height: 1.25;\n            color: @text-color;\n        }\n    }\n\n    section {\n        flex: 1;\n        font-size: .875rem;\n    }\n\n    footer {\n        display: flex;\n        justify-content: flex-end;\n        align-items: center;\n        flex: 0 0 auto;\n        gap: 1rem;\n\n        .notice {\n            flex: 1;\n            font-size: .875rem;\n            line-height: 1.36;\n            color: @text-color;\n        }\n\n        .buttons {\n            .button {\n                min-width: 12rem;\n            }\n        }\n    }\n}\n", "/**\n * Mixin\n * <AUTHOR>\n */\n\n@import \"variables.less\";\n\n// Transition\n.transition(...) {\n    & when (length(@arguments) = 0) {\n        transition: none;\n    }\n\n    & when (length(@arguments) = 1) {\n        transition: @arguments 300ms ease;\n    }\n\n    & when (length(@arguments) > 1) {\n        @properties: replace(\"@{arguments}\", \" \", \", \", \"g\");\n        transition-property: ~\"@{properties}\";\n        transition-timing-function: ease;\n        transition-duration: 300ms;\n    }\n}\n\n// Animations\n.animation(@name, @delay: 0ms) {\n    animation-duration: 500ms;\n    animation-fill-mode: forwards;\n    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n    animation-delay: @delay;\n\n    & when (@name = fade-in-up) {\n        animation-name: fadeInUp;\n    }\n\n    & when (@name = fade-in-left) {\n        animation-name: fadeInLeft;\n    }\n\n    & when (@name = fade-in) {\n        animation-name: fadeIn;\n    }\n}\n", "// variables\r\n@primary-color: #ef4e38;\r\n@primary-gradient: linear-gradient(90deg, #ef4e38 0%, #b61e0e 100%);\r\n@success-color: #34d399;\r\n@success-bg: rgba(16, 185, 129, 0.2);\r\n@dark-bg: linear-gradient(284deg, #152b34 0%, #254b5c 100%);\r\n@background-dark: #1a2a33;\r\n@bg-overlay: rgba(255, 255, 255, 0.1);\r\n@light-overlay: rgba(255, 255, 255, 0.05);\r\n@border-color: rgba(255, 255, 255, 0.1);\r\n@text-color: rgba(255, 255, 255, 1);\r\n@text-secondary: rgba(255, 255, 255, 0.5);\r\n@border-radius: .75rem;\r\n@border-radius-small: .5rem;\r\n@font-family: 'inter', 'noto sans', sans-serif;\r\n"], "mappings": ";AAGA;AACI,WAAA;AACA,kBAAA;AACA,UAAA;AACA,OAAA;AACA,YAAA;AACA,WAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;ACeA,sBAAA;AACA,uBAAA;AACA,6BAA2B,aAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA;AAC3B,mBAAA;AAGI,kBAAA;;AD9BR,MAaI;AACI,WAAA;AACA,eAAA;AACA,OAAA;AACA,iBAAA;;AAjBR,MAaI,OAMI,CAAA;AACI,aAAA;;AApBZ,MAaI,OAUI;AACI,UAAA;AACA;IEdZ,OAAA;IAAS,WAAA;IAAA;AFeG,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AA7BZ,MAiCI;AACI,QAAA;AACA,aAAA;;AAnCR,MAsCI;AACI,WAAA;AACA,mBAAA;AACA,eAAA;AACA,QAAA,EAAA,EAAA;AACA,OAAA;;AA3CR,MAsCI,OAOI,CAAA;AACI,QAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AAjDZ,MAsCI,OAcI,CAAA,QACI,CAAA;AACI,aAAA;;", "names": []}