<div class="disk-mapping" cdkDropListGroup>
        <!-- Source disk column -->
    <div class="source-column-container">
        <div class="source-column">
            <div class="column-header">
                <h4>Backup Image</h4>
            </div>
            @for (disk of sourceDisks(); track disk.id) {
                <div class="disk-item">
                    <!-- Disk header -->
                    <div class="disk-header">
                        <span class="disk-name">{{ disk.name }}</span>
                        <span class="disk-type">{{ disk.type }}</span>
                        <span class="disk-info">{{ disk.capacity }}</span>
                    </div>

                    <div class="disk-body">
                        <div class="disk-icons">
                            <fa-icon icon="database"></fa-icon>
                            <fa-icon icon="play"></fa-icon>
                        </div>
                        <!-- Partitions -->
                        <div class="disk-partitions">
                            @for (partition of disk.partitions; track partition.id) {
                                <div class="partition-item">
                                    <div class="partition-header">
                                        <fa-icon icon="hard-drive" class="partition-icon"></fa-icon>
                                        <span class="partition-name">{{ partition.name }}</span>
                                        <button class="more-options" (click)="onMoreOptions(partition.id)">
                                            <fa-icon icon="play"></fa-icon>
                                        </button>
                                    </div>
                                    <div class="partition-usage">
                                        <div class="usage-bar">
                                            <div class="usage-fill" [style.width.%]="partition.usage"></div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
        <div class="source-column">
            <div class="column-header">
                <h4>Backup Image</h4>
            </div>
            @for (disk of sourceDisks(); track disk.id) {
                <div class="disk-item">
                    <!-- Disk header -->
                    <div class="disk-header">
                        <span class="disk-name">{{ disk.name }}</span>
                        <span class="disk-type">{{ disk.type }}</span>
                        <span class="disk-info">{{ disk.capacity }}</span>
                    </div>

                    <div class="disk-body">
                        <div class="disk-icons">
                            <fa-icon icon="database"></fa-icon>
                            <fa-icon icon="play"></fa-icon>
                        </div>
                        <!-- Partitions -->
                        <div class="disk-partitions" cdkDropList [cdkDropListData]="disk.partitions" cdkDropListSortingDisabled="true">
                            @for (partition of disk.partitions; track partition.id) {
                                <div class="partition-item" cdkDrag [cdkDragData]="partition">
                                    <div class="partition-header">
                                        <fa-icon icon="hard-drive" class="partition-icon"></fa-icon>
                                        <span class="partition-name">{{ partition.name }}</span>
                                        <button class="more-options" (click)="onMoreOptions(partition.id)">
                                            <fa-icon icon="play"></fa-icon>
                                        </button>
                                    </div>
                                    <div class="partition-usage">
                                        <div class="usage-bar">
                                            <div class="usage-fill" [style.width.%]="partition.usage"></div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- Arrow section -->
    <div class="arrow-section">
        <div class="mapping-arrows">
            <div class="arrow-line"></div>
            @for (item of targets(); track $index) {
                @for (part of item.partitions; track $index) {
                    <div class="arrow-line"></div>
                }
            }
        </div>
    </div>

    <!-- Target disk column -->
    <div class="target-column">
        <div class="column-header">
            <h4>After Recovery</h4>
        </div>
        @for (disk of targets(); track disk.id) {
            <div class="disk-item">
                <!-- Disk header -->
                <div class="disk-header">
                    <span class="disk-name">{{ disk.name }}</span>
                    <span class="disk-type">{{ disk.type }}</span>
                    <span class="disk-info">{{ disk.capacity }}</span>
                </div>

                <div class="disk-body">
                    <div class="disk-icons">
                        <fa-icon icon="database"></fa-icon>
                        <fa-icon icon="ellipsis"></fa-icon>
                    </div>
                    <!-- Partitions -->
                    <div class="disk-partitions" cdkDropList [cdkDropListData]="disk.partitions" [cdkDropListEnterPredicate]="customEnterPredicate" (cdkDropListDropped)="onPartitionDrop($event)">
                        @for (partition of disk.partitions; track partition.id) {
                            <div class="partition-item" cdkDrag [cdkDragData]="partition">
                                <div class="partition-header">
                                    <fa-icon icon="hard-drive" class="partition-icon"></fa-icon>
                                    <span class="partition-name">{{ partition.name }}</span>
                                    <button class="more-options" (click)="onMoreOptions(partition.id)">
                                        <fa-icon icon="ellipsis"></fa-icon>
                                    </button>
                                </div>
                                <div class="partition-usage">
                                    <div class="usage-bar">
                                        <div class="usage-fill" [style.width.%]="partition.usage"></div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
</div>
