<header>
    <div class="icon">
        <fa-icon icon="rocket"></fa-icon>
    </div>
    <h2>Hyperback</h2>
</header>

<section>
    <div class="section">
        <h3>Options</h3>

        <div class="subsection">
            <h4>Operation Mode</h4>

            <div class="form-group">
                <label class="form-label">
                    <input type="radio" class="form-radio" name="operationMode" value="parallel" [(ngModel)]="operationMode">
                    <span class="form-radio-label">Parallel Backup (backup VMs in parallel)</span>
                </label>
            </div>

            <div class="form-group">
                <label class="form-label">
                    <input type="radio" class="form-radio" name="operationMode" value="sequential" [(ngModel)]="operationMode">
                    <span class="form-radio-label">Sequential Backup (backup VMs sequentially)</span>
                </label>
            </div>

            <div class="form-group inline">
                <label class="form-label">Run</label>
                <div class="form-select-wrapper">
                    <select class="form-select" [(ngModel)]="concurrentTasks">
                        @for (num of [1,2,3,4,5,6,7,8,9,10]; track num) {
                            <option [value]="num">{{ num }}</option>
                        }
                    </select>
                </div>
                <label class="form-label">HyperBack Task(s) simultaneously</label>
            </div>
        </div>

        <div class="subsection">
            <h4>Tracking Method</h4>

            <div class="form-group">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" [(ngModel)]="useResilientChangeTracking">
                    <span class="form-checkbox-label">Use Microsoft Resilient Change Tracking (RCT)</span>
                </label>
                <div class="form-description">
                    * * This tracking method only applies to backing up of Generation 2 VMs version 8.0 or later (on Server 2016 or later).
                </div>
            </div>
        </div>

        <div class="subsection">
            <h4>Backup Image File Optimization</h4>

            <div class="form-group">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" [(ngModel)]="createOptimizedBackupImage">
                    <span class="form-checkbox-label">Create optimized backup image file</span>
                </label>
                <div class="form-description">
                    * * This option can exclude pagefile.sys and hiberfil.sys, etc. to make the backup image smaller.
                </div>
            </div>
        </div>
    </div>
</section>

<footer>
    <span class="notice">0 Configuration changes waiting to be applied</span>
    <div class="buttons">
        <button type="button" class="button secondary" (click)="onCancel()">Cancel</button>
        <button type="button" class="button primary" (click)="onApply()">Apply</button>
    </div>
</footer>
