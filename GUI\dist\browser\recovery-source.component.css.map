{"version": 3, "sources": ["renderer/src/app/recovery/shared/recovery-source/recovery-source.component.less", "renderer/src/styles/mixins.less"], "sourcesContent": ["@import '../../../../styles/variables.less';\n@import '../../../../styles/mixins.less';\n\n:host {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 0.75rem;\n    overflow: hidden;\n    cursor: pointer;\n    padding: 1rem;\n\n    // Panel Header\n    header {\n        flex: 0 0 auto;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        gap: 1rem;\n        padding-bottom: 1rem;\n\n        h2 {\n            font-size: 1.125rem;\n            font-weight: 600;\n            margin: 0;\n            line-height: 1.125rem;\n            color: @text-color;\n        }\n\n        .icon-container {\n            width: 20px;\n            height: 20px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            flex-shrink: 0;\n            border-radius: 6px;\n            background: rgba(255, 255, 255, 0);\n            padding: 0;\n        }\n    }\n\n    // Collapsed vertical panel styling\n    &.collapsed {\n        header {\n            writing-mode: vertical-lr;\n            text-orientation: mixed;\n            padding: 0;\n            height: 100%;\n            flex-direction: row-reverse;\n            justify-content: flex-end;\n\n            h2 {\n                font-size: 1rem;\n                font-weight: 600;\n                line-height: 1.2;\n                text-align: center;\n                margin: 0;\n                writing-mode: vertical-lr;\n                text-orientation: mixed;\n                color: @text-color;\n                letter-spacing: -0.02em;\n                transform: rotate(180deg);\n            }\n\n            .icon-container {\n                width: 20px;\n                height: 20px;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                flex-shrink: 0;\n                border-radius: 6px;\n                background: rgba(255, 255, 255, 0);\n                padding: 0;\n            }\n        }\n\n        section {\n            display: none;\n        }\n    }\n\n    section {\n        flex: 1;\n        overflow-y: auto;\n        overflow-x: hidden;\n\n        // Hide scrollbar for Chromium/WebKit (Electron)\n        &::-webkit-scrollbar {\n            display: none;\n        }\n\n        .local-panel {\n            display: flex;\n            flex-direction: column;\n            gap: 1rem;\n            background: rgba(255, 255, 255, 0.1);\n            border: 1px solid rgba(255, 255, 255, 0.2);\n            border-radius: .5rem;\n            padding: 1rem;\n\n            .local-header {\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n\n                .local-title {\n                    display: flex;\n                    align-items: center;\n                    font-size: 1rem;\n                    font-weight: 500;\n                    color: @text-color;\n                }\n\n                .filter-section {\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    width: 18px;\n                    height: 18px;\n                    color: @text-color;\n                }\n            }\n\n            .recovery-table {\n                background: rgba(16, 185, 129, 0.2);\n                border: 1px solid rgba(16, 185, 129, 0.5);\n                border-radius: 0.5rem;\n                overflow: hidden;\n\n                .table-header {\n                    display: flex;\n                    background: rgba(255, 255, 255, 0.05);\n\n                    .header-col {\n                        display: flex;\n                        align-items: center;\n                        padding: 8px 10px;\n                        font-size: 0.75rem;\n                        font-weight: 400;\n                        color: @text-color;\n                        line-height: 1.36;\n\n                        &.recovery-point {\n                            flex: 1;\n                            justify-content: flex-start;\n                            padding-left: 18px;\n                        }\n\n                        &.size {\n                            width: 175px;\n                            justify-content: flex-end;\n                            padding-right: 20px;\n                        }\n                    }\n                }\n\n                .table-body {\n                    .table-row {\n                        display: flex;\n                        border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n                        .transition(background-color);\n\n                        &:hover {\n                            background: rgba(255, 255, 255, 0.05);\n                        }\n\n                        &.selected {\n                            background: rgba(16, 185, 129, 0.1);\n                        }\n\n                        &:last-child {\n                            border-bottom: none;\n                        }\n\n                        .row-col {\n                            display: flex;\n                            align-items: center;\n                            padding: 8px 10px;\n                            font-size: 0.75rem;\n                            color: @text-color;\n                            line-height: 1.36;\n                            min-height: 31px;\n\n                            &.recovery-point {\n                                flex: 1;\n                                padding-left: 8px;\n\n                                .row-content {\n                                    display: flex;\n                                    align-items: center;\n                                    gap: 10px;\n                                    width: 100%;\n\n                                    .spacer {\n                                        width: 15px;\n                                        height: 15px;\n                                    }\n\n                                    .tree-icon {\n                                        width: 15px;\n                                        height: 15px;\n                                        position: relative;\n\n                                        .tree-expand {\n                                            width: 15px;\n                                            height: 15px;\n                                            background: rgba(255, 255, 255, 0.2);\n                                            border-radius: 3px;\n                                            position: relative;\n\n                                            &::after {\n                                                content: '';\n                                                position: absolute;\n                                                top: 6px;\n                                                left: 1.5px;\n                                                width: 12px;\n                                                height: 3px;\n                                                background: #D9D9D9;\n                                                border-radius: 2px;\n                                            }\n                                        }\n                                    }\n\n                                    .host-icon {\n                                        width: 16px;\n                                        height: 14px;\n                                        color: @text-color;\n                                    }\n\n                                    .date-text {\n                                        flex: 1;\n                                        text-align: left;\n                                    }\n                                }\n                            }\n\n                            &.size {\n                                width: 175px;\n                                justify-content: flex-end;\n                                padding-right: 20px;\n                                text-align: right;\n                            }\n                        }\n                    }\n                }\n            }\n\n            .recovery-actions {\n                display: flex;\n                justify-content: flex-end;\n            }\n        }\n    }\n}\n", "/**\n * Mixin\n * <AUTHOR>\n */\n\n@import \"variables.less\";\n\n// Transition\n.transition(...) {\n    & when (length(@arguments) = 0) {\n        transition: none;\n    }\n\n    & when (length(@arguments) = 1) {\n        transition: @arguments 300ms ease;\n    }\n\n    & when (length(@arguments) > 1) {\n        @properties: replace(\"@{arguments}\", \" \", \", \", \"g\");\n        transition-property: ~\"@{properties}\";\n        transition-timing-function: ease;\n        transition-duration: 300ms;\n    }\n}\n\n// Animations\n.animation(@name, @delay: 0ms) {\n    animation-duration: 500ms;\n    animation-fill-mode: forwards;\n    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n    animation-delay: @delay;\n\n    & when (@name = fade-in-up) {\n        animation-name: fadeInUp;\n    }\n\n    & when (@name = fade-in-left) {\n        animation-name: fadeInLeft;\n    }\n\n    & when (@name = fade-in) {\n        animation-name: fadeIn;\n    }\n}\n"], "mappings": ";AAGA;AACI,WAAA;AACA,kBAAA;AACA,UAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,YAAA;AACA,UAAA;AACA,WAAA;;AARJ,MAWI;AACI,QAAA,EAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,kBAAA;;AAjBR,MAWI,OAQI;AACI,aAAA;AACA,eAAA;AACA,UAAA;AACA,eAAA;AACA,SAAA;;AAxBZ,MAWI,OAgBI,CAAA;AACI,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;;AAKR,KAAC,CAAA,UACG;AACI,gBAAA;AACA,oBAAA;AACA,WAAA;AACA,UAAA;AACA,kBAAA;AACA,mBAAA;;AAPR,KAAC,CAAA,UACG,OAQI;AACI,aAAA;AACA,eAAA;AACA,eAAA;AACA,cAAA;AACA,UAAA;AACA,gBAAA;AACA,oBAAA;AACA,SAAA;AACA,kBAAA;AACA,aAAW,OAAA;;AAnBvB,KAAC,CAAA,UACG,OAqBI,CApCJ;AAqCQ,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;;AA/BZ,KAAC,CAAA,UAmCG;AACI,WAAA;;AA7EZ,MAiFI;AACI,QAAA;AACA,cAAA;AACA,cAAA;;AAGA,MANJ,OAMK;AACG,WAAA;;AAxFZ,MAiFI,QAUI,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,WAAA;;AAlGZ,MAiFI,QAUI,CAAA,YASI,CAAA;AACI,WAAA;AACA,eAAA;AACA,mBAAA;;AAvGhB,MAiFI,QAUI,CAAA,YASI,CAAA,aAKI,CAAA;AACI,WAAA;AACA,eAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AA9GpB,MAiFI,QAUI,CAAA,YASI,CAAA,aAaI,CAAA;AACI,WAAA;AACA,eAAA;AACA,mBAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA;;AAvHpB,MAiFI,QAUI,CAAA,YAgCI,CAAA;AACI,cAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA,IAAA,MAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,YAAA;;AA/HhB,MAiFI,QAUI,CAAA,YAgCI,CAAA,eAMI,CAAA;AACI,WAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAnIpB,MAiFI,QAUI,CAAA,YAgCI,CAAA,eAMI,CAAA,aAII,CAAA;AACI,WAAA;AACA,eAAA;AACA,WAAA,IAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;;AAEA,MA7DpB,QAUI,CAAA,YAgCI,CAAA,eAMI,CAAA,aAII,CAAA,UASK,CAAA;AACG,QAAA;AACA,mBAAA;AACA,gBAAA;;AAGJ,MAnEpB,QAUI,CAAA,YAgCI,CAAA,eAMI,CAAA,aAII,CAAA,UAeK,CAAA;AACG,SAAA;AACA,mBAAA;AACA,iBAAA;;AAvJ5B,MAiFI,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA;AACI,WAAA;AACA,iBAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;ACpJhB,cAAA,iBAAA,MAAA;;ADuJgB,MAjFpB,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA,SAKK;AACG,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,MArFpB,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA,SASK,CAAA;AACG,cAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,MAzFpB,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA,SAaK;AACG,iBAAA;;AA3K5B,MAiFI,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA,UAiBI,CAAA;AACI,WAAA;AACA,eAAA;AACA,WAAA,IAAA;AACA,aAAA;AACA,SAAA;AACA,eAAA;AACA,cAAA;;AAEA,MAtGxB,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA,UAiBI,CAAA,OASK,CAzCJ;AA0CO,QAAA;AACA,gBAAA;;AAFJ,MAtGxB,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA,UAiBI,CAAA,OASK,CAzCJ,eA6CO,CAAA;AACI,WAAA;AACA,eAAA;AACA,OAAA;AACA,SAAA;;AARR,MAtGxB,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA,UAiBI,CAAA,OASK,CAzCJ,eA6CO,CAAA,YAMI,CAAA;AACI,SAAA;AACA,UAAA;;AAZZ,MAtGxB,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA,UAiBI,CAAA,OASK,CAzCJ,eA6CO,CAAA,YAWI,CAAA;AACI,SAAA;AACA,UAAA;AACA,YAAA;;AAlBZ,MAtGxB,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA,UAiBI,CAAA,OASK,CAzCJ,eA6CO,CAAA,YAWI,CAAA,UAKI,CAAA;AACI,SAAA;AACA,UAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,YAAA;;AAEA,MAjIxC,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA,UAiBI,CAAA,OASK,CAzCJ,eA6CO,CAAA,YAWI,CAAA,UAKI,CAAA,WAOK;AACG,WAAS;AACT,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA;AACA,iBAAA;;AAnCpB,MAtGxB,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA,UAiBI,CAAA,OASK,CAzCJ,eA6CO,CAAA,YAoCI,CAAA;AACI,SAAA;AACA,UAAA;AACA,SAAA;;AA3CZ,MAtGxB,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA,UAiBI,CAAA,OASK,CAzCJ,eA6CO,CAAA,YA0CI,CAAA;AACI,QAAA;AACA,cAAA;;AAKZ,MA3JxB,QAUI,CAAA,YAgCI,CAAA,eAiCI,CAAA,WACI,CAAA,UAiBI,CAAA,OA8DK,CAxFJ;AAyFO,SAAA;AACA,mBAAA;AACA,iBAAA;AACA,cAAA;;AAhPhC,MAiFI,QAUI,CAAA,YA4JI,CAAA;AACI,WAAA;AACA,mBAAA;;", "names": []}