<!-- Header Section -->
<header>
    <div class="title">
        <h1>Recovery</h1>
        <p>What, Where & When</p>
    </div>
    <div class="actions">
        <button class="process" [disabled]="!isButtonEnabled()">
            {{ isButtonEnabled() ? 'Start Recovery' : 'Choose options first' }}
        </button>
        <button class="reset" (click)="resetSelections()">
            <fa-icon icon="rotate-left"></fa-icon>
        </button>
    </div>
</header>

<!-- Main Content Container -->
<section>
    <!-- Recovery Type Selection Panel -->
    <app-recovery-type class="panel recovery-type-panel"
        [selectedRecoveryType]="selectedRecoveryType()"
        [expandedPanel]="expandedPanel()"
        [class.expanded]="expandedPanel() === 'type'"
        [class.collapsed]="expandedPanel() !== null && expandedPanel() !== 'type'"
        (recoveryTypeSelected)="onRecoveryTypeSelected($event)"
        (onPanelExpand)="onPanelExpand($event)"
    ></app-recovery-type>

    <!-- Recovery Source Panel -->
    <app-recovery-source class="panel recovery-source-panel"
        [selectedSource]="selectedSource()"
        [expandedPanel]="expandedPanel()"
        [class.expanded]="expandedPanel() === 'source'"
        [class.collapsed]="expandedPanel() !== null && expandedPanel() !== 'source'"
        (sourceSelected)="onSourceSelected($event)"
        (onPanelExpand)="onPanelExpand($event)"
    ></app-recovery-source>

    <!-- Recovery Destination Panel -->
    <app-recovery-destination class="panel recovery-destination-panel"
        [selectedDestination]="selectedDestination()"
        [expandedPanel]="expandedPanel()"
        [class.expanded]="expandedPanel() === 'destination'"
        [class.collapsed]="expandedPanel() !== null && expandedPanel() !== 'destination'"
        (destinationSelected)="onDestinationSelected($event)"
        (onPanelExpand)="onPanelExpand($event)"
    ></app-recovery-destination>
</section>
