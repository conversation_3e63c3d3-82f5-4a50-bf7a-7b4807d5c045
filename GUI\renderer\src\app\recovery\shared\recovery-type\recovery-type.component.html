<!-- Panel Header -->
<header (click)="expandPanel()">
    <h2>Recovery Type</h2>
    <fa-icon icon="history"></fa-icon>
</header>

<!-- Expanded Content -->
@if (isPanelExpanded()) {
    <section>
        @for (type of recoveryTypes; track type.id) {
            <div class="item" [class.selected]="selectedRecoveryType() === type.id"
                (click)="selectRecoveryType(type.id)">
                <div class="item-icon">
                    <fa-icon [icon]="type.icon"></fa-icon>
                </div>
                <div class="item-content">
                    <h3>{{ type.title }}</h3>
                    <p>{{ type.description }}</p>
                </div>
            </div>
        }
    </section>
}
