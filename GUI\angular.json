{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"renderer": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "less", "skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:interceptor": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:resolver": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}}, "root": "renderer", "sourceRoot": "renderer/src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": "dist", "index": "renderer/src/index.html", "browser": "renderer/src/main.ts", "polyfills": [], "tsConfig": "renderer/tsconfig.app.json", "inlineStyleLanguage": "less", "assets": [{"glob": "**/*", "input": "renderer/assets"}], "styles": ["renderer/src/styles.less"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "renderer:build:production"}, "development": {"buildTarget": "renderer:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}}}}, "schematics": {"@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}