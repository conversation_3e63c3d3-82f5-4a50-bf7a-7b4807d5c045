# Coding Standards and Guidelines

Our team follows strict coding standards to ensure consistency, maintainability, and quality across the codebase. Please ensure all generated code adheres to the following rules:

## Project Architecture
- We are building a desktop application with **Angular 20 (renderer)** and **Electron 36 (main process)**
- Follow **clean architecture** principles with clear separation of concerns
- Implement **dependency injection** patterns where appropriate

## Code Formatting and Style
- Use **4 spaces for indentation**, never tabs
- Maximum line length: **120 characters**
- Use **camelCase** for variables, functions, and methods
- Use **PascalCase** for classes, interfaces, and components
- Use **kebab-case** for file names and component selectors
- All **code comments must be in English**

## Angular Specific Guidelines
- **Do NOT use deprecated syntax** such as `*ngIf`, `*ngFor`, `*ngSwitch`
- **Use new control flow syntax**: `@if`, `@for`, `@switch`, `@defer` as per Angular 17+ guidelines
- **Use modern input/output syntax**: `input()` and `output()` functions instead of `@Input()` and `@Output()` decorators
- Implement **OnPush change detection** strategy where possible for better performance
- Use **standalone components** by default
- Follow **reactive programming patterns** with RxJS
- Implement proper **lifecycle hooks** and cleanup (OnDestroy)

## TypeScript Best Practices
- Use **strict TypeScript configuration**
- Prefer **readonly** properties where applicable
- Use **type-only imports** when importing types: `import type { ... }`
- Implement proper **error handling** with try-catch blocks
- Use **optional chaining** (`?.`) and **nullish coalescing** (`??`) operators
- Define **explicit return types** for functions and methods

## Code Quality Standards
- Avoid any **unnecessary or redundant syntax**
- Code must be **clean, idiomatic**, and follow modern best practices
- Follow **DRY (Don't Repeat Yourself)** principles
- Implement **proper error boundaries** and error handling
- Use **meaningful variable and function names**
- Follow principles of **modularity, readability**, and **maintainability**

## Performance Guidelines
- Implement **lazy loading** for feature modules
- Use **trackBy functions** in `@for` loops when dealing with dynamic lists
- Optimize **bundle size** by avoiding unnecessary imports
- Implement **proper memory management** and subscription cleanup

## Security Considerations
- **Sanitize all user inputs** to prevent XSS attacks
- Use **Content Security Policy (CSP)** headers
- Implement **proper data validation** on both client and server sides
- Never expose sensitive data in console logs

## File Organization
- Group related files in **feature modules**
- Use **shared modules** for common functionality
- Implement **barrel exports** (index.ts) for clean imports
- Follow **Angular style guide** folder structure

## Documentation
- Write **JSDoc comments** for all public methods and complex logic
- Document **API interfaces** and data models

## Electron Specific Guidelines
- Use **contextBridge** for secure IPC communication
- Implement **proper security practices** (disable node integration in renderer)
- Handle **window lifecycle** events appropriately
- Use **preload scripts** for renderer-main communication

Always write **production-ready**, **modern**, and **maintainable** code that follows these established patterns and conventions.
