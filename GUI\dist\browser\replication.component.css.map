{"version": 3, "sources": ["renderer/src/app/shared/wizard.less", "renderer/src/styles/mixins.less", "renderer/src/styles/variables.less", "renderer/src/app/replication/replication.component.less"], "sourcesContent": ["\n@import '../../styles/variables.less';\n@import '../../styles/mixins.less';\n\n:host {\n    display: flex;\n    flex-direction: column;\n    gap: 1rem;\n    height: 100%;\n    padding: 1rem;\n    overflow: hidden;\n\n    // Header Section\n    header {\n        display: flex;\n        justify-content: space-between;\n\n        .animation(fade-in-up);\n\n        .title {\n            h1 {\n                font-size: 1.5rem;\n                font-weight: 700;\n                margin: 0 0 .5rem 0;\n                color: @text-color;\n            }\n\n            p {\n                font-size: 1rem;\n                color: @text-secondary;\n                margin: 0;\n            }\n        }\n\n        .actions {\n            display: flex;\n            gap: 1rem;\n            align-items: center;\n\n            button {\n                .transition(color, background, box-shadow);\n\n                border: none;\n                padding: 1rem;\n                border-radius: 0.75rem;\n                font-size: 1rem;\n                color: white;\n                cursor: pointer;\n\n                &.process {\n                    min-width: 16.25rem;\n                    background: @primary-gradient;\n\n                    &:disabled {\n                        cursor: not-allowed;\n                        color: rgba(255, 255, 255, 0.5);\n                        background: rgba(255, 255, 255, 0.1);\n                    }\n\n                    &:hover:not(:disabled) {\n                        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\n                    }\n                }\n\n                &.reset {\n                    display: flex;\n                    justify-content: center;\n                    align-items: center;\n                    background: rgba(255, 255, 255, 0.1);\n\n                    &:hover {\n                        background: rgba(255, 255, 255, 0.2);\n                    }\n                }\n            }\n        }\n    }\n\n    section {\n        flex: 1;\n        display: flex;\n        gap: 1rem;\n        width: 100%;\n        min-width: 0; // Prevent grid from expanding beyond container\n        overflow: hidden; // Prevent overflow of grid items\n    }\n}\n", "/**\r\n * Mixin\r\n * <AUTHOR>\r\n */\r\n\r\n@import \"variables.less\";\r\n\r\n// Transition\r\n.transition(...) {\r\n    & when (length(@arguments) = 0) {\r\n        transition: none;\r\n    }\r\n\r\n    & when (length(@arguments) = 1) {\r\n        transition: @arguments 300ms ease;\r\n    }\r\n\r\n    & when (length(@arguments) > 1) {\r\n        @properties: replace(\"@{arguments}\", \" \", \", \", \"g\");\r\n        transition-property: ~\"@{properties}\";\r\n        transition-timing-function: ease;\r\n        transition-duration: 300ms;\r\n    }\r\n}\r\n\r\n// Animations\r\n.animation(@name, @delay: 0ms) {\r\n    animation-duration: 500ms;\r\n    animation-fill-mode: forwards;\r\n    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\r\n    animation-delay: @delay;\r\n\r\n    & when (@name = fade-in-up) {\r\n        animation-name: fadeInUp;\r\n    }\r\n\r\n    & when (@name = fade-in-left) {\r\n        animation-name: fadeInLeft;\r\n    }\r\n\r\n    & when (@name = fade-in) {\r\n        animation-name: fadeIn;\r\n    }\r\n}\r\n", "// variables\r\n@primary-color: #ef4e38;\r\n@primary-gradient: linear-gradient(90deg, #ef4e38 0%, #b61e0e 100%);\r\n@success-color: #34d399;\r\n@success-bg: rgba(16, 185, 129, 0.2);\r\n@dark-bg: linear-gradient(284deg, #152b34 0%, #254b5c 100%);\r\n@background-dark: #1a2a33;\r\n@bg-overlay: rgba(255, 255, 255, 0.1);\r\n@light-overlay: rgba(255, 255, 255, 0.05);\r\n@border-color: rgba(255, 255, 255, 0.1);\r\n@text-color: rgba(255, 255, 255, 1);\r\n@text-secondary: rgba(255, 255, 255, 0.5);\r\n@border-radius: .75rem;\r\n@border-radius-small: .5rem;\r\n@font-family: 'inter', 'noto sans', sans-serif;\r\n", "@import '../../styles/variables.less';\n@import '../../styles/mixins.less';\n@import '../shared/wizard.less';\n\n:host {\n    section {\n        .panel {\n            .animation(fade-in-up, 50ms);\n            .transition(flex, box-shadow);\n\n            &:nth-child(2) {\n                animation-delay: 100ms;\n            }\n\n            &:nth-child(3) {\n                animation-delay: 150ms;\n            }\n\n            &.expanded {\n                flex: 1;\n                cursor: default;\n            }\n\n            &.collapsed {\n                flex: 0 0 5rem;\n                width: 5rem;\n                align-items: center;\n                justify-content: flex-start;\n                position: relative;\n            }\n        }\n    }\n}\n"], "mappings": ";AAIA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;AACA,UAAA;AACA,WAAA;AACA,YAAA;;AANJ,MASI;AACI,WAAA;AACA,mBAAA;ACWG,sBAAA;AACH,uBAAA;AACG,6BACT,aAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA;AAAyC,mBAAA;AAGjC,kBAAA;;AD5BV,MASI,OAMI,CAAA,MACI;AACI,aAAA;AACA,eAAA;AACA,UAAA,EAAA,EAAA,OAAA;AACA,SAAA;;AApBhB,MASI,OAMI,CAAA,MAQI;AACI,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;;AA1BhB,MASI,OAqBI,CAAA;AACI,WAAA;AACA,OAAA;AACA,eAAA;;AAjCZ,MASI,OAqBI,CAAA,QAKI;ACrBwC;IAAA,KAAA;IAAA,UAAA;IAAA;AAChB,8BAAA;AACN,uBAAA;ADsBd,UAAA;AACA,WAAA;AACA,iBAAA;AACA,aAAA;AACA,SAAA;AACA,UAAA;;AAEA,MApCZ,OAqBI,CAAA,QAKI,MAUK,CAAA;AACG,aAAA;AACA;IEjDH;MAAA,KAAA;MAAA,QAAA,EAAA;MAAA,QAAA;;AFmDG,MAxChB,OAqBI,CAAA,QAKI,MAUK,CAAA,OAII;AACG,UAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,MA9ChB,OAqBI,CAAA,QAKI,MAUK,CAAA,OAUI,MAAM,KAAI;AACP,cAAA,EAAA,KAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAIR,MAnDZ,OAqBI,CAAA,QAKI,MAyBK,CAAA;AACG,WAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAEA,MAzDhB,OAqBI,CAAA,QAKI,MAyBK,CAAA,KAMI;AACG,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAnExB,MA0EI;AACI,QAAA;AACA,WAAA;AACA,OAAA;AACA,SAAA;AACA,aAAA;AACA,YAAA;;AGhFR,MACI,QACI,CAAA;AFoBG,sBAAA;AACH,uBAAA;AACG,6BACT,aAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA;AAAyC,mBAAA;AAGjC,kBAAA;AAd0C,uBAAA,IAAA,EAAA;AAChB,8BAAA;AACN,uBAAA;;AEVlB,MALR,QACI,CAAA,KAIK;AACG,mBAAA;;AAGJ,MATR,QACI,CAAA,KAQK;AACG,mBAAA;;AAGJ,MAbR,QACI,CAAA,KAYK,CAAA;AACG,QAAA;AACA,UAAA;;AAGJ,MAlBR,QACI,CAAA,KAiBK,CAAA;AACG,QAAA,EAAA,EAAA;AACA,SAAA;AACA,eAAA;AACA,mBAAA;AACA,YAAA;;", "names": []}