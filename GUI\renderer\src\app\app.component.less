@import '../styles/variables.less';
@import '../styles/mixins.less';

:host {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: @dark-bg;
    color: @text-color;
    font-family: @font-family;
    overflow: hidden;

    header {
        height: 39px;
        background-color: rgba(0, 0, 0, 0.15);
        -webkit-app-region: drag; // Enable window dragging
        -webkit-user-select: none; // Disable text selection
        user-select: none; // Disable text selection for other browsers

        // If you have any interactive elements inside header, apply this class to them
        .no-drag {
            -webkit-app-region: no-drag;
        }
    }

    main {
        display: flex;
        width: 100%;
        flex: 1;
        overflow: hidden;
        min-height: 0; // Ensure flex items can shrink properly

        aside {
            width: 16.5rem;
            background: transparent;
            border-right: .0625rem solid @border-color;
            display: flex;
            flex-direction: column;
            overflow-y: auto; // Add scrolling capability
            flex-shrink: 0; // Prevent sidebar from being compressed
            padding: 1rem 0;
            overflow: hidden;
            transition: width 0.3s ease;

            .logo {
                display: flex;
                align-items: center;
                padding: 1rem;

                .title {
                    font-family: 'Noto Sans', sans-serif;
                    font-weight: 300;
                    font-size: 1.625rem;
                    padding-bottom: 1rem;
                    line-height: 1;
                    color: @text-color;
                    white-space: nowrap;
                    transition: opacity 0.3s ease;
                    cursor: pointer;
                }
            }

            nav {
                display: flex;
                flex-direction: column;
                gap: .75rem;
                padding: 0 1rem;
                flex: 1;

                .section {
                    padding-bottom: .75rem;
                    border-bottom: 0.0625rem solid rgba(255, 255, 255, 0.2);
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                    opacity: 0;

                    .animation(fade-in-left);

                    &:nth-child(1) {
                        animation-delay: 0.05s;
                    }

                    &:nth-child(2) {
                        animation-delay: 0.1s;
                    }

                    &:nth-child(3) {
                        animation-delay: 0.15s;
                    }

                    &:last-child {
                        border-bottom: none;
                    }

                    .item {
                        display: flex;
                        align-items: center;
                        gap: .75rem;
                        padding: .5rem .75rem;
                        border-radius: @border-radius-small;
                        cursor: pointer;
                        position: relative;
                        overflow: hidden;
                        color: @text-color;
                        text-decoration: none;
                        outline: none;
                        transition: background-color 0.3s ease, color 0.3s ease;

                        &:hover {
                            background-color: rgba(255, 255, 255, 0.15);
                            text-decoration: none;
                            color: @text-color;
                        }

                        &.active {
                            background-color: rgba(255, 255, 255, 0.2);
                            color: @text-color;

                            &::before {
                                content: '';
                                position: absolute;
                                left: 0;
                                top: 0;
                                height: 100%;
                                width: 0.1875rem;
                                background: @primary-color;
                            }
                        }

                        span {
                            font-family: 'Noto Sans', sans-serif;
                            font-size: 1rem;
                            font-weight: 400;
                            white-space: nowrap;
                            transition: opacity 0.3s ease;
                        }
                    }
                }

                .info {
                    margin-top: auto;
                    background-color: @light-overlay;
                    padding: 1rem;
                    border-radius: @border-radius-small;

                    h4 {
                        font-size: 1.125rem;
                        font-weight: 400;
                        margin: 0 0 .375rem 0;
                    }

                    p {
                        font-size: 0.875rem;
                        color: @text-secondary;
                        line-height: 1.36;
                        margin: 0;
                    }

                    fa-icon {
                        display: none;
                    }
                }
            }

            &.collapsed {
                width: 4.75rem;

                .logo {
                    overflow: hidden;

                    .title {
                        opacity: 0;
                    }
                }

                nav {
                    .section {
                        overflow: hidden;

                        .item {
                            span {
                                opacity: 0;
                            }
                        }
                    }

                    .info {
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        fa-icon {
                            display: inline-block;
                        }
                    }
                }
            }
        }

        section {
            flex: 1;
            overflow-y: auto;
            min-height: 0; // Ensure proper scrolling
        }
    }

    footer {
        background-color: @light-overlay;
        width: 100%;
        height: 3.75rem;
        flex-shrink: 0; // Prevent footer from being compressed
        overflow: hidden;

        .container {
            padding: 1rem;
            border-top: .0625rem solid @border-color;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .left {
                display: flex;
                align-items: center;
                gap: 1rem;

                .logo {
                    width: 6rem;
                    height: 1.5625rem;
                    display: flex;
                    align-items: center;

                    img {
                        width: 100%;
                        height: auto;
                        object-fit: contain;
                    }
                }

                .copyright {
                    font-size: 0.875rem;
                    color: @text-secondary;
                }
            }

            .links {
                display: flex;
                gap: 1.5rem;

                a {
                    color: @text-secondary;
                    text-decoration: none;
                    font-size: 0.875rem;

                    &:hover {
                        color: @text-color;
                        text-decoration: underline;
                    }
                }
            }
        }
    }
}
