{"version": 3, "sources": ["renderer/src/app/recovery/shared/recovery-destination/shared/disk-mapping/disk-mapping.component.less", "renderer/src/styles/mixins.less"], "sourcesContent": ["@import '../../../../../../styles/variables.less';\n@import '../../../../../../styles/mixins.less';\n\n:host {\n    display: grid;\n    grid-template-columns: 1fr auto 1fr;\n    flex: 1;\n    overflow-y: auto;\n\n    // Hide scrollbar for Chromium/WebKit (Electron)\n    &::-webkit-scrollbar {\n        display: none;\n    }\n\n    .source-column,\n    .target-column {\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n\n        .column-header {\n            h4 {\n                font-size: 1rem;\n                font-weight: 500;\n                color: @text-color;\n                margin: 0;\n                text-align: center;\n            }\n        }\n    }\n\n    .source-column:not(.clone) {\n        position: relative;\n    }\n    .source-column.clone {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        z-index: 1;\n        opacity: 0;\n    }\n\n    .disk-item {\n        display: flex;\n        flex-direction: column;\n        gap: 0.5rem;\n\n        .disk-header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            gap: 1rem;\n\n            .disk-name {\n                font-size: 0.75rem;\n                font-weight: 400;\n                color: @text-color;\n                line-height: 1.36;\n            }\n\n            .disk-type {\n                font-size: 0.75rem;\n                font-weight: 400;\n                color: @text-color;\n                line-height: 1.36;\n                text-align: center;\n                flex: 1;\n            }\n\n            .disk-info {\n                font-size: 0.75rem;\n                font-weight: 400;\n                color: @text-color;\n                line-height: 1.36;\n                text-align: right;\n            }\n        }\n\n        .disk-body {\n            display: flex;\n            flex-direction: column;\n            gap: 0.5rem;\n            border: 1px solid rgba(255, 255, 255, 0.1);\n            border-radius: 0.5rem;\n            padding: 0.5rem;\n            background: rgba(255, 255, 255, 0.1);\n\n            .disk-icons {\n                display: flex;\n                align-items: center;\n                justify-content: space-between;\n\n                .disk-icon {\n                    width: 1.5rem;\n                    height: 1.5rem;\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    color: @text-color;\n\n                    fa-icon {\n                        font-size: 1.25rem;\n                    }\n                }\n            }\n\n            .disk-partitions {\n                display: flex;\n                flex-direction: column;\n                gap: 0.5rem;\n\n                .more-options-disk {\n                    position: absolute;\n                    right: 0.5rem;\n                    top: 1rem;\n                    width: 1.2rem;\n                    height: 0.3rem;\n                    background: none;\n                    border: none;\n                    color: @text-color;\n                    cursor: pointer;\n\n                    .transition(color);\n\n                    &:hover {\n                        color: @primary-color;\n                    }\n\n                    fa-icon {\n                        font-size: 0.8rem;\n                    }\n                }\n            }\n        }\n    }\n\n    .arrow-section {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: flex-start;\n        gap: 0;\n        padding-top: 4.375rem;\n        min-width: 16rem;\n\n        .mapping-arrows {\n            display: flex;\n            flex-direction: column;\n            gap: .5rem;\n            width: 100%;\n\n            .arrow-line {\n                display: flex;\n                align-items: center;\n                width: 100%;\n                height: 4.375rem;\n\n                &:first-child {\n                    height: 1.25rem;\n\n                    &::before {\n                        background: rgba(204, 204, 204, 0.5);\n                    }\n\n                    &::after {\n                        border-left-color: rgba(204, 204, 204, 0.5);\n                    }\n                }\n\n                &::before {\n                    content: '';\n                    width: 100%;\n                    height: .25rem;\n                    background: rgba(16, 185, 129, 0.5);\n                }\n\n                &::after {\n                    content: '';\n                    border-top: 0.5rem solid transparent;\n                    border-bottom: 0.5rem solid transparent;\n                    border-left: 0.75rem solid rgba(16, 185, 129, 0.5);\n                }\n            }\n        }\n    }\n}\n\n.partition-item {\n    display: flex;\n    flex-direction: column;\n    gap: 0.5rem;\n    background: rgba(16, 185, 129, 0.2);\n    border: 1px solid rgba(16, 185, 129, 0.5);\n    border-radius: 0.5rem;\n    padding: 0.625rem;\n\n    .partition-header {\n        display: flex;\n        align-items: center;\n        gap: 0.5rem;\n\n        .partition-icon {\n            color: @text-color;\n            font-size: 1rem;\n        }\n\n        .partition-name {\n            font-size: 0.75rem;\n            font-weight: 400;\n            color: @text-color;\n            line-height: 1.36;\n            flex: 1;\n        }\n\n        .more-options {\n            background: none;\n            border: none;\n            color: @text-color;\n            cursor: pointer;\n\n            .transition(color);\n\n            &:hover {\n                color: @primary-color;\n            }\n\n            fa-icon {\n                font-size: 0.8rem;\n            }\n        }\n    }\n\n    .partition-usage {\n        .usage-bar {\n            width: 100%;\n            height: 1.25rem;\n            background: linear-gradient(to bottom, #868686 0%, #ffffff 100%);\n            border-radius: 0.25rem;\n            overflow: hidden;\n            position: relative;\n\n            .usage-fill {\n                height: 100%;\n                background: linear-gradient(to right, #e73820 0%, #c32615 100%);\n                border-radius: 0.25rem;\n                transition: width 0.3s ease;\n            }\n        }\n    }\n}\n", "/**\n * Mixin\n * <AUTHOR>\n */\n\n@import \"variables.less\";\n\n// Transition\n.transition(...) {\n    & when (length(@arguments) = 0) {\n        transition: none;\n    }\n\n    & when (length(@arguments) = 1) {\n        transition: @arguments 300ms ease;\n    }\n\n    & when (length(@arguments) > 1) {\n        @properties: replace(\"@{arguments}\", \" \", \", \", \"g\");\n        transition-property: ~\"@{properties}\";\n        transition-timing-function: ease;\n        transition-duration: 300ms;\n    }\n}\n\n// Animations\n.animation(@name, @delay: 0ms) {\n    animation-duration: 500ms;\n    animation-fill-mode: forwards;\n    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n    animation-delay: @delay;\n\n    & when (@name = fade-in-up) {\n        animation-name: fadeInUp;\n    }\n\n    & when (@name = fade-in-left) {\n        animation-name: fadeInLeft;\n    }\n\n    & when (@name = fade-in) {\n        animation-name: fadeIn;\n    }\n}\n"], "mappings": ";AAGA;AACI,WAAA;AACA,yBAAA,IAAA,KAAA;AACA,QAAA;AACA,cAAA;;AAGA,KAAC;AACG,WAAA;;AARR,MAWI,CAAA;AAXJ,MAYI,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;;AAfR,MAWI,CAAA,cAMI,CAAA,cACI;AAlBZ,MAYI,CAAA,cAKI,CAAA,cACI;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA;;AAvBhB,MA4BI,CAjBA,aAiBc,KAAI,CAAA;AACd,YAAA;;AA7BR,MA+BI,CApBA,aAoBc,CAHI;AAId,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,WAAA;AACA,WAAA;;AAtCR,MAyCI,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;;AA5CR,MAyCI,CAAA,UAKI,CAAA;AACI,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;;AAlDZ,MAyCI,CAAA,UAKI,CAAA,YAMI,CAAA;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;;AAxDhB,MAyCI,CAAA,UAKI,CAAA,YAaI,CAAA;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;AACA,cAAA;AACA,QAAA;;AAjEhB,MAyCI,CAAA,UAKI,CAAA,YAsBI,CAAA;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;AACA,cAAA;;AAzEhB,MAyCI,CAAA,UAoCI,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;AACA,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,WAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AApFZ,MAyCI,CAAA,UAoCI,CAAA,UASI,CAAA;AACI,WAAA;AACA,eAAA;AACA,mBAAA;;AAzFhB,MAyCI,CAAA,UAoCI,CAAA,UASI,CAAA,WAKI,CAAA;AACI,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,SAAA;;AAjGpB,MAyCI,CAAA,UAoCI,CAAA,UASI,CAAA,WAKI,CAAA,UAQI;AACI,aAAA;;AApGxB,MAyCI,CAAA,UAoCI,CAAA,UA4BI,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;;AA5GhB,MAyCI,CAAA,UAoCI,CAAA,UA4BI,CAAA,gBAKI,CAAA;AACI,YAAA;AACA,SAAA;AACA,OAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA;AACA,UAAA;AACA,SAAA;AACA,UAAA;AC5GZ,cAAA,MAAA,MAAA;;ADgHY,MAlFhB,CAAA,UAoCI,CAAA,UA4BI,CAAA,gBAKI,CAAA,iBAaK;AACG,SAAA;;AA5HxB,MAyCI,CAAA,UAoCI,CAAA,UA4BI,CAAA,gBAKI,CAAA,kBAiBI;AACI,aAAA;;AAhIxB,MAuII,CAAA;AACI,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA;AACA,eAAA;AACA,aAAA;;AA9IR,MAuII,CAAA,cASI,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;AACA,SAAA;;AApJZ,MAuII,CAAA,cASI,CAAA,eAMI,CAAA;AACI,WAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA;;AAEA,MArBZ,CAAA,cASI,CAAA,eAMI,CAAA,UAMK;AACG,UAAA;;AAEA,MAxBhB,CAAA,cASI,CAAA,eAMI,CAAA,UAMK,YAGI;AACG,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,MA5BhB,CAAA,cASI,CAAA,eAMI,CAAA,UAMK,YAOI;AACG,qBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAIR,MAjCZ,CAAA,cASI,CAAA,eAMI,CAAA,UAkBK;AACG,WAAS;AACT,SAAA;AACA,UAAA;AACA,cAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,MAxCZ,CAAA,cASI,CAAA,eAMI,CAAA,UAyBK;AACG,WAAS;AACT,cAAA,OAAA,MAAA;AACA,iBAAA,OAAA,MAAA;AACA,eAAA,QAAA,MAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAOpB,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;AACA,cAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA,IAAA,MAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,WAAA;;AAPJ,CAAA,eASI,CAAA;AACI,WAAA;AACA,eAAA;AACA,OAAA;;AAZR,CAAA,eASI,CAAA,iBAKI,CAAA;AACI,SAAA;AACA,aAAA;;AAhBZ,CAAA,eASI,CAAA,iBAUI,CAAA;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;AACA,QAAA;;AAxBZ,CAAA,eASI,CAAA,iBAkBI,CAAA;AACI,cAAA;AACA,UAAA;AACA,SAAA;AACA,UAAA;AC9MJ,cAAA,MAAA,MAAA;;ADkNI,CAnCZ,eASI,CAAA,iBAkBI,CAAA,YAQK;AACG,SAAA;;AApChB,CAAA,eASI,CAAA,iBAkBI,CAAA,aAYI;AACI,aAAA;;AAxChB,CAAA,eA6CI,CAAA,gBACI,CAAA;AACI,SAAA;AACA,UAAA;AACA;IAAY;MAAA,GAAA,MAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACZ,iBAAA;AACA,YAAA;AACA,YAAA;;AApDZ,CAAA,eA6CI,CAAA,gBACI,CAAA,UAQI,CAAA;AACI,UAAA;AACA;IAAY;MAAA,GAAA,KAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACZ,iBAAA;AACA,cAAA,MAAA,KAAA;;", "names": []}