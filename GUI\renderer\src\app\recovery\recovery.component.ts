import { Component, signal } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { RecoveryTypeComponent } from './shared/recovery-type/recovery-type.component';
import { RecoverySourceComponent } from './shared/recovery-source/recovery-source.component';
import { RecoveryDestinationComponent } from './shared/recovery-destination/recovery-destination.component';

@Component({
    selector: 'app-recovery',
    imports: [
        FontAwesomeModule,
        RecoveryTypeComponent,
        RecoverySourceComponent,
        RecoveryDestinationComponent
    ],
    templateUrl: './recovery.component.html',
    styleUrls: ['./recovery.component.less']
})
export class RecoveryComponent {
    // State signals
    selectedRecoveryType = signal<string | null>(null);
    selectedSource = signal<string | null>(null);
    selectedDestination = signal<string | null>(null);
    expandedPanel = signal<'type' | 'source' | 'destination' | null>('destination');

    // Button state
    isButtonEnabled(): boolean {
        return this.selectedRecoveryType() !== null &&
               this.selectedSource() !== null &&
               this.selectedDestination() !== null;
    }

    // Event handlers
    onRecoveryTypeSelected(typeId: string): void {
        this.selectedRecoveryType.set(typeId);
        this.expandedPanel.set('source');
    }

    onSourceSelected(sourceId: string): void {
        this.selectedSource.set(sourceId);
        this.expandedPanel.set('destination');
    }

    onDestinationSelected(destinationId: string): void {
        this.selectedDestination.set(destinationId);
        // Keep destination panel expanded to show detail view
        // this.expandedPanel.set(null);
    }

    onPanelExpand(panel: 'type' | 'source' | 'destination'): void {
        this.expandedPanel.set(panel);
    }

    resetSelections(): void {
        this.selectedRecoveryType.set(null);
        this.selectedSource.set(null);
        this.selectedDestination.set(null);
        this.expandedPanel.set('destination'); // For testing, start with destination expanded
    }
}
