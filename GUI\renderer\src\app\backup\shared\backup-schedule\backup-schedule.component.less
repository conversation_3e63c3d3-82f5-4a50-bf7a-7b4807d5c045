@import '../../../../styles/variables.less';

:host {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 1rem;
    height: 100%;
    width: 5rem;
    border-radius: .75rem;
    opacity: 0;
    overflow: hidden; // Prevent content overflow
    background: rgba(255, 255, 255, 0.1);

    // Panel Header
    header {
        flex: 0 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        padding-bottom: 1rem;

        h2 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
            line-height: 1.125rem;
            color: @text-color;
        }
    }

    // Panel Content
    section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 1rem;
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        padding-top: 4px;
        grid-auto-rows: min-content;
        min-height: 0; // Allow component to shrink

        .item {
            background: #ffffff0d;
            border-radius: .5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: .75rem;
            gap: 0.75em;
            justify-content: center;
            cursor: pointer;
            transition: background 0.25s ease, border-color 0.25s ease;

            &:hover {
                background: rgba(255, 255, 255, 0.08);
                border-color: rgba(255, 255, 255, 0.2);
            }

            &.selected {
                border-color: rgba(16, 185, 129, 0.6);
                background: rgba(16, 185, 129, 0.08);
                box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);
            }

            .icon {
                font-size: 3rem;
            }

            .name {
                font-size: 1rem;
                font-weight: 500;
                color: @text-color;
                text-align: center;
                line-height: 1.25;
                margin: 0;
                white-space: nowrap;
            }
        }
    }

    // Collapsed vertical panel styling
    &.collapsed {
        header {
            writing-mode: vertical-lr;
            text-orientation: mixed;
            padding: 0;
            height: 100%;
            flex-direction: row-reverse;
            justify-content: flex-end;

            h2 {
                font-size: 1rem;
                font-weight: 600;
                line-height: 1.2;
                text-align: center;
                margin: 0;
                writing-mode: vertical-lr;
                text-orientation: mixed;
                color: @text-color;
                letter-spacing: -0.02em;
                transform: rotate(180deg);
            }

            .icon-container {
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                border-radius: 6px;
                background: rgba(255, 255, 255, 0);
                padding: 0;
            }
        }

        section {
            display: none;
        }
    }
}
