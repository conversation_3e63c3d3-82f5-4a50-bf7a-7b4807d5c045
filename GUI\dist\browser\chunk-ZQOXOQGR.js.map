{"version": 3, "sources": ["renderer/src/app/preferences/hyperback/hyperback.component.ts", "renderer/src/app/preferences/hyperback/hyperback.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n@Component({\r\n    selector: 'app-hyperback',\r\n    imports: [FontAwesomeModule, FormsModule],\r\n    templateUrl: './hyperback.component.html',\r\n    styleUrl: './hyperback.component.less'\r\n})\r\nexport class HyperbackComponent {\r\n    // Operation Mode\r\n    operationMode = 'parallel'; // 'parallel' or 'sequential'\r\n    concurrentTasks = 5;\r\n\r\n    // Tracking Method\r\n    useResilientChangeTracking = false;\r\n\r\n    // Backup Image File Optimization\r\n    createOptimizedBackupImage = false;\r\n\r\n    // Post-Backup (placeholder for future implementation)\r\n    postBackupOption = 'option1';\r\n\r\n    // Deduplication (placeholder for future implementation)\r\n    deduplicationOption = 'option1';\r\n\r\n    onApply() {\r\n        // Handle apply logic\r\n        console.log('Hyperback settings applied');\r\n    }\r\n\r\n    onCancel() {\r\n        // Handle cancel logic\r\n        console.log('Hyperback settings cancelled');\r\n    }\r\n}\r\n", "<header>\r\n    <div class=\"icon\">\r\n        <fa-icon icon=\"rocket\"></fa-icon>\r\n    </div>\r\n    <h2>Hyperback</h2>\r\n</header>\r\n\r\n<section>\r\n    <div class=\"section\">\r\n        <h3>Options</h3>\r\n\r\n        <div class=\"subsection\">\r\n            <h4>Operation Mode</h4>\r\n\r\n            <div class=\"form-group\">\r\n                <label class=\"form-label\">\r\n                    <input type=\"radio\" class=\"form-radio\" name=\"operationMode\" value=\"parallel\" [(ngModel)]=\"operationMode\">\r\n                    <span class=\"form-radio-label\">Parallel Backup (backup VMs in parallel)</span>\r\n                </label>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n                <label class=\"form-label\">\r\n                    <input type=\"radio\" class=\"form-radio\" name=\"operationMode\" value=\"sequential\" [(ngModel)]=\"operationMode\">\r\n                    <span class=\"form-radio-label\">Sequential Backup (backup VMs sequentially)</span>\r\n                </label>\r\n            </div>\r\n\r\n            <div class=\"form-group inline\">\r\n                <label class=\"form-label\">Run</label>\r\n                <div class=\"form-select-wrapper\">\r\n                    <select class=\"form-select\" [(ngModel)]=\"concurrentTasks\">\r\n                        @for (num of [1,2,3,4,5,6,7,8,9,10]; track num) {\r\n                            <option [value]=\"num\">{{ num }}</option>\r\n                        }\r\n                    </select>\r\n                </div>\r\n                <label class=\"form-label\">HyperBack Task(s) simultaneously</label>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"subsection\">\r\n            <h4>Tracking Method</h4>\r\n\r\n            <div class=\"form-group\">\r\n                <label class=\"form-label\">\r\n                    <input type=\"checkbox\" class=\"form-checkbox\" [(ngModel)]=\"useResilientChangeTracking\">\r\n                    <span class=\"form-checkbox-label\">Use Microsoft Resilient Change Tracking (RCT)</span>\r\n                </label>\r\n                <div class=\"form-description\">\r\n                    * * This tracking method only applies to backing up of Generation 2 VMs version 8.0 or later (on Server 2016 or later).\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"subsection\">\r\n            <h4>Backup Image File Optimization</h4>\r\n\r\n            <div class=\"form-group\">\r\n                <label class=\"form-label\">\r\n                    <input type=\"checkbox\" class=\"form-checkbox\" [(ngModel)]=\"createOptimizedBackupImage\">\r\n                    <span class=\"form-checkbox-label\">Create optimized backup image file</span>\r\n                </label>\r\n                <div class=\"form-description\">\r\n                    * * This option can exclude pagefile.sys and hiberfil.sys, etc. to make the backup image smaller.\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n\r\n<footer>\r\n    <span class=\"notice\">0 Configuration changes waiting to be applied</span>\r\n    <div class=\"buttons\">\r\n        <button type=\"button\" class=\"button secondary\" (click)=\"onCancel()\">Cancel</button>\r\n        <button type=\"button\" class=\"button primary\" (click)=\"onApply()\">Apply</button>\r\n    </div>\r\n</footer>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iBCgCyB,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAE;;;AACjB,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAsB,IAAA,iBAAA,CAAA;AAAS,IAAA,uBAAA;;;;AAAvB,IAAA,qBAAA,SAAA,MAAA;AAAc,IAAA,oBAAA;AAAA,IAAA,4BAAA,MAAA;;;ADvB5C,IAAO,qBAAP,MAAO,oBAAkB;;EAE3B,gBAAgB;;EAChB,kBAAkB;;EAGlB,6BAA6B;;EAG7B,6BAA6B;;EAG7B,mBAAmB;;EAGnB,sBAAsB;EAEtB,UAAO;AAEH,YAAQ,IAAI,4BAA4B;EAC5C;EAEA,WAAQ;AAEJ,YAAQ,IAAI,8BAA8B;EAC9C;;qCAzBS,qBAAkB;EAAA;yEAAlB,qBAAkB,WAAA,CAAA,CAAA,eAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,QAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,QAAA,SAAA,QAAA,iBAAA,SAAA,YAAA,GAAA,cAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,QAAA,SAAA,QAAA,iBAAA,SAAA,cAAA,GAAA,cAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,cAAA,QAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,YAAA,GAAA,iBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,aAAA,GAAA,OAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,WAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,4BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACV/B,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,OAAA,CAAA;AAEA,MAAA,oBAAA,GAAA,WAAA,CAAA;AACJ,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,WAAA;AAAS,MAAA,uBAAA,EAAK;AAGtB,MAAA,yBAAA,GAAA,SAAA,EAAS,GAAA,OAAA,CAAA,EACgB,GAAA,IAAA;AACb,MAAA,iBAAA,GAAA,SAAA;AAAO,MAAA,uBAAA;AAEX,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,IAAA,IAAA;AAChB,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AAElB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA,EACM,IAAA,SAAA,CAAA;AACuD,MAAA,2BAAA,iBAAA,SAAA,4DAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,eAAA,MAAA,MAAA,IAAA,gBAAA;AAAA,eAAA;MAAA,CAAA;AAA7E,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,QAAA,CAAA;AAA+B,MAAA,iBAAA,IAAA,0CAAA;AAAwC,MAAA,uBAAA,EAAO,EAC1E;AAGZ,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA,EACM,IAAA,SAAA,CAAA;AACyD,MAAA,2BAAA,iBAAA,SAAA,4DAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,eAAA,MAAA,MAAA,IAAA,gBAAA;AAAA,eAAA;MAAA,CAAA;AAA/E,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,QAAA,CAAA;AAA+B,MAAA,iBAAA,IAAA,6CAAA;AAA2C,MAAA,uBAAA,EAAO,EAC7E;AAGZ,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA+B,IAAA,SAAA,CAAA;AACD,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAiC,IAAA,UAAA,EAAA;AACD,MAAA,2BAAA,iBAAA,SAAA,6DAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,iBAAA,MAAA,MAAA,IAAA,kBAAA;AAAA,eAAA;MAAA,CAAA;AACxB,MAAA,2BAAA,IAAA,oCAAA,GAAA,GAAA,UAAA,IAAA,mCAAA;AAGJ,MAAA,uBAAA,EAAS;AAEb,MAAA,yBAAA,IAAA,SAAA,CAAA;AAA0B,MAAA,iBAAA,IAAA,kCAAA;AAAgC,MAAA,uBAAA,EAAQ,EAChE;AAGV,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,IAAA;AAChB,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AAEnB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA,EACM,IAAA,SAAA,EAAA;AACuB,MAAA,2BAAA,iBAAA,SAAA,4DAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,4BAAA,MAAA,MAAA,IAAA,6BAAA;AAAA,eAAA;MAAA,CAAA;AAA7C,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAkC,MAAA,iBAAA,IAAA,+CAAA;AAA6C,MAAA,uBAAA,EAAO;AAE1F,MAAA,yBAAA,IAAA,OAAA,EAAA;AACI,MAAA,iBAAA,IAAA,2HAAA;AACJ,MAAA,uBAAA,EAAM,EACJ;AAGV,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,IAAA;AAChB,MAAA,iBAAA,IAAA,gCAAA;AAA8B,MAAA,uBAAA;AAElC,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA,EACM,IAAA,SAAA,EAAA;AACuB,MAAA,2BAAA,iBAAA,SAAA,4DAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,4BAAA,MAAA,MAAA,IAAA,6BAAA;AAAA,eAAA;MAAA,CAAA;AAA7C,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAkC,MAAA,iBAAA,IAAA,oCAAA;AAAkC,MAAA,uBAAA,EAAO;AAE/E,MAAA,yBAAA,IAAA,OAAA,EAAA;AACI,MAAA,iBAAA,IAAA,qGAAA;AACJ,MAAA,uBAAA,EAAM,EACJ,EACJ,EACJ;AAGV,MAAA,yBAAA,IAAA,QAAA,EAAQ,IAAA,QAAA,EAAA;AACiB,MAAA,iBAAA,IAAA,+CAAA;AAA6C,MAAA,uBAAA;AAClE,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAqB,IAAA,UAAA,EAAA;AAC8B,MAAA,qBAAA,SAAA,SAAA,uDAAA;AAAA,eAAS,IAAA,SAAA;MAAU,CAAA;AAAE,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC1E,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA6C,MAAA,qBAAA,SAAA,SAAA,uDAAA;AAAA,eAAS,IAAA,QAAA;MAAS,CAAA;AAAE,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAS,EAC7E;;;AA5DuF,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,aAAA;AAOE,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,aAAA;AAQnD,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,eAAA;AACxB,MAAA,oBAAA;AAAA,MAAA,qBAAA,0BAAA,GAAA,GAAA,CAAA;AAcyC,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,0BAAA;AAcA,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,0BAAA;;oBDtDnD,mBAAiB,iBAAE,aAAW,gBAAA,8BAAA,sBAAA,8BAAA,4BAAA,2BAAA,iBAAA,OAAA,GAAA,QAAA,CAAA,ksFAAA,EAAA,CAAA;;;sEAI/B,oBAAkB,CAAA;UAN9B;uBACa,iBAAe,SAChB,CAAC,mBAAmB,WAAW,GAAC,UAAA,qxGAAA,QAAA,CAAA,igEAAA,EAAA,CAAA;;;;6EAIhC,oBAAkB,EAAA,WAAA,sBAAA,UAAA,iEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}