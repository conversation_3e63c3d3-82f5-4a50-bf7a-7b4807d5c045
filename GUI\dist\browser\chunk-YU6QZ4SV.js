import {
  FaIconComponent,
  FontAwesomeModule
} from "./chunk-N34W2DJ7.js";
import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-TBQAZTY7.js";

// renderer/src/app/preferences/online-portal/online-portal.component.ts
var OnlinePortalComponent = class _OnlinePortalComponent {
  static \u0275fac = function OnlinePortalComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _OnlinePortalComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _OnlinePortalComponent, selectors: [["app-online-portal"]], decls: 16, vars: 0, consts: [[1, "icon"], ["icon", "globe"], [1, "notice"], [1, "buttons"], ["type", "button", 1, "button", "secondary"], ["type", "button", 1, "button", "primary"]], template: function OnlinePortalComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "header")(1, "div", 0);
      \u0275\u0275element(2, "fa-icon", 1);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "h2");
      \u0275\u0275text(4, "Online Portal");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(5, "section")(6, "p");
      \u0275\u0275text(7, "Online portal settings will be implemented here.");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "footer")(9, "span", 2);
      \u0275\u0275text(10, "0 Configuration changes waiting to be applied");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "div", 3)(12, "button", 4);
      \u0275\u0275text(13, "Cancel");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(14, "button", 5);
      \u0275\u0275text(15, "Apply");
      \u0275\u0275elementEnd()()();
    }
  }, dependencies: [FontAwesomeModule, FaIconComponent], styles: ['\n\n[_nghost-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: 1rem;\n  overflow: hidden;\n  padding: 16px;\n  border-radius: 0.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  background: rgba(255, 255, 255, 0.1);\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 0ms;\n  animation-name: fadeInUp;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  border-radius: 0.5rem;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\n  font-size: 1rem;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  margin: 0;\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 500;\n  font-size: 1rem;\n  line-height: 1.25;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  flex: 1;\n  font-size: 0.875rem;\n}\n[_nghost-%COMP%]   footer[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  flex: 0 0 auto;\n  gap: 1rem;\n}\n[_nghost-%COMP%]   footer[_ngcontent-%COMP%]   .notice[_ngcontent-%COMP%] {\n  flex: 1;\n  font-size: 0.875rem;\n  line-height: 1.36;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   footer[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .button[_ngcontent-%COMP%] {\n  min-width: 12rem;\n}\n/*# sourceMappingURL=online-portal.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnlinePortalComponent, [{
    type: Component,
    args: [{ selector: "app-online-portal", imports: [FontAwesomeModule], template: '<header>\r\n    <div class="icon">\r\n        <fa-icon icon="globe"></fa-icon>\r\n    </div>\r\n    <h2>Online Portal</h2>\r\n</header>\r\n\r\n<section>\r\n    <p>Online portal settings will be implemented here.</p>\r\n</section>\r\n\r\n<footer>\r\n    <span class="notice">0 Configuration changes waiting to be applied</span>\r\n    <div class="buttons">\r\n        <button type="button" class="button secondary">Cancel</button>\r\n        <button type="button" class="button primary">Apply</button>\r\n    </div>\r\n</footer>\r\n', styles: ['/* renderer/src/app/preferences/online-portal/online-portal.component.less */\n:host {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: 1rem;\n  overflow: hidden;\n  padding: 16px;\n  border-radius: 0.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  background: rgba(255, 255, 255, 0.1);\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 0ms;\n  animation-name: fadeInUp;\n}\n:host header {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  border-radius: 0.5rem;\n}\n:host header .icon {\n  font-size: 1rem;\n}\n:host header h2 {\n  margin: 0;\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 500;\n  font-size: 1rem;\n  line-height: 1.25;\n  color: #ffffff;\n}\n:host section {\n  flex: 1;\n  font-size: 0.875rem;\n}\n:host footer {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  flex: 0 0 auto;\n  gap: 1rem;\n}\n:host footer .notice {\n  flex: 1;\n  font-size: 0.875rem;\n  line-height: 1.36;\n  color: #ffffff;\n}\n:host footer .buttons .button {\n  min-width: 12rem;\n}\n/*# sourceMappingURL=online-portal.component.css.map */\n'] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(OnlinePortalComponent, { className: "OnlinePortalComponent", filePath: "renderer/src/app/preferences/online-portal/online-portal.component.ts", lineNumber: 10 });
})();
export {
  OnlinePortalComponent
};
//# sourceMappingURL=chunk-YU6QZ4SV.js.map
