@import "variables.less";
@import "mixins.less";

.buttons {
    display: flex;
    gap: 1rem;
}

.button {
    padding: .5rem .75rem;
    border-radius: @border-radius-small;
    font-family: @font-family;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1.36;
    border: 1px solid transparent;
    cursor: pointer;
    height: 2.125rem;
    display: flex;
    align-items: center;
    justify-content: center;

    .transition(background, color, border-color, box-shadow);

    &.secondary {
        background: rgba(255, 255, 255, 0.1);
        color: @text-color;
        border-color: transparent;

        &:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.2);
        }
    }

    &.primary {
        background: @primary-gradient;
        color: @text-color;
        border-color: transparent;

        &:hover:not(:disabled) {
            opacity: 0.9;
            box-shadow: 0 4px 12px rgba(239, 78, 56, 0.3);
        }
    }

    &.disabled,
    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
}
