# The application id.
# Used as CFBundleIdentifier for MacOS
# and as Application User Model ID for Windows (NSIS target only, Squirrel.Windows not supported).
# It is strongly recommended that an explicit ID is set.
appId: com.actiphy.backup-and-standby

# As name, but allows you to specify a product name for your executable which contains spaces
# and other special characters not allowed in the name property.
productName: Actiphy Backup and Standby

# The human-readable copyright line for the app.
copyright: Copyright (C) Actiphy, Inc. All rights reserved.

directories:
    # The application directory (containing the application package.json),
    # defaults to app, www or working directory.
    app: dist

    # The output directory. File macros are supported.
    output: release

    # The path to build resources.
    # Please note — build resources is not packed into the app.
    # If you need to use some files, e.g. as tray icon,
    # please include required files explicitly: "files": ["**/*", "build/icon.*"]
    buildResources: assets

# Options related to how build Windows targets.
win:
    # The path to application icon.
    icon: assets/icon.ico

    # The security level at which the application requests to be executed.
    # Cannot be specified per target, allowed only in the win.
    requestedExecutionLevel: highestAvailable

    # The executable name. Defaults to productName.
    executableName: ActiphyBackup.exe

# The electron locales to keep.
# By default, all Electron locales used as-is.
electronLanguages:
    - en-US
