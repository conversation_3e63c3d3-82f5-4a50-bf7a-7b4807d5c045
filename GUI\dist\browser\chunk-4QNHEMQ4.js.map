{"version": 3, "sources": ["renderer/src/app/tools/tools.component.ts", "renderer/src/app/tools/tools.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-tools',\r\n  imports: [],\r\n  templateUrl: './tools.component.html',\r\n  styleUrl: './tools.component.less'\r\n})\r\nexport class ToolsComponent {\r\n\r\n}\r\n", "<p>tools works!</p>\r\n"], "mappings": ";;;;;;;;;;;AAQM,IAAO,iBAAP,MAAO,gBAAc;;qCAAd,iBAAc;EAAA;yEAAd,iBAAc,WAAA,CAAA,CAAA,WAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,wBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACR3B,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,cAAA;AAAY,MAAA,uBAAA;;;;;sEDQF,gBAAc,CAAA;UAN1B;uBACW,aAAW,SACZ,CAAA,GAAE,UAAA,0BAAA,CAAA;;;;6EAIA,gBAAc,EAAA,WAAA,kBAAA,UAAA,6CAAA,YAAA,EAAA,CAAA;AAAA,GAAA;", "names": []}