{"version": 3, "sources": ["renderer/src/app/preferences/preferences.component.ts", "renderer/src/app/preferences/preferences.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router, NavigationEnd } from '@angular/router';\r\nimport { RouterModule } from '@angular/router';\r\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\nimport { filter } from 'rxjs/operators';\r\n\r\n@Component({\r\n    selector: 'app-preferences',\r\n    imports: [RouterModule, FontAwesomeModule],\r\n    templateUrl: './preferences.component.html',\r\n    styleUrl: './preferences.component.less'\r\n})\r\nexport class PreferencesComponent implements OnInit {\r\n    activeRoute = 'general';\r\n\r\n    constructor(private router: Router) {}\r\n\r\n    ngOnInit(): void {\r\n        // Listen to route changes to update active route\r\n        this.router.events\r\n            .pipe(filter(event => event instanceof NavigationEnd))\r\n            .subscribe((event: NavigationEnd) => {\r\n                const urlSegments = event.url.split('/');\r\n                const lastSegment = urlSegments[urlSegments.length - 1];\r\n                this.activeRoute = lastSegment || 'general';\r\n            });\r\n\r\n        // Set initial active route\r\n        const currentUrl = this.router.url;\r\n        const urlSegments = currentUrl.split('/');\r\n        const lastSegment = urlSegments[urlSegments.length - 1];\r\n        this.activeRoute = lastSegment || 'general';\r\n    }\r\n}\r\n", "<!-- Header Section -->\r\n<header>\r\n    <div class=\"title\">\r\n        <h1>Preferences</h1>\r\n        <p>Configure Application Preferences, predefined Cloud & Hypervisors, Storage Destinations and Network connected Client Agents here</p>\r\n    </div>\r\n</header>\r\n\r\n<!-- Main Content Container -->\r\n<section>\r\n    <!-- Left Navigation Panel -->\r\n    <nav>\r\n        <a class=\"item\" [style.--nav-index]=\"0\"\r\n            [routerLink]=\"['/preferences', 'general']\"\r\n            [class.active]=\"activeRoute === 'general'\"\r\n            routerLinkActive=\"active\">\r\n            <fa-icon icon=\"sliders\"></fa-icon>\r\n            <span>General Settings</span>\r\n        </a>\r\n\r\n        <a class=\"item\" [style.--nav-index]=\"1\"\r\n            [routerLink]=\"['/preferences', 'proxy']\"\r\n            [class.active]=\"activeRoute === 'proxy'\"\r\n            routerLinkActive=\"active\">\r\n            <fa-icon icon=\"globe\"></fa-icon>\r\n            <span>Proxy</span>\r\n        </a>\r\n\r\n        <a class=\"item\" [style.--nav-index]=\"2\"\r\n            [routerLink]=\"['/preferences', 'notifications']\"\r\n            [class.active]=\"activeRoute === 'notifications'\"\r\n            routerLinkActive=\"active\">\r\n            <fa-icon icon=\"bell\"></fa-icon>\r\n            <span>Notifications</span>\r\n        </a>\r\n\r\n        <a class=\"item\" [style.--nav-index]=\"3\"\r\n            [routerLink]=\"['/preferences', 'backup']\"\r\n            [class.active]=\"activeRoute === 'backup'\"\r\n            routerLinkActive=\"active\">\r\n            <fa-icon icon=\"server\"></fa-icon>\r\n            <span>Backup</span>\r\n        </a>\r\n\r\n        <a class=\"item\" [style.--nav-index]=\"4\"\r\n            [routerLink]=\"['/preferences', 'alert']\"\r\n            [class.active]=\"activeRoute === 'alert'\"\r\n            routerLinkActive=\"active\">\r\n            <fa-icon icon=\"exclamation-triangle\"></fa-icon>\r\n            <span>Alert</span>\r\n        </a>\r\n\r\n        <a class=\"item\" [style.--nav-index]=\"5\"\r\n            [routerLink]=\"['/preferences', 'console']\"\r\n            [class.active]=\"activeRoute === 'console'\"\r\n            routerLinkActive=\"active\">\r\n            <fa-icon icon=\"square-terminal\"></fa-icon>\r\n            <span>Console</span>\r\n        </a>\r\n\r\n        <a class=\"item\" [style.--nav-index]=\"6\"\r\n            [routerLink]=\"['/preferences', 'hyperback']\"\r\n            [class.active]=\"activeRoute === 'hyperback'\"\r\n            routerLinkActive=\"active\">\r\n            <fa-icon icon=\"rocket\"></fa-icon>\r\n            <span>Hyperback</span>\r\n        </a>\r\n\r\n        <a class=\"item\" [style.--nav-index]=\"7\"\r\n            [routerLink]=\"['/preferences', 'online-portal']\"\r\n            [class.active]=\"activeRoute === 'online-portal'\"\r\n            routerLinkActive=\"active\">\r\n            <fa-icon icon=\"external-link-alt\"></fa-icon>\r\n            <span>Online Portal</span>\r\n        </a>\r\n\r\n        <a class=\"item\" [style.--nav-index]=\"8\"\r\n            [routerLink]=\"['/preferences', 'quick-recovery']\"\r\n            [class.active]=\"activeRoute === 'quick-recovery'\"\r\n            routerLinkActive=\"active\">\r\n            <fa-icon icon=\"undo-alt\"></fa-icon>\r\n            <span>Quick Recovery</span>\r\n        </a>\r\n\r\n        <a class=\"item\" [style.--nav-index]=\"9\"\r\n            [routerLink]=\"['/preferences', 'rescue-boot']\"\r\n            [class.active]=\"activeRoute === 'rescue-boot'\"\r\n            routerLinkActive=\"active\">\r\n            <fa-icon icon=\"boot\"></fa-icon>\r\n            <span>Rescue Boot</span>\r\n        </a>\r\n    </nav>\r\n\r\n    <!-- Right Content Panel -->\r\n    <div class=\"main\">\r\n        <router-outlet></router-outlet>\r\n    </div>\r\n</section>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYM,IAAO,uBAAP,MAAO,sBAAoB;EAGT;EAFpB,cAAc;EAEd,YAAoB,QAAc;AAAd,SAAA,SAAA;EAAiB;EAErC,WAAQ;AAEJ,SAAK,OAAO,OACP,KAAK,OAAO,WAAS,iBAAiB,aAAa,CAAC,EACpD,UAAU,CAAC,UAAwB;AAChC,YAAMA,eAAc,MAAM,IAAI,MAAM,GAAG;AACvC,YAAMC,eAAcD,aAAYA,aAAY,SAAS,CAAC;AACtD,WAAK,cAAcC,gBAAe;IACtC,CAAC;AAGL,UAAM,aAAa,KAAK,OAAO;AAC/B,UAAM,cAAc,WAAW,MAAM,GAAG;AACxC,UAAM,cAAc,YAAY,YAAY,SAAS,CAAC;AACtD,SAAK,cAAc,eAAe;EACtC;;qCApBS,uBAAoB,4BAAA,MAAA,CAAA;EAAA;yEAApB,uBAAoB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,OAAA,GAAA,CAAA,oBAAA,UAAA,GAAA,QAAA,GAAA,YAAA,GAAA,CAAA,QAAA,SAAA,GAAA,CAAA,QAAA,OAAA,GAAA,CAAA,QAAA,MAAA,GAAA,CAAA,QAAA,QAAA,GAAA,CAAA,QAAA,sBAAA,GAAA,CAAA,QAAA,iBAAA,GAAA,CAAA,QAAA,QAAA,GAAA,CAAA,QAAA,mBAAA,GAAA,CAAA,QAAA,UAAA,GAAA,CAAA,QAAA,MAAA,GAAA,CAAA,GAAA,MAAA,CAAA,GAAA,UAAA,SAAA,8BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACXjC,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,OAAA,CAAA,EACe,GAAA,IAAA;AACX,MAAA,iBAAA,GAAA,aAAA;AAAW,MAAA,uBAAA;AACf,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,kIAAA;AAAgI,MAAA,uBAAA,EAAI,EACrI;AAIV,MAAA,yBAAA,GAAA,SAAA,EAAS,GAAA,KAAA,EAEA,GAAA,KAAA,CAAA;AAKG,MAAA,oBAAA,GAAA,WAAA,CAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA,EAAO;AAGjC,MAAA,yBAAA,IAAA,KAAA,CAAA;AAII,MAAA,oBAAA,IAAA,WAAA,CAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAO;AAGtB,MAAA,yBAAA,IAAA,KAAA,CAAA;AAII,MAAA,oBAAA,IAAA,WAAA,CAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA,EAAO;AAG9B,MAAA,yBAAA,IAAA,KAAA,CAAA;AAII,MAAA,oBAAA,IAAA,WAAA,CAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA,EAAO;AAGvB,MAAA,yBAAA,IAAA,KAAA,CAAA;AAII,MAAA,oBAAA,IAAA,WAAA,CAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAO;AAGtB,MAAA,yBAAA,IAAA,KAAA,CAAA;AAII,MAAA,oBAAA,IAAA,WAAA,CAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA,EAAO;AAGxB,MAAA,yBAAA,IAAA,KAAA,CAAA;AAII,MAAA,oBAAA,IAAA,WAAA,CAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA,EAAO;AAG1B,MAAA,yBAAA,IAAA,KAAA,CAAA;AAII,MAAA,oBAAA,IAAA,WAAA,CAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA,EAAO;AAG9B,MAAA,yBAAA,IAAA,KAAA,CAAA;AAII,MAAA,oBAAA,IAAA,WAAA,EAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA,EAAO;AAG/B,MAAA,yBAAA,IAAA,KAAA,CAAA;AAII,MAAA,oBAAA,IAAA,WAAA,EAAA;AACA,MAAA,yBAAA,IAAA,MAAA;AAAM,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA,EAAO,EACxB;AAIR,MAAA,yBAAA,IAAA,OAAA,EAAA;AACI,MAAA,oBAAA,IAAA,eAAA;AACJ,MAAA,uBAAA,EAAM;;;AApFc,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,CAAA;AAEZ,MAAA,sBAAA,UAAA,IAAA,gBAAA,SAAA;AADA,MAAA,qBAAA,cAAA,0BAAA,IAAA,GAAA,CAAA;AAOY,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,CAAA;AAEZ,MAAA,sBAAA,UAAA,IAAA,gBAAA,OAAA;AADA,MAAA,qBAAA,cAAA,0BAAA,IAAA,GAAA,CAAA;AAOY,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,CAAA;AAEZ,MAAA,sBAAA,UAAA,IAAA,gBAAA,eAAA;AADA,MAAA,qBAAA,cAAA,0BAAA,IAAA,GAAA,CAAA;AAOY,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,CAAA;AAEZ,MAAA,sBAAA,UAAA,IAAA,gBAAA,QAAA;AADA,MAAA,qBAAA,cAAA,0BAAA,IAAA,GAAA,CAAA;AAOY,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,CAAA;AAEZ,MAAA,sBAAA,UAAA,IAAA,gBAAA,OAAA;AADA,MAAA,qBAAA,cAAA,0BAAA,IAAA,GAAA,CAAA;AAOY,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,CAAA;AAEZ,MAAA,sBAAA,UAAA,IAAA,gBAAA,SAAA;AADA,MAAA,qBAAA,cAAA,0BAAA,IAAA,GAAA,CAAA;AAOY,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,CAAA;AAEZ,MAAA,sBAAA,UAAA,IAAA,gBAAA,WAAA;AADA,MAAA,qBAAA,cAAA,0BAAA,IAAA,GAAA,CAAA;AAOY,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,CAAA;AAEZ,MAAA,sBAAA,UAAA,IAAA,gBAAA,eAAA;AADA,MAAA,qBAAA,cAAA,0BAAA,IAAA,GAAA,CAAA;AAOY,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,CAAA;AAEZ,MAAA,sBAAA,UAAA,IAAA,gBAAA,gBAAA;AADA,MAAA,qBAAA,cAAA,0BAAA,IAAA,GAAA,CAAA;AAOY,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,CAAA;AAEZ,MAAA,sBAAA,UAAA,IAAA,gBAAA,aAAA;AADA,MAAA,qBAAA,cAAA,0BAAA,IAAA,GAAA,CAAA;;oBD7EE,cAAY,cAAA,YAAA,kBAAE,mBAAiB,eAAA,GAAA,QAAA,CAAA,0kIAAA,EAAA,CAAA;;;sEAIhC,sBAAoB,CAAA;UANhC;uBACa,mBAAiB,SAClB,CAAC,cAAc,iBAAiB,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,osGAAA,EAAA,CAAA;;;;6EAIjC,sBAAoB,EAAA,WAAA,wBAAA,UAAA,yDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": ["urlSegments", "lastSegment"]}