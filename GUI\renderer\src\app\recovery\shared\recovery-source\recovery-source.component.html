<!-- Panel Header -->
<header (click)="expandPanel()">
    <h2>Recovery Source</h2>
    <fa-icon icon="folder"></fa-icon>
</header>

<!-- Expanded Content -->
@if (isPanelExpanded()) {
    <section>
        <div class="local-panel">
            <div class="local-header">
                <div class="local-title">
                    <span>Local</span>
                </div>
                <div class="filter-section">
                    <fa-icon icon="filter"></fa-icon>
                </div>
            </div>

            <div class="recovery-table">
                <div class="table-header">
                    <div class="header-col recovery-point">Select Recovery Point</div>
                    <div class="header-col size">Size</div>
                </div>

                <div class="table-body">
                    @for (recoveryPoint of recoveryPoints; track recoveryPoint.id) {
                        <div class="table-row"
                             [class.selected]="selectedSource() === recoveryPoint.id"
                             (click)="selectSource(recoveryPoint.id)">
                            <div class="row-col recovery-point">
                                <div class="row-content">
                                    @if (recoveryPoint.hasChildren) {
                                        <div class="spacer"></div>
                                        <div class="tree-icon">
                                            <div class="tree-expand"></div>
                                        </div>
                                    } @else {
                                        <fa-icon icon="desktop" class="host-icon"></fa-icon>
                                    }
                                    <span class="date-text">{{ recoveryPoint.date }}</span>
                                </div>
                            </div>
                            <div class="row-col size">
                                <span>{{ recoveryPoint.size }}</span>
                            </div>
                        </div>
                    }
                </div>
            </div>

            <div class="recovery-actions">
                <button class="button secondary" (click)="openPinDialog()">
                    Enter Recovery PIN
                </button>
            </div>
        </div>
    </section>
}
