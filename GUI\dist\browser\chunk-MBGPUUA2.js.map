{"version": 3, "sources": ["renderer/src/app/activities/activities.component.ts", "renderer/src/app/activities/activities.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-activities',\r\n  imports: [],\r\n  templateUrl: './activities.component.html',\r\n  styleUrl: './activities.component.less'\r\n})\r\nexport class ActivitiesComponent {\r\n\r\n}\r\n", "<p>activities works!</p>\r\n"], "mappings": ";;;;;;;;;;;AAQM,IAAO,sBAAP,MAAO,qBAAmB;;qCAAnB,sBAAmB;EAAA;yEAAnB,sBAAmB,WAAA,CAAA,CAAA,gBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACRhC,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,mBAAA;AAAiB,MAAA,uBAAA;;;;;sEDQP,qBAAmB,CAAA;UAN/B;uBACW,kBAAgB,SACjB,CAAA,GAAE,UAAA,+BAAA,CAAA;;;;6EAIA,qBAAmB,EAAA,WAAA,uBAAA,UAAA,uDAAA,YAAA,EAAA,CAAA;AAAA,GAAA;", "names": []}