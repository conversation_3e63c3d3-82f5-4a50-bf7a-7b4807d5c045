@import "../../../styles/variables.less";
@import "../../../styles/mixins.less";
@import "../shared/preferences.less";

:host {
    section {
        padding: 1rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: @border-radius-small;
        overflow-y: auto;

        /* Hide scrollbar for webkit browsers */
        &::-webkit-scrollbar {
            display: none;
        }

        .section {
            margin-bottom: 1rem;

            .header {
                display: flex;
                align-items: center;
                gap: .5rem;
                font-size: 1rem;
                line-height: 1.25;
                color: @text-color;
                margin-bottom: .5rem;

                h3 {
                    font-weight: 700;
                    font-size: 1rem;
                }
            }

            .body {
                padding-left: 1.75rem;

                .form-group {
                    margin-bottom: .5rem;
                }

                .options {
                    padding-left: 1.5rem;
                }

                input[type="number"] {
                    width: 5rem;
                }
            }


        }
    }
}
