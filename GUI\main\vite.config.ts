import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
    // Define build mode
    mode: process.env['NODE_ENV'] || 'production',
    // Define root directory
    root: resolve(__dirname),
    // Define build configuration
    build: {
        // Specify output directory
        outDir: '../dist/main',
        // Specify entry file
        lib: {
            entry: resolve(__dirname, 'src/main.ts'),
            // Use commonjs format for Electron main process code
            formats: ['cjs'],
            fileName: () => 'index.js',
        },
        // Enable sourcemap for debugging
        sourcemap: true,
        // Enable minify to reduce file size
        minify: 'esbuild',
        // Empty output directory
        emptyOutDir: true,
        // Set target environment for Electron main process
        target: 'node22',
        // Configure external dependencies
        rollupOptions: {
            // Mark all Electron and Node.js built-in modules as external dependencies
            external: ['electron'/* , 'node:fs', 'node:path', 'node:os' */],
            output: {
                // Ensure generated file uses .js extension
                entryFileNames: 'index.js',
            },
        },
    },
    // Configure resolution options
    resolve: {
        // Set aliases if needed
        alias: {},
    },
});
