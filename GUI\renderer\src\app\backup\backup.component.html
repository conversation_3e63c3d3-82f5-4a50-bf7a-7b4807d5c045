<!-- Header Section -->
<header>
    <div class="title">
        <h1>Backup</h1>
        <p>What, Where & When</p>
    </div>
    <div class="actions">
        <button class="process" [disabled]="!isButtonEnabled()">
            {{ isButtonEnabled() ? 'Start Backup' : 'Choose options first' }}
        </button>
        <button class="reset" (click)="resetSelections()">
            <fa-icon icon="rotate-left"></fa-icon>
        </button>
    </div>
</header>

<!-- Main Content Container -->
<section>
    <!-- Backup Source Panel -->
    <app-backup-source class="panel"
        [selectedSource]="selectedSource()"
        [expandedSource]="expandedSource()"
        [class.collapsed]="expandedDestination() !== null || showBackupSettings()"
        (sourceSelected)="selectSource($event)"
        (sourceConfigurationSaved)="saveSourceConfiguration($event)"
    ></app-backup-source>

    <!-- Backup Destination Panel -->
    <app-backup-destination class="panel"
        [selectedDestination]="selectedDestination()"
        [expandedDestination]="expandedDestination()"
        [class.collapsed]="expandedSource() !== null || showBackupSettings()"
        (destinationSelected)="selectDestination($event)"
        (destinationConfigurationSaved)="saveDestinationConfiguration($event)"
    ></app-backup-destination>

    <!-- Scheduling Panel -->
    <app-backup-schedule class="panel"
        [selectedSchedule]="selectedSchedule()"
        [class.collapsed]="expandedSource() !== null || expandedDestination() !== null || showBackupSettings()"
        (scheduleSelected)="selectSchedule($event)"
    ></app-backup-schedule>

    <!-- BackupSettings Panel -->
    @if (showBackupSettings()) {
        <app-backup-settings [@show] class="panel settings"
            (settingsSaved)="onBackupSettingsSaved($event)"
            (settingsClosed)="onBackupSettingsClosed()">
        </app-backup-settings>
    }

    <!-- Settings Panel -->
    <app-options class="panel options" (taskOptionsSelected)="onTaskOptionsSelected($event)"></app-options>
</section>
