<header>
    <h2>Backup Source</h2>
    <fa-icon icon="database"></fa-icon>
</header>
<section>
    <div
        class="item"
        [class.selected]="isSourceSelected()('local')"
        [class.expanded]="isSourceExpanded()('local')"
        [class.compact]="isSourceCompact()('local')"
        (click)="selectSource('local')"
    >
        @if (isSourceExpanded()('local')) {
            <div class="item-header-row">
                <div class="item-icon">
                    <fa-icon icon="laptop"></fa-icon>
                </div>
                <div class="item-content">
                    <div class="item-header">
                        <h3>Local</h3>
                    </div>
                </div>
            </div>
            <div class="expanded-form">
                <div class="form-row">
                    <div class="dropdown-select">
                        <span class="dropdown-text">Volume</span>
                        <div class="dropdown-arrow"></div>
                    </div>
                </div>
                <div class="file-select-table">
                    <div class="table-header">
                        <div class="header-cell">Select File</div>
                        <div class="header-cell"></div>
                        <div class="header-cell"></div>
                        <div class="header-cell"></div>
                    </div>
                    <div class="table-row">
                        <div class="row-cell">
                            <div class="expand-icon"></div>
                            Disk 0
                        </div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                    </div>
                    <div class="table-row">
                        <div class="row-cell">
                            <div class="expand-icon"></div>
                            Disk 1
                        </div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                    </div>
                    <div class="table-row">
                        <div class="row-cell">
                            <div class="expand-icon"></div>
                            Disk 2
                        </div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                    </div>
                    <div class="table-row">
                        <div class="row-cell">
                            <div class="expand-icon"></div>
                            Disk 3
                        </div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                    </div>
                </div>
                <div class="form-actions">
                    <button class="save-button" (click)="saveSourceConfiguration($event)">
                        Save
                    </button>
                </div>
            </div>
        }
        @else {
            <div class="item-icon">
                <fa-icon icon="laptop"></fa-icon>
            </div>
            <div class="item-content">
                <div class="item-header">
                    <h3>Local</h3>
                    <p>Backup local physical machine</p>
                </div>
            </div>
        }
    </div>
    <div
        class="item"
        [class.selected]="isSourceSelected()('remote')"
        [class.expanded]="isSourceExpanded()('remote')"
        [class.compact]="isSourceCompact()('remote')"
        (click)="selectSource('remote')"
    >
        @if (isSourceExpanded()('remote')) {
            <div class="item-header-row">
                <div class="item-icon">
                    <fa-icon icon="desktop"></fa-icon>
                </div>
                <div class="item-content">
                    <div class="item-header">
                        <h3>Remote</h3>
                    </div>
                </div>
            </div>
            <div class="expanded-form">
                <div class="form-row">
                    <div class="dropdown-select">
                        <span class="dropdown-text">Remote Machine</span>
                        <div class="dropdown-arrow"></div>
                    </div>
                </div>
                <div class="file-select-table">
                    <div class="table-header">
                        <div class="header-cell">Select File</div>
                        <div class="header-cell"></div>
                        <div class="header-cell"></div>
                        <div class="header-cell"></div>
                    </div>
                    <div class="table-row">
                        <div class="row-cell">
                            <div class="expand-icon"></div>
                            Remote Disk 0
                        </div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                    </div>
                    <div class="table-row">
                        <div class="row-cell">
                            <div class="expand-icon"></div>
                            Remote Disk 1
                        </div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                    </div>
                    <div class="table-row">
                        <div class="row-cell">
                            <div class="expand-icon"></div>
                            Remote Disk 2
                        </div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                    </div>
                    <div class="table-row">
                        <div class="row-cell">
                            <div class="expand-icon"></div>
                            Remote Disk 3
                        </div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                    </div>
                </div>
                <div class="form-actions">
                    <button class="save-button" (click)="saveSourceConfiguration($event)">
                        Save
                    </button>
                </div>
            </div>
        }
        @else {
            <div class="item-icon">
                <fa-icon icon="desktop"></fa-icon>
            </div>
            <div class="item-content">
                <div class="item-header">
                    <h3>Remote</h3>
                    <p>Backup remote physical machine</p>
                </div>
            </div>
        }
    </div>
    <div
        class="item"
        [class.selected]="isSourceSelected()('hypervisor')"
        [class.expanded]="isSourceExpanded()('hypervisor')"
        [class.compact]="isSourceCompact()('hypervisor')"
        (click)="selectSource('hypervisor')"
    >
        @if (isSourceExpanded()('hypervisor')) {
            <div class="item-header-row">
                <div class="item-icon">
                    <fa-icon [icon]="['fac', 'custom-hypervisor']"></fa-icon>
                </div>
                <div class="item-content">
                    <div class="item-header">
                        <h3>Hypervisor VM</h3>
                    </div>
                </div>
            </div>
            <div class="expanded-form">
                <div class="form-row">
                    <div class="dropdown-select">
                        <span class="dropdown-text">Hypervisor</span>
                        <div class="dropdown-arrow"></div>
                    </div>
                </div>
                <div class="file-select-table">
                    <div class="table-header">
                        <div class="header-cell">Select File</div>
                        <div class="header-cell"></div>
                        <div class="header-cell"></div>
                        <div class="header-cell"></div>
                    </div>
                    <div class="table-row">
                        <div class="row-cell">
                            <div class="expand-icon"></div>
                            VM-WebServer-01
                        </div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                    </div>
                    <div class="table-row">
                        <div class="row-cell">
                            <div class="expand-icon"></div>
                            VM-Database-01
                        </div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                    </div>
                    <div class="table-row">
                        <div class="row-cell">
                            <div class="expand-icon"></div>
                            VM-App-Server
                        </div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                    </div>
                    <div class="table-row">
                        <div class="row-cell">
                            <div class="expand-icon"></div>
                            VM-Test-Server
                        </div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                        <div class="row-cell"></div>
                    </div>
                </div>
                <div class="form-actions">
                    <button class="save-button" (click)="saveSourceConfiguration($event)">
                        Save
                    </button>
                </div>
            </div>
        }
        @else {
            <div class="item-icon">
                <fa-icon [icon]="['fac', 'custom-hypervisor']"></fa-icon>
            </div>
            <div class="item-content">
                <div class="item-header">
                    <h3>Hypervisor VM</h3>
                    <p>Backup Hypervisor VM</p>
                </div>
            </div>
        }
    </div>
</section>
