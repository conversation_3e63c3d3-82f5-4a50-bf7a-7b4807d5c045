/* node_modules/@angular/cdk/overlay-prebuilt.css */
.cdk-overlay-container,
.cdk-global-overlay-wrapper {
  pointer-events: none;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}
.cdk-overlay-container {
  position: fixed;
  z-index: 1000;
}
.cdk-overlay-container:empty {
  display: none;
}
.cdk-global-overlay-wrapper {
  display: flex;
  position: absolute;
  z-index: 1000;
}
.cdk-overlay-pane {
  position: absolute;
  pointer-events: auto;
  box-sizing: border-box;
  display: flex;
  max-width: 100%;
  max-height: 100%;
  z-index: 1000;
}
.cdk-overlay-backdrop {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  pointer-events: auto;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  opacity: 0;
  touch-action: manipulation;
  z-index: 1000;
  transition: opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1);
}
@media (prefers-reduced-motion) {
  .cdk-overlay-backdrop {
    transition-duration: 1ms;
  }
}
.cdk-overlay-backdrop-showing {
  opacity: 1;
}
@media (forced-colors: active) {
  .cdk-overlay-backdrop-showing {
    opacity: .6;
  }
}
.cdk-overlay-dark-backdrop {
  background: rgba(0, 0, 0, .32);
}
.cdk-overlay-transparent-backdrop {
  transition: visibility 1ms linear, opacity 1ms linear;
  visibility: hidden;
  opacity: 1;
}
.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,
.cdk-high-contrast-active .cdk-overlay-transparent-backdrop {
  opacity: 0;
  visibility: visible;
}
.cdk-overlay-backdrop-noop-animation {
  transition: none;
}
.cdk-overlay-connected-position-bounding-box {
  position: absolute;
  display: flex;
  flex-direction: column;
  min-width: 1px;
  min-height: 1px;
  z-index: 1000;
}
.cdk-global-scrollblock {
  position: fixed;
  width: 100%;
  overflow-y: scroll;
}

/* renderer/src/styles.less */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(1.25rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-1.25rem);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes cardPop {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0.625rem 1.5rem rgba(0, 0, 0, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0.375rem 1rem rgba(0, 0, 0, 0.2);
  }
}
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 78, 56, 0.4);
  }
  70% {
    box-shadow: 0 0 0 0.5rem rgba(239, 78, 56, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 78, 56, 0);
  }
}
@keyframes focusGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 78, 56, 0);
  }
  70% {
    box-shadow: 0 0 1rem 0.375rem rgba(239, 78, 56, 0.4);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 78, 56, 0);
  }
}
@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(1.25rem);
    visibility: hidden;
  }
  1% {
    visibility: visible;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
  }
}
@keyframes progressAnimate {
  from {
    width: 0;
  }
  to {
    width: var(--progress-width, 48%);
  }
}
@keyframes zoom-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html {
  font-size: 16px;
  scroll-behavior: smooth;
}
body {
  font-family:
    "Inter",
    "Noto Sans",
    sans-serif;
  background: #1A2A33;
  color: #FFFFFF;
  height: 100vh;
  overflow: hidden;
  -webkit-font-smoothing: antialiased;
}
::-webkit-scrollbar {
  width: 0.5rem;
  height: 0.5rem;
}
::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.25rem;
}
::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}
.form-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
}
.form-group.inline {
  flex-direction: row;
  align-items: center;
}
.form-group:has(input:disabled:not(.always-enable-label)) .form-label,
.form-group:has(select:disabled:not(.always-enable-label)) .form-label,
.form-group:has(textarea:disabled:not(.always-enable-label)) .form-label {
  color: rgba(255, 255, 255, 0.5);
}
.form-group .form-label {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-family:
    "inter",
    "noto sans",
    sans-serif;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #ffffff;
  white-space: nowrap;
  transition: color 300ms ease;
}
.form-group .form-label.required::after {
  content: " *";
  color: #ef4e38;
}
.form-group .form-input,
.form-group .form-textarea,
.form-group .form-select {
  font-family:
    "inter",
    "noto sans",
    sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid transparent;
  border-radius: 0.25rem;
  padding: 0.375rem 0.75rem;
  height: 1.875rem;
  width: 100%;
  transition-property:
    background-color,
    border-color,
    box-shadow,
    opacity;
  transition-timing-function: ease;
  transition-duration: 300ms;
}
.form-group .form-input::placeholder,
.form-group .form-textarea::placeholder,
.form-group .form-select::placeholder {
  color: rgba(255, 255, 255, 0.5);
}
.form-group .form-input:focus,
.form-group .form-textarea:focus,
.form-group .form-select:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.25);
  border-color: #ef4e38;
  box-shadow: 0 0 0 2px rgba(239, 78, 56, 0.2);
}
.form-group .form-input:hover:not(:focus):not(:disabled),
.form-group .form-textarea:hover:not(:focus):not(:disabled),
.form-group .form-select:hover:not(:focus):not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}
.form-group .form-input:disabled,
.form-group .form-textarea:disabled,
.form-group .form-select:disabled {
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  opacity: 0.7;
  background: rgba(255, 255, 255, 0.1);
}
.form-group .form-input.error,
.form-group .form-textarea.error,
.form-group .form-select.error {
  border-color: #ef4e38;
  background: rgba(239, 78, 56, 0.1);
}
.form-group .form-input.success,
.form-group .form-textarea.success,
.form-group .form-select.success {
  border-color: #34d399;
  background: rgba(16, 185, 129, 0.2);
}
.form-group .form-input.small,
.form-group .form-textarea.small,
.form-group .form-select.small {
  padding: 0.25rem 0.5rem;
  height: 1.5rem;
  font-size: 0.75rem;
}
.form-group .form-input.large,
.form-group .form-textarea.large,
.form-group .form-select.large {
  padding: 0.5rem 1rem;
  height: 2.25rem;
  font-size: 1rem;
}
.form-group .form-textarea {
  min-height: 5.625rem;
  resize: vertical;
}
.form-group .form-select-wrapper {
  position: relative;
}
.form-group .form-select-wrapper::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0.75rem;
  width: 0;
  height: 0;
  border-left: 0.25rem solid transparent;
  border-right: 0.25rem solid transparent;
  border-top: 0.3125rem solid #ffffff;
  transform: translateY(-50%);
  pointer-events: none;
  transition-property: border-top-color, opacity;
  transition-timing-function: ease;
  transition-duration: 300ms;
}
.form-group .form-select-wrapper:has(select:disabled):after {
  border-top-color: rgba(255, 255, 255, 0.5);
  opacity: 0.7;
}
.form-group .form-select-wrapper .form-select {
  appearance: none;
  padding-right: 2rem;
}
.form-group .form-checkbox {
  appearance: none;
  position: relative;
  width: 0.9375rem;
  height: 0.9375rem;
  border-radius: 0.1875rem;
  background: rgba(255, 255, 255, 0.2);
  transition-property: background, box-shadow;
  transition-timing-function: ease;
  transition-duration: 300ms;
}
.form-group .form-checkbox::after {
  content: "";
  position: absolute;
  top: 0.125rem;
  left: 0.3125rem;
  width: 0.25rem;
  height: 0.4375rem;
  border: 0.125rem solid #ffffff;
  border-top: 0;
  border-left: 0;
  transform: rotate(45deg);
  opacity: 0;
  transition: opacity 300ms ease;
}
.form-group .form-checkbox:focus {
  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);
}
.form-group .form-checkbox:checked {
  background: rgba(16, 185, 129, 0.5);
}
.form-group .form-checkbox:checked::after {
  opacity: 1;
}
.form-group .form-checkbox:disabled {
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.1);
}
.form-group .form-checkbox:disabled:checked::after {
  opacity: 0.5;
}
.form-group .form-checkbox:disabled + .form-checkbox-label {
  color: rgba(255, 255, 255, 0.5);
}
.form-group .form-checkbox:hover:not(:disabled):not(:checked) {
  background: rgba(255, 255, 255, 0.3);
}
.form-group .form-radio {
  appearance: none;
  position: relative;
  width: 0.9375rem;
  height: 0.9375rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transition-property:
    background,
    box-shadow,
    border-color;
  transition-timing-function: ease;
  transition-duration: 300ms;
}
.form-group .form-radio::after {
  content: "";
  position: absolute;
  top: 0.25rem;
  left: 0.25rem;
  width: 0.4375rem;
  height: 0.4375rem;
  background: #ffffff;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 300ms ease;
}
.form-group .form-radio:focus {
  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);
}
.form-group .form-radio:checked {
  background: rgba(16, 185, 129, 0.5);
}
.form-group .form-radio:checked::after {
  opacity: 1;
}
.form-group .form-radio:disabled {
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.1);
}
.form-group .form-radio:disabled:checked::after {
  opacity: 0.5;
}
.form-group .form-radio:disabled + .form-radio-label {
  color: rgba(255, 255, 255, 0.5);
}
.form-group .form-radio:hover:not(:disabled):not(:checked) {
  background: rgba(255, 255, 255, 0.3);
}
.form-group .form-range {
  appearance: none;
  width: 100%;
  height: 1.125rem;
  background: transparent;
  cursor: pointer;
  transition: opacity 300ms ease;
}
.form-group .form-range::-webkit-slider-runnable-track {
  background-color: rgba(255, 255, 255, 0.2);
  height: 0.375rem;
  border-radius: 0.25rem;
  border: none;
}
.form-group .form-range::-webkit-slider-thumb {
  appearance: none;
  height: 1.125rem;
  width: 0.5rem;
  margin-top: -0.375rem;
  border-radius: 0.25rem;
  background:
    linear-gradient(
      90deg,
      #ef4e38 0%,
      #b61e0e 100%);
  border: none;
  cursor: pointer;
  transition: box-shadow 300ms ease;
}
.form-group .form-range::-webkit-slider-thumb:hover {
  box-shadow: 0 0 2px rgba(239, 78, 56, 0.4);
}
.form-group .form-range:focus {
  outline: none;
}
.form-group .form-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 2px rgba(239, 78, 56, 0.2);
}
.form-group .form-range:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}
.form-group .form-range:disabled::-webkit-slider-thumb {
  cursor: not-allowed;
}
.form-group .form-range:disabled ~ .form-range-labels {
  color: rgba(255, 255, 255, 0.5);
}
.form-group .form-range-labels {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  width: 100%;
  font-family:
    "inter",
    "noto sans",
    sans-serif;
  font-size: 0.625rem;
  font-weight: 500;
  color: #ffffff;
  transition: color 300ms ease;
}
.form-group .form-range-labels > .label {
  flex: 1;
  text-align: center;
}
.form-group .form-range-labels > .label:first-child {
  flex: 0.5;
  text-align: left;
}
.form-group .form-range-labels > .label:last-child {
  flex: 0.5;
  text-align: right;
}
.form-group .form-input-group {
  position: relative;
  display: flex;
  align-items: center;
}
.form-group .form-input-group .form-input {
  flex: 1;
  padding-right: 2rem;
}
.form-group .form-input-group .form-input-icon {
  position: absolute;
  top: 0.3125rem;
  right: 0.3125rem;
  color: rgba(255, 255, 255, 0.5);
  pointer-events: none;
}
.form-group .form-input-group .form-input-icon.clickable {
  pointer-events: auto;
  cursor: pointer;
  transition: color 300ms ease;
}
.form-group .form-input-group .form-input-icon.clickable:hover {
  color: #ffffff;
}
.form-group .form-description {
  font-family:
    "inter",
    "noto sans",
    sans-serif;
  font-size: 12px;
  line-height: 1.33;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 4px;
}
.form-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.form-button {
  font-family:
    "inter",
    "noto sans",
    sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.36;
  color: #ffffff;
  background:
    linear-gradient(
      90deg,
      #ef4e38 0%,
      #b61e0e 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  min-height: 2.125rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;
  transition-property:
    opacity,
    transform,
    box-shadow;
  transition-timing-function: ease;
  transition-duration: 300ms;
}
.form-button:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 78, 56, 0.3);
}
.form-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(239, 78, 56, 0.2);
}
.form-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(239, 78, 56, 0.2);
}
.form-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
.form-button.form-button--secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}
.form-button.form-button--secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}
.form-button.form-button--small {
  font-size: 12px;
  padding: 6px 10px;
  min-height: 28px;
}
.form-button.form-button--large {
  font-size: 16px;
  padding: 12px 20px;
  min-height: 44px;
}
.form-error {
  color: #ef4e38;
  font-size: 12px;
  font-weight: 500;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}
.form-error::before {
  content: "\26a0";
  font-size: 14px;
}
.form-success {
  color: #34d399;
  font-size: 12px;
  font-weight: 500;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}
.form-success::before {
  content: "\2713";
  font-size: 14px;
}
.buttons {
  display: flex;
  gap: 1rem;
}
.button {
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  font-family:
    "inter",
    "noto sans",
    sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.36;
  border: 1px solid transparent;
  cursor: pointer;
  height: 2.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition-property:
    background,
    color,
    border-color,
    box-shadow;
  transition-timing-function: ease;
  transition-duration: 300ms;
}
.button.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border-color: transparent;
}
.button.secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}
.button.primary {
  background:
    linear-gradient(
      90deg,
      #ef4e38 0%,
      #b61e0e 100%);
  color: #ffffff;
  border-color: transparent;
}
.button.primary:hover:not(:disabled) {
  opacity: 0.9;
  box-shadow: 0 4px 12px rgba(239, 78, 56, 0.3);
}
.button.disabled,
.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.cdk-overlay-backdrop {
  background-color: rgba(0, 0, 0, 0.35);
}
.cdk-overlay-pane {
  animation: zoom-in 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* angular:styles/global:styles */
/*# sourceMappingURL=styles.css.map */
