import { Component, input, output, computed } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

@Component({
    selector: 'app-backup-schedule',
    templateUrl: './backup-schedule.component.html',
    styleUrl: './backup-schedule.component.less',
    imports: [FontAwesomeModule],
})
export class BackupScheduleComponent {
    // Inputs from parent component
    selectedSchedule = input<string | null>(null);
    isCollapsedVertical = input<boolean>(false);

    // Outputs to parent component
    scheduleSelected = output<string>();

    // Check if a specific schedule is selected
    isScheduleSelected = computed(() => (scheduleType: string) => this.selectedSchedule() === scheduleType);

    // Handle schedule selection
    selectSchedule(scheduleType: string): void {
        this.scheduleSelected.emit(scheduleType);
    }
}
