{"version": 3, "sources": ["renderer/src/app/tasks/tasks.component.ts", "renderer/src/app/tasks/tasks.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-tasks',\r\n  imports: [],\r\n  templateUrl: './tasks.component.html',\r\n  styleUrl: './tasks.component.less'\r\n})\r\nexport class TasksComponent {\r\n\r\n}\r\n", "<p>tasks works!</p>\r\n"], "mappings": ";;;;;;;;;;;AAQM,IAAO,iBAAP,MAAO,gBAAc;;qCAAd,iBAAc;EAAA;yEAAd,iBAAc,WAAA,CAAA,CAAA,WAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,wBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACR3B,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,cAAA;AAAY,MAAA,uBAAA;;;;;sEDQF,gBAAc,CAAA;UAN1B;uBACW,aAAW,SACZ,CAAA,GAAE,UAAA,0BAAA,CAAA;;;;6EAIA,gBAAc,EAAA,WAAA,kBAAA,UAAA,6CAAA,YAAA,EAAA,CAAA;AAAA,GAAA;", "names": []}