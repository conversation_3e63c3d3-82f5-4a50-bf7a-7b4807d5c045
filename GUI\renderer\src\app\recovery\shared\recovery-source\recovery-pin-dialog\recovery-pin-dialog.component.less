/*
Our team follows strict coding standards. Please ensure all generated code adheres to the following rules:

- Use **4 spaces for indentation**, no tabs.
- All **code comments must be in English**.
- Avoid any **unnecessary or redundant syntax**.
- Code must be **clean, idiomatic**, and follow modern best practices.
- We are building an application with **Angular 20 (renderer)** and **Electron 36 (main process)**.
- In Angular templates, **do not use deprecated syntax** such as `*ngIf` or `*ngFor`. Instead, use the **new control flow syntax** like `@if`, `@for` as recommended by the latest Angular Style Guide.
- Follow principles of **modularity, readability**, and **maintainability**.

Always write production-ready, modern, and maintainable code.
*/
:host {
    display: block;
    background: #2e4a5a;
    border-radius: .75rem; // 12px in rem
    padding: 1rem; // 16px in rem
    width: 32rem; // 512px in rem
    border: 1px solid rgba(16, 185, 129, 0.5);
}

.dialog-container {
    display: flex;
    flex-direction: column;
    gap: 1rem; // 16px in rem
}

h2 {
    font-family: Inter, sans-serif;
    font-size: 1.125rem; // 18px in rem
    font-weight: 400;
    margin: 0;
    color: #ffffff;
}

p {
    font-family: 'Noto Sans', sans-serif;
    font-size: 0.875rem; // 14px in rem
    font-weight: 300;
    color: #ffffff;
    margin: 0;
    line-height: 1.4;
}

.pin-input-grid {
    display: grid;
    grid-template-rows: 1fr 1fr;
    gap: 1rem; // 16px in rem
    margin: 1rem 0;
}

.pin-input-row {
    display: flex;
    gap: 1rem; // 16px in rem
    align-items: center;
    justify-content: center;

    input {
        width: 4.25rem; // Adjusted width to fill space
        height: 1.8125rem; // 29px in rem
        text-align: left;
        padding: .3125rem .625rem; // 5px in rem
        font-family: Inter, sans-serif;
        font-size: 0.75rem; // 12px in rem
        font-weight: 500;
        border-radius: .25rem; // 4px in rem
        border: 0;
        background-color: rgba(255, 255, 255, 0.2);
        color: #ffffff;

        &::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
            outline: none;
            border-color: #3895d3;
        }
    }

    .separator {
        font-family: 'Noto Sans', sans-serif;
        font-size: 1rem; // 16px in rem
        font-weight: 500;
        color: #ffffff;
    }
}

[cdk-dialog-actions] {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    button {
        padding: 8px 12px;
        border-radius: 8px;
        border: 1px solid transparent;
        font-family: 'Noto Sans', sans-serif;
        font-size: 14px;
        cursor: pointer;

        &.secondary {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;
        }

        &:disabled {
            background-color: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.5);
            cursor: not-allowed;
            border: 1px solid transparent;
        }

        &:not(:disabled):not(.secondary) {
            background-color: #3895d3; // A default primary color
            color: #ffffff;
        }
    }
}
