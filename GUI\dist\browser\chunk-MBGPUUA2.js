import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-TBQAZTY7.js";

// renderer/src/app/activities/activities.component.ts
var ActivitiesComponent = class _ActivitiesComponent {
  static \u0275fac = function ActivitiesComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ActivitiesComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ActivitiesComponent, selectors: [["app-activities"]], decls: 2, vars: 0, template: function ActivitiesComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "p");
      \u0275\u0275text(1, "activities works!");
      \u0275\u0275elementEnd();
    }
  }, encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ActivitiesComponent, [{
    type: Component,
    args: [{ selector: "app-activities", imports: [], template: "<p>activities works!</p>\r\n" }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ActivitiesComponent, { className: "ActivitiesComponent", filePath: "renderer/src/app/activities/activities.component.ts", lineNumber: 9 });
})();
export {
  ActivitiesComponent
};
//# sourceMappingURL=chunk-MBGPUUA2.js.map
