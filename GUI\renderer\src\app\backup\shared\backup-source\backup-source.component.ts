import { Component, input, output, computed, HostBinding } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

@Component({
    selector: 'app-backup-source',
    templateUrl: './backup-source.component.html',
    styleUrl: './backup-source.component.less',
    imports: [FontAwesomeModule],
})
export class BackupSourceComponent {
    // Inputs from parent component
    selectedSource = input<string | null>(null);
    expandedSource = input<string | null>(null);

    // Outputs to parent component
    sourceSelected = output<string>();
    sourceConfigurationSaved = output<MouseEvent>();

    // Check if a specific source is selected
    isSourceSelected = computed(() => (sourceType: string) => this.selectedSource() === sourceType);

    // Check if a specific source is expanded
    isSourceExpanded = computed(() => (sourceType: string) => this.expandedSource() === sourceType);

    // Check if a source is compact (another source is expanded)
    isSourceCompact = computed(() => (sourceType: string) =>
        this.expandedSource() !== null && this.expandedSource() !== sourceType
    );

    // Handle source selection
    selectSource(sourceType: string): void {
        this.sourceSelected.emit(sourceType);
    }

    // Handle save configuration
    saveSourceConfiguration(event: MouseEvent): void {
        this.sourceConfigurationSaved.emit(event);
    }
}
