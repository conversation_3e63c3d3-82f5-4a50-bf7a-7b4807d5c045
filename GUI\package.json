{"name": "renderer", "version": "0.0.0", "type": "module", "scripts": {"start": "electron dist", "dev": "ng serve", "build": "npm run build:browser && npm run build:main && npm run post:build", "build:main": "vite build -c main/vite.config.ts", "post:build": "tsx tasks/post-build.ts", "build:browser": "ng build --configuration development", "package": "electron-builder"}, "private": true, "dependencies": {"@angular/animations": "^20.0.2", "@angular/cdk": "^20.0.4", "@angular/common": "^20.0.2", "@angular/compiler": "^20.0.2", "@angular/core": "^20.0.2", "@angular/forms": "^20.0.2", "@angular/platform-browser": "^20.0.2", "@angular/platform-browser-dynamic": "^20.0.2", "@angular/router": "^20.0.2", "@fortawesome/angular-fontawesome": "^2.0.1", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/pro-duotone-svg-icons": "^6.7.2", "@fortawesome/pro-light-svg-icons": "^6.7.2", "@fortawesome/pro-regular-svg-icons": "^6.7.2", "@fortawesome/pro-solid-svg-icons": "^6.7.2", "@fortawesome/pro-thin-svg-icons": "^6.7.2", "rxjs": "~7.8.0", "tslib": "^2.3.0"}, "devDependencies": {"@angular/build": "^20.0.1", "@angular/cli": "^20.0.1", "@angular/compiler-cli": "^20.0.2", "@types/node": "^22.15.17", "electron": "^37.1.0", "electron-builder": "^26.0.17", "less": "^4.2.0", "tsx": "^4.19.4", "typescript": "~5.8.3", "vite": "^6.3.5", "vite-electron-plugin": "^0.8.3"}}