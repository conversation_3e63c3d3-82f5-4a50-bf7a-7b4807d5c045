import { Component, signal, computed } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FormsModule } from '@angular/forms';
import { BackupSourceComponent } from './shared/backup-source/backup-source.component';
import { BackupDestinationComponent } from './shared/backup-destination/backup-destination.component';
import { BackupScheduleComponent } from './shared/backup-schedule/backup-schedule.component';
import { OptionsComponent } from './shared/options/options.component';
import { BackupSettingsComponent, BackupSettings } from './shared/backup-settings/backup-settings.component';
import { SidebarService } from '../../services/sidebar.service';
import { animate, style, transition, trigger } from '@angular/animations';

@Component({
    selector: 'app-backup',
    imports: [
        FormsModule,
        FontAwesomeModule,
        BackupSourceComponent,
        BackupDestinationComponent,
        BackupScheduleComponent,
        BackupSettingsComponent,
        OptionsComponent,
    ],
    templateUrl: './backup.component.html',
    styleUrl: './backup.component.less',
    animations: [
        trigger('show', [
            transition(':enter', [
                style({
                    flex: 0,
                    opacity: 0,
                }),
                animate('300ms ease', style({
                    flex: 1,
                    opacity: 1,
                }))
            ]),
            transition(':leave', [
                style({
                    flex: 1,
                    opacity: 1
                }),
                animate('300ms ease', style({
                    flex: 0,
                    opacity: 0
                }))
            ])
        ])
    ]
})
export class BackupComponent {
    // Task configuration using signals
    taskName = signal<string>('');

    // Selected options using signals
    selectedSource = signal<string | null>(null);
    selectedDestination = signal<string | null>(null);
    selectedSchedule = signal<string | null>(null);

    // Expanded state for source panel
    expandedSource = signal<string | null>(null);

    // Expanded state for destination panel
    expandedDestination = signal<string | null>(null);

    // BackupSettings visibility and state
    showBackupSettings = signal<boolean>(false);
    backupSettings = signal<BackupSettings | null>(null);

    constructor(
        private readonly Sidebar: SidebarService,
    ) {}

    // Handle selection changes
    selectSource(sourceId: string): void {
        this.selectedSource.set(sourceId);
        this.expandedSource.set(sourceId); // Also set expanded state
        this.expandedDestination.set(null); // Collapse destination when expanding source
        this.checkEnableButton();
    }

    selectDestination(destinationId: string): void {
        this.selectedDestination.set(destinationId);
        this.expandedDestination.set(destinationId); // Also set expanded state
        this.expandedSource.set(null); // Collapse source when expanding destination
        this.checkEnableButton();
    }    // Handle task name input change
    onTaskNameChange(event: Event): void {
        const target = event.target as HTMLInputElement;
        this.taskName.set(target.value);
    }

    selectSchedule(scheduleId: string): void {
        this.selectedSchedule.set(scheduleId);
        this.checkEnableButton();
    }

    // Handle Task Options selection from Options component
    onTaskOptionsSelected(isSelected: boolean): void {
        this.showBackupSettings.set(isSelected);

        // Collapse other panels when backup settings is shown
        if (isSelected) {
            this.expandedSource.set(null);
            this.expandedDestination.set(null);
            this.Sidebar.collapse(); // Collapse sidebar when showing settings
        }
    }

    // Handle backup settings save
    onBackupSettingsSaved(settings: BackupSettings): void {
        this.backupSettings.set(settings);
        this.showBackupSettings.set(false);
        console.log('Backup settings saved:', settings);
    }

    // Handle backup settings close
    onBackupSettingsClosed(): void {
        this.showBackupSettings.set(false);
    }

    // Check if all options are selected to enable the button
    isButtonEnabled = computed(() => {
        return this.selectedSource() !== null &&
               this.selectedDestination() !== null &&
               this.selectedSchedule() !== null;
    });

    // Helper method to check and update button state
    checkEnableButton(): void {
        // This method exists for future extensibility
    }

    // Process backup with selected options
    processBackup(): void {
        if (this.isButtonEnabled()) {
            // Implement backup process logic here
            console.log('Processing backup with options:', {
                source: this.selectedSource(),
                destination: this.selectedDestination(),
                schedule: this.selectedSchedule()
            });
        }
    }    // Reset all selections
    resetSelections(): void {
        this.taskName.set('');
        this.selectedSource.set(null);
        this.selectedDestination.set(null);
        this.selectedSchedule.set(null);
        this.expandedSource.set(null); // Also reset expanded state
        this.expandedDestination.set(null); // Also reset expanded destination state
    }

    // Save backup source configuration
    saveSourceConfiguration($event: MouseEvent): void {
        $event.stopPropagation(); // Prevent event bubbling

        const sourceType = this.selectedSource();

        if (sourceType === 'local') {
            // Implement save logic for local backup source configuration
            console.log('Saving local backup source configuration');
        } else if (sourceType === 'remote') {
            // Implement save logic for remote backup source configuration
            console.log('Saving remote backup source configuration');
        } else if (sourceType === 'hypervisor') {
            // Implement save logic for hypervisor backup source configuration
            console.log('Saving hypervisor backup source configuration');
        }

        // Close expanded state but keep selection state
        this.expandedSource.set(null);
    }

    // Save backup destination configuration
    saveDestinationConfiguration($event: MouseEvent): void {
        $event.stopPropagation(); // Prevent event bubbling

        const destinationType = this.selectedDestination();

        if (destinationType === 'local') {
            // Implement save logic for local backup destination configuration
            console.log('Saving local backup destination configuration');
        } else if (destinationType === 'network') {
            // Implement save logic for network backup destination configuration
            console.log('Saving network backup destination configuration');
        } else if (destinationType === 'actiphy') {
            // Implement save logic for actiphy backup destination configuration
            console.log('Saving actiphy backup destination configuration');
        } else if (destinationType === 'cloud') {
            // Implement save logic for cloud backup destination configuration
            console.log('Saving cloud backup destination configuration');
        } else if (destinationType === 'azure') {
            // Implement save logic for azure backup destination configuration
            console.log('Saving azure backup destination configuration');
        } else if (destinationType === 'amazon') {
            // Implement save logic for amazon backup destination configuration
            console.log('Saving amazon backup destination configuration');
        } else if (destinationType === 'tape') {
            // Implement save logic for tape backup destination configuration
            console.log('Saving tape backup destination configuration');
        }

        // Close expanded state but keep selection state
        this.expandedDestination.set(null);
    }
}
