@import '../../../../styles/variables.less';
@import '../../../../styles/mixins.less';

:host {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.75rem;
    overflow: hidden;
    cursor: pointer;
    padding: 1rem;

    // Panel Header
    header {
        flex: 0 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        padding-bottom: 1rem;

        h2 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
            line-height: 1.125rem;
            color: @text-color;
        }

        .icon-container {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0);
            padding: 0;
        }
    }

    // Collapsed vertical panel styling
    &.collapsed {
        header {
            writing-mode: vertical-lr;
            text-orientation: mixed;
            padding: 0;
            height: 100%;
            flex-direction: row-reverse;
            justify-content: flex-end;

            h2 {
                font-size: 1rem;
                font-weight: 600;
                line-height: 1.2;
                text-align: center;
                margin: 0;
                writing-mode: vertical-lr;
                text-orientation: mixed;
                color: @text-color;
                letter-spacing: -0.02em;
                transform: rotate(180deg);
            }

            .icon-container {
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                border-radius: 6px;
                background: rgba(255, 255, 255, 0);
                padding: 0;
            }
        }

        section {
            display: none;
        }
    }

    section {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;

        // Hide scrollbar for Chromium/WebKit (Electron)
        &::-webkit-scrollbar {
            display: none;
        }

        .local-panel {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: .5rem;
            padding: 1rem;

            .local-header {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .local-title {
                    display: flex;
                    align-items: center;
                    font-size: 1rem;
                    font-weight: 500;
                    color: @text-color;
                }

                .filter-section {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 18px;
                    height: 18px;
                    color: @text-color;
                }
            }

            .recovery-table {
                background: rgba(16, 185, 129, 0.2);
                border: 1px solid rgba(16, 185, 129, 0.5);
                border-radius: 0.5rem;
                overflow: hidden;

                .table-header {
                    display: flex;
                    background: rgba(255, 255, 255, 0.05);

                    .header-col {
                        display: flex;
                        align-items: center;
                        padding: 8px 10px;
                        font-size: 0.75rem;
                        font-weight: 400;
                        color: @text-color;
                        line-height: 1.36;

                        &.recovery-point {
                            flex: 1;
                            justify-content: flex-start;
                            padding-left: 18px;
                        }

                        &.size {
                            width: 175px;
                            justify-content: flex-end;
                            padding-right: 20px;
                        }
                    }
                }

                .table-body {
                    .table-row {
                        display: flex;
                        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                        .transition(background-color);

                        &:hover {
                            background: rgba(255, 255, 255, 0.05);
                        }

                        &.selected {
                            background: rgba(16, 185, 129, 0.1);
                        }

                        &:last-child {
                            border-bottom: none;
                        }

                        .row-col {
                            display: flex;
                            align-items: center;
                            padding: 8px 10px;
                            font-size: 0.75rem;
                            color: @text-color;
                            line-height: 1.36;
                            min-height: 31px;

                            &.recovery-point {
                                flex: 1;
                                padding-left: 8px;

                                .row-content {
                                    display: flex;
                                    align-items: center;
                                    gap: 10px;
                                    width: 100%;

                                    .spacer {
                                        width: 15px;
                                        height: 15px;
                                    }

                                    .tree-icon {
                                        width: 15px;
                                        height: 15px;
                                        position: relative;

                                        .tree-expand {
                                            width: 15px;
                                            height: 15px;
                                            background: rgba(255, 255, 255, 0.2);
                                            border-radius: 3px;
                                            position: relative;

                                            &::after {
                                                content: '';
                                                position: absolute;
                                                top: 6px;
                                                left: 1.5px;
                                                width: 12px;
                                                height: 3px;
                                                background: #D9D9D9;
                                                border-radius: 2px;
                                            }
                                        }
                                    }

                                    .host-icon {
                                        width: 16px;
                                        height: 14px;
                                        color: @text-color;
                                    }

                                    .date-text {
                                        flex: 1;
                                        text-align: left;
                                    }
                                }
                            }

                            &.size {
                                width: 175px;
                                justify-content: flex-end;
                                padding-right: 20px;
                                text-align: right;
                            }
                        }
                    }
                }
            }

            .recovery-actions {
                display: flex;
                justify-content: flex-end;
            }
        }
    }
}
