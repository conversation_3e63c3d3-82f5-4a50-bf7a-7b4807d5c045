{"version": 3, "sources": ["renderer/src/app/preferences/backup/backup.component.ts", "renderer/src/app/preferences/backup/backup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\n\r\n@Component({\r\n    selector: 'app-backup',\r\n    imports: [FontAwesomeModule],\r\n    templateUrl: './backup.component.html',\r\n    styleUrl: './backup.component.less'\r\n})\r\nexport class BackupComponent {\r\n}\r\n", "<header>\r\n    <div class=\"icon\">\r\n        <fa-icon icon=\"archive\"></fa-icon>\r\n    </div>\r\n    <h2>Backup Settings</h2>\r\n</header>\r\n\r\n<section>\r\n    <p>Backup settings will be implemented here.</p>\r\n</section>\r\n\r\n<footer>\r\n    <span class=\"notice\">0 Configuration changes waiting to be applied</span>\r\n    <div class=\"buttons\">\r\n        <button type=\"button\" class=\"button secondary\">Cancel</button>\r\n        <button type=\"button\" class=\"button primary\">Apply</button>\r\n    </div>\r\n</footer>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AASM,IAAO,kBAAP,MAAO,iBAAe;;qCAAf,kBAAe;EAAA;yEAAf,kBAAe,WAAA,CAAA,CAAA,YAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,SAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,WAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,SAAA,CAAA,GAAA,UAAA,SAAA,yBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACT5B,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,OAAA,CAAA;AAEA,MAAA,oBAAA,GAAA,WAAA,CAAA;AACJ,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,iBAAA;AAAe,MAAA,uBAAA,EAAK;AAG5B,MAAA,yBAAA,GAAA,SAAA,EAAS,GAAA,GAAA;AACF,MAAA,iBAAA,GAAA,2CAAA;AAAyC,MAAA,uBAAA,EAAI;AAGpD,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,QAAA,CAAA;AACiB,MAAA,iBAAA,IAAA,+CAAA;AAA6C,MAAA,uBAAA;AAClE,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,UAAA,CAAA;AAC8B,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AACrD,MAAA,yBAAA,IAAA,UAAA,CAAA;AAA6C,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAS,EACzD;;oBDXI,mBAAiB,eAAA,GAAA,QAAA,CAAA,o9CAAA,EAAA,CAAA;;;sEAIlB,iBAAe,CAAA;UAN3B;uBACa,cAAY,SACb,CAAC,iBAAiB,GAAC,UAAA,ohBAAA,QAAA,CAAA,+rCAAA,EAAA,CAAA;;;;6EAInB,iBAAe,EAAA,WAAA,mBAAA,UAAA,2DAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}