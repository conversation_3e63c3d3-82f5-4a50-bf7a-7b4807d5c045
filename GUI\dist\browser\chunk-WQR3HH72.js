import {
  SidebarService,
  animate,
  style,
  transition,
  trigger
} from "./chunk-7GEJEGK5.js";
import {
  CheckboxControlValueAccessor,
  DefaultValueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel,
  NgSelectOption,
  NumberValueAccessor,
  RadioControlValueAccessor,
  RangeValueAccessor,
  SelectControlValueAccessor,
  ɵNgSelectMultipleOption
} from "./chunk-QOWQNEHV.js";
import {
  CommonModule,
  DatePipe,
  FaIconComponent,
  FontAwesomeModule
} from "./chunk-N34W2DJ7.js";
import {
  Component,
  EventEmitter,
  Output,
  computed,
  input,
  output,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵconditionalCreate,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtext,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-TBQAZTY7.js";

// renderer/src/app/backup/shared/backup-source/backup-source.component.ts
var _c0 = () => ["fac", "custom-hypervisor"];
function BackupSourceComponent_Conditional_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 2)(1, "div", 3);
    \u0275\u0275element(2, "fa-icon", 4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 5)(4, "div", 6)(5, "h3");
    \u0275\u0275text(6, "Local");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(7, "div", 7)(8, "div", 8)(9, "div", 9)(10, "span", 10);
    \u0275\u0275text(11, "Volume");
    \u0275\u0275elementEnd();
    \u0275\u0275element(12, "div", 11);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(13, "div", 12)(14, "div", 13)(15, "div", 14);
    \u0275\u0275text(16, "Select File");
    \u0275\u0275elementEnd();
    \u0275\u0275element(17, "div", 14)(18, "div", 14)(19, "div", 14);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "div", 15)(21, "div", 16);
    \u0275\u0275element(22, "div", 17);
    \u0275\u0275text(23, " Disk 0 ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(24, "div", 16)(25, "div", 16)(26, "div", 16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(27, "div", 15)(28, "div", 16);
    \u0275\u0275element(29, "div", 17);
    \u0275\u0275text(30, " Disk 1 ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(31, "div", 16)(32, "div", 16)(33, "div", 16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(34, "div", 15)(35, "div", 16);
    \u0275\u0275element(36, "div", 17);
    \u0275\u0275text(37, " Disk 2 ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(38, "div", 16)(39, "div", 16)(40, "div", 16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(41, "div", 15)(42, "div", 16);
    \u0275\u0275element(43, "div", 17);
    \u0275\u0275text(44, " Disk 3 ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(45, "div", 16)(46, "div", 16)(47, "div", 16);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(48, "div", 18)(49, "button", 19);
    \u0275\u0275listener("click", function BackupSourceComponent_Conditional_6_Template_button_click_49_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.saveSourceConfiguration($event));
    });
    \u0275\u0275text(50, " Save ");
    \u0275\u0275elementEnd()()();
  }
}
function BackupSourceComponent_Conditional_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 3);
    \u0275\u0275element(1, "fa-icon", 4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(2, "div", 5)(3, "div", 6)(4, "h3");
    \u0275\u0275text(5, "Local");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "p");
    \u0275\u0275text(7, "Backup local physical machine");
    \u0275\u0275elementEnd()()();
  }
}
function BackupSourceComponent_Conditional_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 2)(1, "div", 3);
    \u0275\u0275element(2, "fa-icon", 20);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 5)(4, "div", 6)(5, "h3");
    \u0275\u0275text(6, "Remote");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(7, "div", 7)(8, "div", 8)(9, "div", 9)(10, "span", 10);
    \u0275\u0275text(11, "Remote Machine");
    \u0275\u0275elementEnd();
    \u0275\u0275element(12, "div", 11);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(13, "div", 12)(14, "div", 13)(15, "div", 14);
    \u0275\u0275text(16, "Select File");
    \u0275\u0275elementEnd();
    \u0275\u0275element(17, "div", 14)(18, "div", 14)(19, "div", 14);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "div", 15)(21, "div", 16);
    \u0275\u0275element(22, "div", 17);
    \u0275\u0275text(23, " Remote Disk 0 ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(24, "div", 16)(25, "div", 16)(26, "div", 16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(27, "div", 15)(28, "div", 16);
    \u0275\u0275element(29, "div", 17);
    \u0275\u0275text(30, " Remote Disk 1 ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(31, "div", 16)(32, "div", 16)(33, "div", 16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(34, "div", 15)(35, "div", 16);
    \u0275\u0275element(36, "div", 17);
    \u0275\u0275text(37, " Remote Disk 2 ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(38, "div", 16)(39, "div", 16)(40, "div", 16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(41, "div", 15)(42, "div", 16);
    \u0275\u0275element(43, "div", 17);
    \u0275\u0275text(44, " Remote Disk 3 ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(45, "div", 16)(46, "div", 16)(47, "div", 16);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(48, "div", 18)(49, "button", 19);
    \u0275\u0275listener("click", function BackupSourceComponent_Conditional_9_Template_button_click_49_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.saveSourceConfiguration($event));
    });
    \u0275\u0275text(50, " Save ");
    \u0275\u0275elementEnd()()();
  }
}
function BackupSourceComponent_Conditional_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 3);
    \u0275\u0275element(1, "fa-icon", 20);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(2, "div", 5)(3, "div", 6)(4, "h3");
    \u0275\u0275text(5, "Remote");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "p");
    \u0275\u0275text(7, "Backup remote physical machine");
    \u0275\u0275elementEnd()()();
  }
}
function BackupSourceComponent_Conditional_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 2)(1, "div", 3);
    \u0275\u0275element(2, "fa-icon", 21);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 5)(4, "div", 6)(5, "h3");
    \u0275\u0275text(6, "Hypervisor VM");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(7, "div", 7)(8, "div", 8)(9, "div", 9)(10, "span", 10);
    \u0275\u0275text(11, "Hypervisor");
    \u0275\u0275elementEnd();
    \u0275\u0275element(12, "div", 11);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(13, "div", 12)(14, "div", 13)(15, "div", 14);
    \u0275\u0275text(16, "Select File");
    \u0275\u0275elementEnd();
    \u0275\u0275element(17, "div", 14)(18, "div", 14)(19, "div", 14);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "div", 15)(21, "div", 16);
    \u0275\u0275element(22, "div", 17);
    \u0275\u0275text(23, " VM-WebServer-01 ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(24, "div", 16)(25, "div", 16)(26, "div", 16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(27, "div", 15)(28, "div", 16);
    \u0275\u0275element(29, "div", 17);
    \u0275\u0275text(30, " VM-Database-01 ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(31, "div", 16)(32, "div", 16)(33, "div", 16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(34, "div", 15)(35, "div", 16);
    \u0275\u0275element(36, "div", 17);
    \u0275\u0275text(37, " VM-App-Server ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(38, "div", 16)(39, "div", 16)(40, "div", 16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(41, "div", 15)(42, "div", 16);
    \u0275\u0275element(43, "div", 17);
    \u0275\u0275text(44, " VM-Test-Server ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(45, "div", 16)(46, "div", 16)(47, "div", 16);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(48, "div", 18)(49, "button", 19);
    \u0275\u0275listener("click", function BackupSourceComponent_Conditional_12_Template_button_click_49_listener($event) {
      \u0275\u0275restoreView(_r4);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.saveSourceConfiguration($event));
    });
    \u0275\u0275text(50, " Save ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    \u0275\u0275advance(2);
    \u0275\u0275property("icon", \u0275\u0275pureFunction0(1, _c0));
  }
}
function BackupSourceComponent_Conditional_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 3);
    \u0275\u0275element(1, "fa-icon", 21);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(2, "div", 5)(3, "div", 6)(4, "h3");
    \u0275\u0275text(5, "Hypervisor VM");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "p");
    \u0275\u0275text(7, "Backup Hypervisor VM");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    \u0275\u0275advance();
    \u0275\u0275property("icon", \u0275\u0275pureFunction0(1, _c0));
  }
}
var BackupSourceComponent = class _BackupSourceComponent {
  // Inputs from parent component
  selectedSource = input(null);
  expandedSource = input(null);
  // Outputs to parent component
  sourceSelected = output();
  sourceConfigurationSaved = output();
  // Check if a specific source is selected
  isSourceSelected = computed(() => (sourceType) => this.selectedSource() === sourceType);
  // Check if a specific source is expanded
  isSourceExpanded = computed(() => (sourceType) => this.expandedSource() === sourceType);
  // Check if a source is compact (another source is expanded)
  isSourceCompact = computed(() => (sourceType) => this.expandedSource() !== null && this.expandedSource() !== sourceType);
  // Handle source selection
  selectSource(sourceType) {
    this.sourceSelected.emit(sourceType);
  }
  // Handle save configuration
  saveSourceConfiguration(event) {
    this.sourceConfigurationSaved.emit(event);
  }
  static \u0275fac = function BackupSourceComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _BackupSourceComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _BackupSourceComponent, selectors: [["app-backup-source"]], inputs: { selectedSource: [1, "selectedSource"], expandedSource: [1, "expandedSource"] }, outputs: { sourceSelected: "sourceSelected", sourceConfigurationSaved: "sourceConfigurationSaved" }, decls: 14, vars: 21, consts: [["icon", "database"], [1, "item", 3, "click"], [1, "item-header-row"], [1, "item-icon"], ["icon", "laptop"], [1, "item-content"], [1, "item-header"], [1, "expanded-form"], [1, "form-row"], [1, "dropdown-select"], [1, "dropdown-text"], [1, "dropdown-arrow"], [1, "file-select-table"], [1, "table-header"], [1, "header-cell"], [1, "table-row"], [1, "row-cell"], [1, "expand-icon"], [1, "form-actions"], [1, "save-button", 3, "click"], ["icon", "desktop"], [3, "icon"]], template: function BackupSourceComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "header")(1, "h2");
      \u0275\u0275text(2, "Backup Source");
      \u0275\u0275elementEnd();
      \u0275\u0275element(3, "fa-icon", 0);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "section")(5, "div", 1);
      \u0275\u0275listener("click", function BackupSourceComponent_Template_div_click_5_listener() {
        return ctx.selectSource("local");
      });
      \u0275\u0275conditionalCreate(6, BackupSourceComponent_Conditional_6_Template, 51, 0)(7, BackupSourceComponent_Conditional_7_Template, 8, 0);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(8, "div", 1);
      \u0275\u0275listener("click", function BackupSourceComponent_Template_div_click_8_listener() {
        return ctx.selectSource("remote");
      });
      \u0275\u0275conditionalCreate(9, BackupSourceComponent_Conditional_9_Template, 51, 0)(10, BackupSourceComponent_Conditional_10_Template, 8, 0);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "div", 1);
      \u0275\u0275listener("click", function BackupSourceComponent_Template_div_click_11_listener() {
        return ctx.selectSource("hypervisor");
      });
      \u0275\u0275conditionalCreate(12, BackupSourceComponent_Conditional_12_Template, 51, 2)(13, BackupSourceComponent_Conditional_13_Template, 8, 2);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(5);
      \u0275\u0275classProp("selected", ctx.isSourceSelected()("local"))("expanded", ctx.isSourceExpanded()("local"))("compact", ctx.isSourceCompact()("local"));
      \u0275\u0275advance();
      \u0275\u0275conditional(ctx.isSourceExpanded()("local") ? 6 : 7);
      \u0275\u0275advance(2);
      \u0275\u0275classProp("selected", ctx.isSourceSelected()("remote"))("expanded", ctx.isSourceExpanded()("remote"))("compact", ctx.isSourceCompact()("remote"));
      \u0275\u0275advance();
      \u0275\u0275conditional(ctx.isSourceExpanded()("remote") ? 9 : 10);
      \u0275\u0275advance(2);
      \u0275\u0275classProp("selected", ctx.isSourceSelected()("hypervisor"))("expanded", ctx.isSourceExpanded()("hypervisor"))("compact", ctx.isSourceCompact()("hypervisor"));
      \u0275\u0275advance();
      \u0275\u0275conditional(ctx.isSourceExpanded()("hypervisor") ? 12 : 13);
    }
  }, dependencies: [FontAwesomeModule, FaIconComponent], styles: ['\n\n[_nghost-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 1rem;\n  height: 100%;\n  width: 5rem;\n  border-radius: 0.75rem;\n  opacity: 0;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-bottom: 1rem;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0;\n  line-height: 1.125rem;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr;\n  grid-template-rows: repeat(3, 1fr);\n  gap: 1rem;\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n  grid-auto-rows: min-content;\n  min-height: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]::-webkit-scrollbar {\n  display: none;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  background: rgba(255, 255, 255, 0);\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  cursor: pointer;\n  transition: background 0.25s ease, border-color 0.25s ease;\n  flex-shrink: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.05);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected[_ngcontent-%COMP%] {\n  border-color: rgba(16, 185, 129, 0.6);\n  background: rgba(16, 185, 129, 0.08);\n  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%] {\n  flex-direction: column;\n  align-items: flex-start;\n  height: 100%;\n  min-height: 200px;\n  padding: 10px 24px 24px 10px;\n  transform: none;\n  display: flex;\n  flex: 1;\n  grid-column: 1 / span 2;\n  grid-row: 1;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .item-header-row[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  width: 100%;\n  padding: 10px 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .item-header-row[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .item-header-row[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\n  padding: 0;\n  gap: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .item-header-row[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 16px;\n  font-weight: 500;\n  margin: 0;\n  line-height: 1.25;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%] {\n  width: 100%;\n  padding-left: 40px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .dropdown-select[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.2);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n  padding: 5px 10px;\n  color: #ffffff;\n  font-size: 12px;\n  line-height: 1.67;\n  width: 223px;\n  height: 29px;\n  box-sizing: border-box;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  cursor: pointer;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .dropdown-select[_ngcontent-%COMP%]   .dropdown-text[_ngcontent-%COMP%] {\n  color: #ffffff;\n  font-weight: 500;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .dropdown-select[_ngcontent-%COMP%]   .dropdown-arrow[_ngcontent-%COMP%] {\n  width: 10px;\n  height: 9px;\n  background: #D9D9D9;\n  clip-path: polygon(50% 70%, 0% 0%, 100% 0%);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%] {\n  background: rgba(16, 185, 129, 0.2);\n  border: 1px solid rgba(16, 185, 129, 0.5);\n  border-radius: 8px;\n  padding: 0;\n  width: 100%;\n  min-height: 150px;\n  overflow: hidden;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.05);\n  padding: 0;\n  display: flex;\n  border-bottom: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .header-cell[_ngcontent-%COMP%] {\n  padding: 0 0 0 10px;\n  font-size: 12px;\n  line-height: 1.36;\n  color: #ffffff;\n  text-align: left;\n  height: 31px;\n  display: flex;\n  align-items: center;\n  font-weight: 400;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .header-cell[_ngcontent-%COMP%]:first-child {\n  width: 175px;\n  justify-content: flex-start;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .header-cell[_ngcontent-%COMP%]:nth-child(2) {\n  width: 175px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .header-cell[_ngcontent-%COMP%]:nth-child(3) {\n  width: 138px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .header-cell[_ngcontent-%COMP%]:nth-child(4) {\n  width: 135px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .header-cell[_ngcontent-%COMP%]:last-child {\n  flex: 1;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%] {\n  display: flex;\n  border-bottom: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%] {\n  padding: 0 0 0 10px;\n  font-size: 12px;\n  line-height: 1.36;\n  color: #ffffff;\n  height: 31px;\n  display: flex;\n  align-items: center;\n  font-weight: 400;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%]:first-child {\n  width: 177px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%]:first-child   .expand-icon[_ngcontent-%COMP%] {\n  width: 15px;\n  height: 15px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 3px;\n  margin-right: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  flex-shrink: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%]:first-child   .expand-icon[_ngcontent-%COMP%]::after {\n  content: "";\n  width: 12px;\n  height: 12px;\n  background: #D9D9D9;\n  border-radius: 2px;\n  clip-path: polygon(50% 20%, 20% 80%, 80% 80%);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%]:nth-child(2) {\n  width: 175px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%]:nth-child(3) {\n  width: 140px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%]:last-child {\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  padding-top: 16px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      90deg,\n      #ef4e38 0%,\n      #b61e0e 100%);\n  border: none;\n  border-radius: 8px;\n  color: #ffffff;\n  font-family: "Noto Sans", sans-serif;\n  font-size: 14px;\n  font-weight: 400;\n  line-height: 1.36;\n  text-align: center;\n  padding: 8px 12px;\n  width: 194px;\n  height: 34px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-sizing: border-box;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 4px 8px rgba(239, 78, 56, 0.3);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]:active {\n  box-shadow: 0 2px 4px rgba(239, 78, 56, 0.3);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected.expanded[_ngcontent-%COMP%]   .expanded-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]:focus {\n  outline: 2px solid rgba(239, 78, 56, 0.5);\n  outline-offset: 2px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  padding: 1rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  gap: 0.5em;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  font-weight: 500;\n  margin: 0;\n  line-height: 1.375;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n  line-height: 1.375;\n  opacity: 0.8;\n  margin-top: 0.25rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.compact[_ngcontent-%COMP%] {\n  height: 74px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.compact[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%] {\n  width: 52px;\n  height: 52px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.compact[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\n  padding: 8px 0;\n  gap: 4px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.compact[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 16px;\n  font-weight: 500;\n  line-height: 1.36;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.compact[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 13px;\n  line-height: 1.36;\n  margin-top: 0;\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  padding: 0;\n  height: 100%;\n  flex-direction: row-reverse;\n  justify-content: flex-end;\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-align: center;\n  margin: 0;\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  color: #ffffff;\n  letter-spacing: -0.02em;\n  transform: rotate(180deg);\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n.collapsed[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  display: none;\n}\n/*# sourceMappingURL=backup-source.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BackupSourceComponent, [{
    type: Component,
    args: [{ selector: "app-backup-source", imports: [FontAwesomeModule], template: `<header>\r
    <h2>Backup Source</h2>\r
    <fa-icon icon="database"></fa-icon>\r
</header>\r
<section>\r
    <div\r
        class="item"\r
        [class.selected]="isSourceSelected()('local')"\r
        [class.expanded]="isSourceExpanded()('local')"\r
        [class.compact]="isSourceCompact()('local')"\r
        (click)="selectSource('local')"\r
    >\r
        @if (isSourceExpanded()('local')) {\r
            <div class="item-header-row">\r
                <div class="item-icon">\r
                    <fa-icon icon="laptop"></fa-icon>\r
                </div>\r
                <div class="item-content">\r
                    <div class="item-header">\r
                        <h3>Local</h3>\r
                    </div>\r
                </div>\r
            </div>\r
            <div class="expanded-form">\r
                <div class="form-row">\r
                    <div class="dropdown-select">\r
                        <span class="dropdown-text">Volume</span>\r
                        <div class="dropdown-arrow"></div>\r
                    </div>\r
                </div>\r
                <div class="file-select-table">\r
                    <div class="table-header">\r
                        <div class="header-cell">Select File</div>\r
                        <div class="header-cell"></div>\r
                        <div class="header-cell"></div>\r
                        <div class="header-cell"></div>\r
                    </div>\r
                    <div class="table-row">\r
                        <div class="row-cell">\r
                            <div class="expand-icon"></div>\r
                            Disk 0\r
                        </div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                    </div>\r
                    <div class="table-row">\r
                        <div class="row-cell">\r
                            <div class="expand-icon"></div>\r
                            Disk 1\r
                        </div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                    </div>\r
                    <div class="table-row">\r
                        <div class="row-cell">\r
                            <div class="expand-icon"></div>\r
                            Disk 2\r
                        </div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                    </div>\r
                    <div class="table-row">\r
                        <div class="row-cell">\r
                            <div class="expand-icon"></div>\r
                            Disk 3\r
                        </div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                    </div>\r
                </div>\r
                <div class="form-actions">\r
                    <button class="save-button" (click)="saveSourceConfiguration($event)">\r
                        Save\r
                    </button>\r
                </div>\r
            </div>\r
        }\r
        @else {\r
            <div class="item-icon">\r
                <fa-icon icon="laptop"></fa-icon>\r
            </div>\r
            <div class="item-content">\r
                <div class="item-header">\r
                    <h3>Local</h3>\r
                    <p>Backup local physical machine</p>\r
                </div>\r
            </div>\r
        }\r
    </div>\r
    <div\r
        class="item"\r
        [class.selected]="isSourceSelected()('remote')"\r
        [class.expanded]="isSourceExpanded()('remote')"\r
        [class.compact]="isSourceCompact()('remote')"\r
        (click)="selectSource('remote')"\r
    >\r
        @if (isSourceExpanded()('remote')) {\r
            <div class="item-header-row">\r
                <div class="item-icon">\r
                    <fa-icon icon="desktop"></fa-icon>\r
                </div>\r
                <div class="item-content">\r
                    <div class="item-header">\r
                        <h3>Remote</h3>\r
                    </div>\r
                </div>\r
            </div>\r
            <div class="expanded-form">\r
                <div class="form-row">\r
                    <div class="dropdown-select">\r
                        <span class="dropdown-text">Remote Machine</span>\r
                        <div class="dropdown-arrow"></div>\r
                    </div>\r
                </div>\r
                <div class="file-select-table">\r
                    <div class="table-header">\r
                        <div class="header-cell">Select File</div>\r
                        <div class="header-cell"></div>\r
                        <div class="header-cell"></div>\r
                        <div class="header-cell"></div>\r
                    </div>\r
                    <div class="table-row">\r
                        <div class="row-cell">\r
                            <div class="expand-icon"></div>\r
                            Remote Disk 0\r
                        </div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                    </div>\r
                    <div class="table-row">\r
                        <div class="row-cell">\r
                            <div class="expand-icon"></div>\r
                            Remote Disk 1\r
                        </div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                    </div>\r
                    <div class="table-row">\r
                        <div class="row-cell">\r
                            <div class="expand-icon"></div>\r
                            Remote Disk 2\r
                        </div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                    </div>\r
                    <div class="table-row">\r
                        <div class="row-cell">\r
                            <div class="expand-icon"></div>\r
                            Remote Disk 3\r
                        </div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                    </div>\r
                </div>\r
                <div class="form-actions">\r
                    <button class="save-button" (click)="saveSourceConfiguration($event)">\r
                        Save\r
                    </button>\r
                </div>\r
            </div>\r
        }\r
        @else {\r
            <div class="item-icon">\r
                <fa-icon icon="desktop"></fa-icon>\r
            </div>\r
            <div class="item-content">\r
                <div class="item-header">\r
                    <h3>Remote</h3>\r
                    <p>Backup remote physical machine</p>\r
                </div>\r
            </div>\r
        }\r
    </div>\r
    <div\r
        class="item"\r
        [class.selected]="isSourceSelected()('hypervisor')"\r
        [class.expanded]="isSourceExpanded()('hypervisor')"\r
        [class.compact]="isSourceCompact()('hypervisor')"\r
        (click)="selectSource('hypervisor')"\r
    >\r
        @if (isSourceExpanded()('hypervisor')) {\r
            <div class="item-header-row">\r
                <div class="item-icon">\r
                    <fa-icon [icon]="['fac', 'custom-hypervisor']"></fa-icon>\r
                </div>\r
                <div class="item-content">\r
                    <div class="item-header">\r
                        <h3>Hypervisor VM</h3>\r
                    </div>\r
                </div>\r
            </div>\r
            <div class="expanded-form">\r
                <div class="form-row">\r
                    <div class="dropdown-select">\r
                        <span class="dropdown-text">Hypervisor</span>\r
                        <div class="dropdown-arrow"></div>\r
                    </div>\r
                </div>\r
                <div class="file-select-table">\r
                    <div class="table-header">\r
                        <div class="header-cell">Select File</div>\r
                        <div class="header-cell"></div>\r
                        <div class="header-cell"></div>\r
                        <div class="header-cell"></div>\r
                    </div>\r
                    <div class="table-row">\r
                        <div class="row-cell">\r
                            <div class="expand-icon"></div>\r
                            VM-WebServer-01\r
                        </div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                    </div>\r
                    <div class="table-row">\r
                        <div class="row-cell">\r
                            <div class="expand-icon"></div>\r
                            VM-Database-01\r
                        </div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                    </div>\r
                    <div class="table-row">\r
                        <div class="row-cell">\r
                            <div class="expand-icon"></div>\r
                            VM-App-Server\r
                        </div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                    </div>\r
                    <div class="table-row">\r
                        <div class="row-cell">\r
                            <div class="expand-icon"></div>\r
                            VM-Test-Server\r
                        </div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                        <div class="row-cell"></div>\r
                    </div>\r
                </div>\r
                <div class="form-actions">\r
                    <button class="save-button" (click)="saveSourceConfiguration($event)">\r
                        Save\r
                    </button>\r
                </div>\r
            </div>\r
        }\r
        @else {\r
            <div class="item-icon">\r
                <fa-icon [icon]="['fac', 'custom-hypervisor']"></fa-icon>\r
            </div>\r
            <div class="item-content">\r
                <div class="item-header">\r
                    <h3>Hypervisor VM</h3>\r
                    <p>Backup Hypervisor VM</p>\r
                </div>\r
            </div>\r
        }\r
    </div>\r
</section>\r
`, styles: ['/* renderer/src/app/backup/shared/backup-source/backup-source.component.less */\n:host {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 1rem;\n  height: 100%;\n  width: 5rem;\n  border-radius: 0.75rem;\n  opacity: 0;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n}\n:host header {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-bottom: 1rem;\n}\n:host header h2 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0;\n  line-height: 1.125rem;\n  color: #ffffff;\n}\n:host section {\n  display: grid;\n  grid-template-columns: 1fr;\n  grid-template-rows: repeat(3, 1fr);\n  gap: 1rem;\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n  grid-auto-rows: min-content;\n  min-height: 0;\n}\n:host section::-webkit-scrollbar {\n  display: none;\n}\n:host section .item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  background: rgba(255, 255, 255, 0);\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  cursor: pointer;\n  transition: background 0.25s ease, border-color 0.25s ease;\n  flex-shrink: 0;\n}\n:host section .item:hover {\n  background: rgba(255, 255, 255, 0.05);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n:host section .item.selected {\n  border-color: rgba(16, 185, 129, 0.6);\n  background: rgba(16, 185, 129, 0.08);\n  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n}\n:host section .item.selected.expanded {\n  flex-direction: column;\n  align-items: flex-start;\n  height: 100%;\n  min-height: 200px;\n  padding: 10px 24px 24px 10px;\n  transform: none;\n  display: flex;\n  flex: 1;\n  grid-column: 1 / span 2;\n  grid-row: 1;\n}\n:host section .item.selected.expanded .item-header-row {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  width: 100%;\n  padding: 10px 0;\n}\n:host section .item.selected.expanded .item-header-row .item-icon {\n  width: 40px;\n  height: 40px;\n}\n:host section .item.selected.expanded .item-header-row .item-content {\n  padding: 0;\n  gap: 0;\n}\n:host section .item.selected.expanded .item-header-row .item-content h3 {\n  font-size: 16px;\n  font-weight: 500;\n  margin: 0;\n  line-height: 1.25;\n}\n:host section .item.selected.expanded .expanded-form {\n  width: 100%;\n  padding-left: 40px;\n}\n:host section .item.selected.expanded .expanded-form .form-row {\n  margin-bottom: 16px;\n}\n:host section .item.selected.expanded .expanded-form .form-row .dropdown-select {\n  background: rgba(255, 255, 255, 0.2);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n  padding: 5px 10px;\n  color: #ffffff;\n  font-size: 12px;\n  line-height: 1.67;\n  width: 223px;\n  height: 29px;\n  box-sizing: border-box;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  cursor: pointer;\n}\n:host section .item.selected.expanded .expanded-form .form-row .dropdown-select .dropdown-text {\n  color: #ffffff;\n  font-weight: 500;\n}\n:host section .item.selected.expanded .expanded-form .form-row .dropdown-select .dropdown-arrow {\n  width: 10px;\n  height: 9px;\n  background: #D9D9D9;\n  clip-path: polygon(50% 70%, 0% 0%, 100% 0%);\n}\n:host section .item.selected.expanded .expanded-form .file-select-table {\n  background: rgba(16, 185, 129, 0.2);\n  border: 1px solid rgba(16, 185, 129, 0.5);\n  border-radius: 8px;\n  padding: 0;\n  width: 100%;\n  min-height: 150px;\n  overflow: hidden;\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-header {\n  background: rgba(255, 255, 255, 0.05);\n  padding: 0;\n  display: flex;\n  border-bottom: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-header .header-cell {\n  padding: 0 0 0 10px;\n  font-size: 12px;\n  line-height: 1.36;\n  color: #ffffff;\n  text-align: left;\n  height: 31px;\n  display: flex;\n  align-items: center;\n  font-weight: 400;\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-header .header-cell:first-child {\n  width: 175px;\n  justify-content: flex-start;\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-header .header-cell:nth-child(2) {\n  width: 175px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-header .header-cell:nth-child(3) {\n  width: 138px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-header .header-cell:nth-child(4) {\n  width: 135px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-header .header-cell:last-child {\n  flex: 1;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-row {\n  display: flex;\n  border-bottom: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-row:last-child {\n  border-bottom: none;\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-row .row-cell {\n  padding: 0 0 0 10px;\n  font-size: 12px;\n  line-height: 1.36;\n  color: #ffffff;\n  height: 31px;\n  display: flex;\n  align-items: center;\n  font-weight: 400;\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-row .row-cell:first-child {\n  width: 177px;\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-row .row-cell:first-child .expand-icon {\n  width: 15px;\n  height: 15px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 3px;\n  margin-right: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  flex-shrink: 0;\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-row .row-cell:first-child .expand-icon::after {\n  content: "";\n  width: 12px;\n  height: 12px;\n  background: #D9D9D9;\n  border-radius: 2px;\n  clip-path: polygon(50% 20%, 20% 80%, 80% 80%);\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-row .row-cell:nth-child(2) {\n  width: 175px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-row .row-cell:nth-child(3) {\n  width: 140px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section .item.selected.expanded .expanded-form .file-select-table .table-row .row-cell:last-child {\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section .item.selected.expanded .expanded-form .form-actions {\n  display: flex;\n  justify-content: flex-end;\n  padding-top: 16px;\n}\n:host section .item.selected.expanded .expanded-form .form-actions .save-button {\n  background:\n    linear-gradient(\n      90deg,\n      #ef4e38 0%,\n      #b61e0e 100%);\n  border: none;\n  border-radius: 8px;\n  color: #ffffff;\n  font-family: "Noto Sans", sans-serif;\n  font-size: 14px;\n  font-weight: 400;\n  line-height: 1.36;\n  text-align: center;\n  padding: 8px 12px;\n  width: 194px;\n  height: 34px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-sizing: border-box;\n}\n:host section .item.selected.expanded .expanded-form .form-actions .save-button:hover {\n  box-shadow: 0 4px 8px rgba(239, 78, 56, 0.3);\n}\n:host section .item.selected.expanded .expanded-form .form-actions .save-button:active {\n  box-shadow: 0 2px 4px rgba(239, 78, 56, 0.3);\n}\n:host section .item.selected.expanded .expanded-form .form-actions .save-button:focus {\n  outline: 2px solid rgba(239, 78, 56, 0.5);\n  outline-offset: 2px;\n}\n:host section .item .item-icon {\n  font-size: 3rem;\n  padding: 1rem;\n}\n:host section .item .item-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  gap: 0.5em;\n}\n:host section .item .item-content .item-header h3 {\n  font-size: 1rem;\n  font-weight: 500;\n  margin: 0;\n  line-height: 1.375;\n  color: #ffffff;\n}\n:host section .item .item-content .item-header p {\n  font-size: 0.875rem;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n  line-height: 1.375;\n  opacity: 0.8;\n  margin-top: 0.25rem;\n}\n:host section .item.compact {\n  height: 74px;\n}\n:host section .item.compact .item-icon {\n  width: 52px;\n  height: 52px;\n}\n:host section .item.compact .item-content {\n  padding: 8px 0;\n  gap: 4px;\n}\n:host section .item.compact .item-content .item-header h3 {\n  font-size: 16px;\n  font-weight: 500;\n  line-height: 1.36;\n}\n:host section .item.compact .item-content .item-header p {\n  font-size: 13px;\n  line-height: 1.36;\n  margin-top: 0;\n}\n:host.collapsed header {\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  padding: 0;\n  height: 100%;\n  flex-direction: row-reverse;\n  justify-content: flex-end;\n}\n:host.collapsed header h2 {\n  font-size: 1rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-align: center;\n  margin: 0;\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  color: #ffffff;\n  letter-spacing: -0.02em;\n  transform: rotate(180deg);\n}\n:host.collapsed header .icon-container {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n:host.collapsed section {\n  display: none;\n}\n/*# sourceMappingURL=backup-source.component.css.map */\n'] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(BackupSourceComponent, { className: "BackupSourceComponent", filePath: "renderer/src/app/backup/shared/backup-source/backup-source.component.ts", lineNumber: 10 });
})();

// renderer/src/app/backup/shared/backup-destination/backup-destination.component.ts
var _c02 = () => ["fab", "microsoft"];
var _c1 = () => ["fab", "aws"];
function BackupDestinationComponent_Conditional_5_Conditional_28_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 13)(1, "div", 14)(2, "div", 15);
    \u0275\u0275element(3, "fa-icon", 6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "h3");
    \u0275\u0275text(5, "Local");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 16)(7, "div", 17)(8, "div", 18)(9, "div", 19);
    \u0275\u0275text(10, "Select File");
    \u0275\u0275elementEnd();
    \u0275\u0275element(11, "div", 19)(12, "div", 19);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "div", 20)(14, "div", 21);
    \u0275\u0275element(15, "div", 22);
    \u0275\u0275text(16, " C: ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(17, "div", 21)(18, "div", 21);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "div", 20)(20, "div", 21);
    \u0275\u0275element(21, "div", 22);
    \u0275\u0275text(22, " D: ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(23, "div", 21)(24, "div", 21);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "div", 20)(26, "div", 21);
    \u0275\u0275element(27, "div", 22);
    \u0275\u0275text(28, " E: ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(29, "div", 21)(30, "div", 21);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(31, "div", 20)(32, "div", 21);
    \u0275\u0275element(33, "div", 22);
    \u0275\u0275text(34, " F: ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(35, "div", 21)(36, "div", 21);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(37, "div", 23)(38, "button", 24);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_5_Conditional_28_Template_button_click_38_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.saveDestinationConfiguration($event));
    });
    \u0275\u0275text(39, " Save ");
    \u0275\u0275elementEnd()()();
  }
}
function BackupDestinationComponent_Conditional_5_Conditional_29_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 13)(1, "div", 14)(2, "div", 15);
    \u0275\u0275element(3, "fa-icon", 25);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "h3");
    \u0275\u0275text(5, "Network Location");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(6, "div", 16);
    \u0275\u0275elementStart(7, "div", 23)(8, "button", 24);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_5_Conditional_29_Template_button_click_8_listener($event) {
      \u0275\u0275restoreView(_r4);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.saveDestinationConfiguration($event));
    });
    \u0275\u0275text(9, " Save ");
    \u0275\u0275elementEnd()()();
  }
}
function BackupDestinationComponent_Conditional_5_Conditional_30_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 13)(1, "div", 14)(2, "div", 15);
    \u0275\u0275element(3, "fa-icon", 8);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "h3");
    \u0275\u0275text(5, "Actiphy Storage Server");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(6, "div", 16);
    \u0275\u0275elementStart(7, "div", 23)(8, "button", 24);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_5_Conditional_30_Template_button_click_8_listener($event) {
      \u0275\u0275restoreView(_r5);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.saveDestinationConfiguration($event));
    });
    \u0275\u0275text(9, " Save ");
    \u0275\u0275elementEnd()()();
  }
}
function BackupDestinationComponent_Conditional_5_Conditional_31_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 13)(1, "div", 14)(2, "div", 15);
    \u0275\u0275element(3, "fa-icon", 9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "h3");
    \u0275\u0275text(5, "Actiphy Backup Cloud Service");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(6, "div", 16);
    \u0275\u0275elementStart(7, "div", 23)(8, "button", 24);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_5_Conditional_31_Template_button_click_8_listener($event) {
      \u0275\u0275restoreView(_r6);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.saveDestinationConfiguration($event));
    });
    \u0275\u0275text(9, " Save ");
    \u0275\u0275elementEnd()()();
  }
}
function BackupDestinationComponent_Conditional_5_Conditional_32_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 13)(1, "div", 14)(2, "div", 15);
    \u0275\u0275element(3, "fa-icon", 10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "h3");
    \u0275\u0275text(5, "Azure Blob");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(6, "div", 16);
    \u0275\u0275elementStart(7, "div", 23)(8, "button", 24);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_5_Conditional_32_Template_button_click_8_listener($event) {
      \u0275\u0275restoreView(_r7);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.saveDestinationConfiguration($event));
    });
    \u0275\u0275text(9, " Save ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    \u0275\u0275advance(3);
    \u0275\u0275property("icon", \u0275\u0275pureFunction0(1, _c02));
  }
}
function BackupDestinationComponent_Conditional_5_Conditional_33_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 13)(1, "div", 14)(2, "div", 15);
    \u0275\u0275element(3, "fa-icon", 26);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "h3");
    \u0275\u0275text(5, "Amazon S3");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(6, "div", 16);
    \u0275\u0275elementStart(7, "div", 23)(8, "button", 24);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_5_Conditional_33_Template_button_click_8_listener($event) {
      \u0275\u0275restoreView(_r8);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.saveDestinationConfiguration($event));
    });
    \u0275\u0275text(9, " Save ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    \u0275\u0275advance(3);
    \u0275\u0275property("icon", \u0275\u0275pureFunction0(1, _c1));
  }
}
function BackupDestinationComponent_Conditional_5_Conditional_34_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 13)(1, "div", 14)(2, "div", 15);
    \u0275\u0275element(3, "fa-icon", 11);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "h3");
    \u0275\u0275text(5, "Tape Backup");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(6, "div", 16);
    \u0275\u0275elementStart(7, "div", 23)(8, "button", 24);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_5_Conditional_34_Template_button_click_8_listener($event) {
      \u0275\u0275restoreView(_r9);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.saveDestinationConfiguration($event));
    });
    \u0275\u0275text(9, " Save ");
    \u0275\u0275elementEnd()()();
  }
}
function BackupDestinationComponent_Conditional_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "section", 2)(1, "div", 3)(2, "div", 4);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_5_Template_div_click_2_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.selectDestination("local"));
    });
    \u0275\u0275elementStart(3, "div", 5);
    \u0275\u0275element(4, "fa-icon", 6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 7);
    \u0275\u0275text(6, "Machine");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 4);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_5_Template_div_click_7_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.selectDestination("actiphy"));
    });
    \u0275\u0275elementStart(8, "div", 5);
    \u0275\u0275element(9, "fa-icon", 8);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "div", 7);
    \u0275\u0275text(11, "Actiphy Storage Server");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(12, "div", 4);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_5_Template_div_click_12_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.selectDestination("cloud"));
    });
    \u0275\u0275elementStart(13, "div", 5);
    \u0275\u0275element(14, "fa-icon", 9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "div", 7);
    \u0275\u0275text(16, "Actiphy Backup Cloud Service");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(17, "div", 4);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_5_Template_div_click_17_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.selectDestination("azure"));
    });
    \u0275\u0275elementStart(18, "div", 5);
    \u0275\u0275element(19, "fa-icon", 10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "div", 7);
    \u0275\u0275text(21, "Cloud");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(22, "div", 4);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_5_Template_div_click_22_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.selectDestination("tape"));
    });
    \u0275\u0275elementStart(23, "div", 5);
    \u0275\u0275element(24, "fa-icon", 11);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "div", 7);
    \u0275\u0275text(26, "Tape Backup");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(27, "div", 12);
    \u0275\u0275conditionalCreate(28, BackupDestinationComponent_Conditional_5_Conditional_28_Template, 40, 0, "div", 13);
    \u0275\u0275conditionalCreate(29, BackupDestinationComponent_Conditional_5_Conditional_29_Template, 10, 0, "div", 13);
    \u0275\u0275conditionalCreate(30, BackupDestinationComponent_Conditional_5_Conditional_30_Template, 10, 0, "div", 13);
    \u0275\u0275conditionalCreate(31, BackupDestinationComponent_Conditional_5_Conditional_31_Template, 10, 0, "div", 13);
    \u0275\u0275conditionalCreate(32, BackupDestinationComponent_Conditional_5_Conditional_32_Template, 10, 2, "div", 13);
    \u0275\u0275conditionalCreate(33, BackupDestinationComponent_Conditional_5_Conditional_33_Template, 10, 2, "div", 13);
    \u0275\u0275conditionalCreate(34, BackupDestinationComponent_Conditional_5_Conditional_34_Template, 10, 0, "div", 13);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275classProp("selected", ctx_r1.isDestinationSelected()("local") && ctx_r1.isDestinationExpanded()("local"))("active", ctx_r1.isDestinationExpanded()("local"));
    \u0275\u0275advance(5);
    \u0275\u0275classProp("selected", ctx_r1.isDestinationSelected()("actiphy"));
    \u0275\u0275advance(5);
    \u0275\u0275classProp("selected", ctx_r1.isDestinationSelected()("cloud"));
    \u0275\u0275advance(5);
    \u0275\u0275classProp("selected", ctx_r1.isDestinationSelected()("azure"));
    \u0275\u0275advance(2);
    \u0275\u0275property("icon", \u0275\u0275pureFunction0(20, _c02));
    \u0275\u0275advance(3);
    \u0275\u0275classProp("selected", ctx_r1.isDestinationSelected()("tape"));
    \u0275\u0275advance(6);
    \u0275\u0275conditional(ctx_r1.isDestinationExpanded()("local") ? 28 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r1.isDestinationExpanded()("network") ? 29 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r1.isDestinationExpanded()("actiphy") ? 30 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r1.isDestinationExpanded()("cloud") ? 31 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r1.isDestinationExpanded()("azure") ? 32 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r1.isDestinationExpanded()("amazon") ? 33 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r1.isDestinationExpanded()("tape") ? 34 : -1);
  }
}
function BackupDestinationComponent_Conditional_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "section")(1, "div", 27);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_6_Template_div_click_1_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.selectDestination("local"));
    });
    \u0275\u0275elementStart(2, "div", 5);
    \u0275\u0275element(3, "fa-icon", 28);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "div", 29)(5, "div", 30)(6, "h3");
    \u0275\u0275text(7, "Local");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p");
    \u0275\u0275text(9, "Backup to a local machine");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(10, "div", 27);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_6_Template_div_click_10_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.selectDestination("actiphy"));
    });
    \u0275\u0275elementStart(11, "div", 5);
    \u0275\u0275element(12, "fa-icon", 8);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "div", 29)(14, "div", 30)(15, "h3");
    \u0275\u0275text(16, "Actiphy Storage Server");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "p");
    \u0275\u0275text(18, "Backup to an Actiphy Storage Server");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(19, "div", 27);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_6_Template_div_click_19_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.selectDestination("cloud"));
    });
    \u0275\u0275elementStart(20, "div", 5);
    \u0275\u0275element(21, "fa-icon", 9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(22, "div", 29)(23, "div", 30)(24, "h3");
    \u0275\u0275text(25, "Actiphy Backup Cloud Service");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(26, "p");
    \u0275\u0275text(27, "Backup to Actiphy cloud");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(28, "div", 27);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_6_Template_div_click_28_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.selectDestination("azure"));
    });
    \u0275\u0275elementStart(29, "div", 5);
    \u0275\u0275element(30, "fa-icon", 10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(31, "div", 29)(32, "div", 30)(33, "h3");
    \u0275\u0275text(34, "Azure Blob");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(35, "p");
    \u0275\u0275text(36, "Backup to Azure Blob storage");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(37, "div", 27);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_6_Template_div_click_37_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.selectDestination("amazon"));
    });
    \u0275\u0275elementStart(38, "div", 5);
    \u0275\u0275element(39, "fa-icon", 26);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(40, "div", 29)(41, "div", 30)(42, "h3");
    \u0275\u0275text(43, "Amazon S3");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(44, "p");
    \u0275\u0275text(45, "Backup to Amazon S3");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(46, "div", 27);
    \u0275\u0275listener("click", function BackupDestinationComponent_Conditional_6_Template_div_click_46_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.selectDestination("tape"));
    });
    \u0275\u0275elementStart(47, "div", 5);
    \u0275\u0275element(48, "fa-icon", 11);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(49, "div", 29)(50, "div", 30)(51, "h3");
    \u0275\u0275text(52, "Tape Devices");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(53, "p");
    \u0275\u0275text(54, "Backup to tape storage");
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275classProp("selected", ctx_r1.isDestinationSelected()("local"));
    \u0275\u0275advance(9);
    \u0275\u0275classProp("selected", ctx_r1.isDestinationSelected()("actiphy"));
    \u0275\u0275advance(9);
    \u0275\u0275classProp("selected", ctx_r1.isDestinationSelected()("cloud"));
    \u0275\u0275advance(9);
    \u0275\u0275classProp("selected", ctx_r1.isDestinationSelected()("azure"));
    \u0275\u0275advance(2);
    \u0275\u0275property("icon", \u0275\u0275pureFunction0(14, _c02));
    \u0275\u0275advance(7);
    \u0275\u0275classProp("selected", ctx_r1.isDestinationSelected()("amazon"));
    \u0275\u0275advance(2);
    \u0275\u0275property("icon", \u0275\u0275pureFunction0(15, _c1));
    \u0275\u0275advance(7);
    \u0275\u0275classProp("selected", ctx_r1.isDestinationSelected()("tape"));
  }
}
var BackupDestinationComponent = class _BackupDestinationComponent {
  // Inputs from parent component
  selectedDestination = input(null);
  expandedDestination = input(null);
  isCollapsedVertical = input(false);
  // Outputs to parent component
  destinationSelected = output();
  destinationConfigurationSaved = output();
  // Check if a specific destination is selected
  isDestinationSelected = computed(() => (destinationType) => this.selectedDestination() === destinationType);
  // Check if a specific destination is expanded
  isDestinationExpanded = computed(() => (destinationType) => this.expandedDestination() === destinationType);
  // Handle destination selection
  selectDestination(destinationType) {
    this.destinationSelected.emit(destinationType);
  }
  // Handle save configuration
  saveDestinationConfiguration(event) {
    this.destinationConfigurationSaved.emit(event);
  }
  static \u0275fac = function BackupDestinationComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _BackupDestinationComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _BackupDestinationComponent, selectors: [["app-backup-destination"]], inputs: { selectedDestination: [1, "selectedDestination"], expandedDestination: [1, "expandedDestination"], isCollapsedVertical: [1, "isCollapsedVertical"] }, outputs: { destinationSelected: "destinationSelected", destinationConfigurationSaved: "destinationConfigurationSaved" }, decls: 7, vars: 1, consts: [[1, "icon-container"], ["icon", "location-dot"], [1, "expanded-content"], [1, "left-column"], [1, "item", "single-column", 3, "click"], [1, "item-icon"], ["icon", "desktop"], [1, "item-name"], ["icon", "server"], ["icon", "cloud"], ["icon", "microsoft", 3, "icon"], ["icon", "tape"], [1, "right-column"], [1, "destination-form"], [1, "form-header"], [1, "header-icon"], [1, "form-content"], [1, "file-select-table"], [1, "table-header"], [1, "header-cell"], [1, "table-row"], [1, "row-cell"], [1, "expand-icon"], [1, "form-actions"], [1, "save-button", 3, "click"], ["icon", "network-wired"], ["icon", "aws", 3, "icon"], [1, "item", 3, "click"], ["icon", "laptop"], [1, "item-content"], [1, "item-header"]], template: function BackupDestinationComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "header")(1, "h2");
      \u0275\u0275text(2, "Backup Destination");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "div", 0);
      \u0275\u0275element(4, "fa-icon", 1);
      \u0275\u0275elementEnd()();
      \u0275\u0275conditionalCreate(5, BackupDestinationComponent_Conditional_5_Template, 35, 21, "section", 2)(6, BackupDestinationComponent_Conditional_6_Template, 55, 16, "section");
    }
    if (rf & 2) {
      \u0275\u0275advance(5);
      \u0275\u0275conditional(ctx.expandedDestination() !== null ? 5 : 6);
    }
  }, dependencies: [FontAwesomeModule, FaIconComponent], styles: ['\n\n[_nghost-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 1rem;\n  height: 100%;\n  width: 5rem;\n  border-radius: 0.75rem;\n  opacity: 0;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-bottom: 1rem;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0;\n  line-height: 1.125rem;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-template-rows: repeat(3, 1fr);\n  gap: 1rem;\n  flex: 1;\n  min-height: 0;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  overflow: hidden;\n  padding-top: 0;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  width: 8rem;\n  flex-shrink: 0;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]::-webkit-scrollbar {\n  display: none;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   .item.single-column[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  padding: 1rem;\n  height: 8rem;\n  cursor: pointer;\n  transition: background 0.25s ease, border-color 0.25s ease;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   .item.single-column[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.05);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   .item.single-column.selected[_ngcontent-%COMP%] {\n  border-color: rgba(16, 185, 129, 0.5);\n  background: rgba(16, 185, 129, 0.2);\n  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   .item.single-column.active[_ngcontent-%COMP%] {\n  border-color: rgba(16, 185, 129, 0.5);\n  background: rgba(16, 185, 129, 0.2);\n  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   .item.single-column[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   .item.single-column[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\n  font-family: "Noto Sans", sans-serif;\n  font-size: 1rem;\n  font-weight: 500;\n  line-height: 1.36;\n  color: #ffffff;\n  text-align: center;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  padding: 10px 24px 24px 10px;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 10px 0;\n  margin-bottom: 16px;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-family: "Inter", sans-serif;\n  font-size: 16px;\n  font-weight: 500;\n  line-height: 1.25;\n  color: #ffffff;\n  margin: 0;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%] {\n  flex: 1;\n  padding-left: 40px;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%] {\n  background: rgba(16, 185, 129, 0.2);\n  border: 1px solid rgba(16, 185, 129, 0.5);\n  border-radius: 8px;\n  overflow: hidden;\n  width: 100%;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.05);\n  display: flex;\n  border-bottom: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .header-cell[_ngcontent-%COMP%] {\n  padding: 0 0 0 10px;\n  font-family: "Noto Sans", sans-serif;\n  font-size: 12px;\n  font-weight: 400;\n  line-height: 1.36;\n  color: #ffffff;\n  text-align: left;\n  height: 31px;\n  display: flex;\n  align-items: center;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .header-cell[_ngcontent-%COMP%]:first-child {\n  width: 125px;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .header-cell[_ngcontent-%COMP%]:nth-child(2) {\n  width: 125px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .header-cell[_ngcontent-%COMP%]:nth-child(3) {\n  width: 128px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .header-cell[_ngcontent-%COMP%]:last-child {\n  flex: 1;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%] {\n  display: flex;\n  border-bottom: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%] {\n  padding: 0 0 0 10px;\n  font-family: "Noto Sans", sans-serif;\n  font-size: 12px;\n  font-weight: 400;\n  line-height: 1.36;\n  color: #ffffff;\n  height: 31px;\n  display: flex;\n  align-items: center;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%]:first-child {\n  width: 125px;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%]:first-child   .expand-icon[_ngcontent-%COMP%] {\n  width: 15px;\n  height: 15px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 3px;\n  margin-right: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  flex-shrink: 0;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%]:first-child   .expand-icon[_ngcontent-%COMP%]::after {\n  content: "";\n  width: 12px;\n  height: 12px;\n  background: #D9D9D9;\n  border-radius: 2px;\n  clip-path: polygon(50% 20%, 20% 80%, 80% 80%);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%]:nth-child(2) {\n  width: 125px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%]:nth-child(3) {\n  width: 128px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .file-select-table[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]   .row-cell[_ngcontent-%COMP%]:last-child {\n  flex: 1;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  padding-top: 16px;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      90deg,\n      #ef4e38 0%,\n      #b61e0e 100%);\n  border: none;\n  border-radius: 8px;\n  color: #ffffff;\n  font-family: "Noto Sans", sans-serif;\n  font-size: 14px;\n  font-weight: 400;\n  line-height: 1.36;\n  text-align: center;\n  padding: 8px 12px;\n  width: 194px;\n  height: 34px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-sizing: border-box;\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 4px 8px rgba(239, 78, 56, 0.3);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]:active {\n  box-shadow: 0 2px 4px rgba(239, 78, 56, 0.3);\n}\n[_nghost-%COMP%]   section.expanded-content[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%]   .destination-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]:focus {\n  outline: 2px solid rgba(239, 78, 56, 0.5);\n  outline-offset: 2px;\n}\n[_nghost-%COMP%]   section.has-expanded[_ngcontent-%COMP%] {\n  grid-template-rows: 1fr repeat(auto-fit, min-content);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n  gap: 0.75rem;\n  background: rgba(255, 255, 255, 0);\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  cursor: pointer;\n  transition: background 0.25s ease, border-color 0.25s ease;\n  flex-shrink: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.05);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected[_ngcontent-%COMP%] {\n  border-color: rgba(16, 185, 129, 0.6);\n  background: rgba(16, 185, 129, 0.08);\n  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\n  flex-shrink: 0;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  gap: 0.5em;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-header[_ngcontent-%COMP%] {\n  text-align: center;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  font-weight: 500;\n  margin: 0;\n  line-height: 1.375;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n  line-height: 1.375;\n  opacity: 0.8;\n  margin-top: 0.25rem;\n  display: none;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.compact[_ngcontent-%COMP%] {\n  height: 74px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.compact[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%] {\n  width: 52px;\n  height: 52px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.compact[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\n  padding: 8px 0;\n  gap: 4px;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.compact[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 16px;\n  font-weight: 500;\n  line-height: 1.36;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.compact[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 13px;\n  line-height: 1.36;\n  margin-top: 0;\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  padding: 0;\n  height: 100%;\n  flex-direction: row-reverse;\n  justify-content: flex-end;\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-align: center;\n  margin: 0;\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  color: #ffffff;\n  letter-spacing: -0.02em;\n  transform: rotate(180deg);\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n.collapsed[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  display: none;\n}\n@keyframes _ngcontent-%COMP%_fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n/*# sourceMappingURL=backup-destination.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BackupDestinationComponent, [{
    type: Component,
    args: [{ selector: "app-backup-destination", imports: [FontAwesomeModule], template: `<header>\r
    <h2>Backup Destination</h2>\r
    <div class="icon-container">\r
        <fa-icon icon="location-dot"></fa-icon>\r
    </div>\r
</header>\r
@if (expandedDestination() !== null) {\r
    <section class="expanded-content">\r
        <div class="left-column">\r
            <div\r
                class="item single-column"\r
                [class.selected]="isDestinationSelected()('local') && isDestinationExpanded()('local')"\r
                [class.active]="isDestinationExpanded()('local')"\r
                (click)="selectDestination('local')"\r
            >\r
                <div class="item-icon">\r
                    <fa-icon icon="desktop"></fa-icon>\r
                </div>\r
                <div class="item-name">Machine</div>\r
            </div>\r
            <div\r
                class="item single-column"\r
                [class.selected]="isDestinationSelected()('actiphy')"\r
                (click)="selectDestination('actiphy')"\r
            >\r
                <div class="item-icon">\r
                    <fa-icon icon="server"></fa-icon>\r
                </div>\r
                <div class="item-name">Actiphy Storage Server</div>\r
            </div>\r
            <div\r
                class="item single-column"\r
                [class.selected]="isDestinationSelected()('cloud')"\r
                (click)="selectDestination('cloud')"\r
            >\r
                <div class="item-icon">\r
                    <fa-icon icon="cloud"></fa-icon>\r
                </div>\r
                <div class="item-name">Actiphy Backup Cloud Service</div>\r
            </div>\r
            <div\r
                class="item single-column"\r
                [class.selected]="isDestinationSelected()('azure')"\r
                (click)="selectDestination('azure')"\r
            >\r
                <div class="item-icon">\r
                    <fa-icon icon="microsoft" [icon]="['fab', 'microsoft']"></fa-icon>\r
                </div>\r
                <div class="item-name">Cloud</div>\r
            </div>\r
            <div\r
                class="item single-column"\r
                [class.selected]="isDestinationSelected()('tape')"\r
                (click)="selectDestination('tape')"\r
            >\r
                <div class="item-icon">\r
                    <fa-icon icon="tape"></fa-icon>\r
                </div>\r
                <div class="item-name">Tape Backup</div>\r
            </div>\r
        </div>\r
        <div class="right-column">\r
            @if (isDestinationExpanded()('local')) {\r
                <div class="destination-form">\r
                    <div class="form-header">\r
                        <div class="header-icon">\r
                            <fa-icon icon="desktop"></fa-icon>\r
                        </div>\r
                        <h3>Local</h3>\r
                    </div>\r
                    <div class="form-content">\r
                        <div class="file-select-table">\r
                            <div class="table-header">\r
                                <div class="header-cell">Select File</div>\r
                                <div class="header-cell"></div>\r
                                <div class="header-cell"></div>\r
                            </div>\r
                            <div class="table-row">\r
                                <div class="row-cell">\r
                                    <div class="expand-icon"></div>\r
                                    C:\r
                                </div>\r
                                <div class="row-cell"></div>\r
                                <div class="row-cell"></div>\r
                            </div>\r
                            <div class="table-row">\r
                                <div class="row-cell">\r
                                    <div class="expand-icon"></div>\r
                                    D:\r
                                </div>\r
                                <div class="row-cell"></div>\r
                                <div class="row-cell"></div>\r
                            </div>\r
                            <div class="table-row">\r
                                <div class="row-cell">\r
                                    <div class="expand-icon"></div>\r
                                    E:\r
                                </div>\r
                                <div class="row-cell"></div>\r
                                <div class="row-cell"></div>\r
                            </div>\r
                            <div class="table-row">\r
                                <div class="row-cell">\r
                                    <div class="expand-icon"></div>\r
                                    F:\r
                                </div>\r
                                <div class="row-cell"></div>\r
                                <div class="row-cell"></div>\r
                            </div>\r
                        </div>\r
                    </div>\r
                    <div class="form-actions">\r
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">\r
                            Save\r
                        </button>\r
                    </div>\r
                </div>\r
            }\r
            @if (isDestinationExpanded()('network')) {\r
                <div class="destination-form">\r
                    <div class="form-header">\r
                        <div class="header-icon">\r
                            <fa-icon icon="network-wired"></fa-icon>\r
                        </div>\r
                        <h3>Network Location</h3>\r
                    </div>\r
                    <div class="form-content">\r
                        <!-- Network configuration content will be added here -->\r
                    </div>\r
                    <div class="form-actions">\r
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">\r
                            Save\r
                        </button>\r
                    </div>\r
                </div>\r
            }\r
            @if (isDestinationExpanded()('actiphy')) {\r
                <div class="destination-form">\r
                    <div class="form-header">\r
                        <div class="header-icon">\r
                            <fa-icon icon="server"></fa-icon>\r
                        </div>\r
                        <h3>Actiphy Storage Server</h3>\r
                    </div>\r
                    <div class="form-content">\r
                        <!-- Actiphy storage configuration content will be added here -->\r
                    </div>\r
                    <div class="form-actions">\r
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">\r
                            Save\r
                        </button>\r
                    </div>\r
                </div>\r
            }\r
            @if (isDestinationExpanded()('cloud')) {\r
                <div class="destination-form">\r
                    <div class="form-header">\r
                        <div class="header-icon">\r
                            <fa-icon icon="cloud"></fa-icon>\r
                        </div>\r
                        <h3>Actiphy Backup Cloud Service</h3>\r
                    </div>\r
                    <div class="form-content">\r
                        <!-- Cloud service configuration content will be added here -->\r
                    </div>\r
                    <div class="form-actions">\r
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">\r
                            Save\r
                        </button>\r
                    </div>\r
                </div>\r
            }\r
            @if (isDestinationExpanded()('azure')) {\r
                <div class="destination-form">\r
                    <div class="form-header">\r
                        <div class="header-icon">\r
                            <fa-icon icon="microsoft" [icon]="['fab', 'microsoft']"></fa-icon>\r
                        </div>\r
                        <h3>Azure Blob</h3>\r
                    </div>\r
                    <div class="form-content">\r
                        <!-- Azure Blob configuration content will be added here -->\r
                    </div>\r
                    <div class="form-actions">\r
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">\r
                            Save\r
                        </button>\r
                    </div>\r
                </div>\r
            }\r
            @if (isDestinationExpanded()('amazon')) {\r
                <div class="destination-form">\r
                    <div class="form-header">\r
                        <div class="header-icon">\r
                            <fa-icon icon="aws" [icon]="['fab', 'aws']"></fa-icon>\r
                        </div>\r
                        <h3>Amazon S3</h3>\r
                    </div>\r
                    <div class="form-content">\r
                        <!-- Amazon S3 configuration content will be added here -->\r
                    </div>\r
                    <div class="form-actions">\r
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">\r
                            Save\r
                        </button>\r
                    </div>\r
                </div>\r
            }\r
            @if (isDestinationExpanded()('tape')) {\r
                <div class="destination-form">\r
                    <div class="form-header">\r
                        <div class="header-icon">\r
                            <fa-icon icon="tape"></fa-icon>\r
                        </div>\r
                        <h3>Tape Backup</h3>\r
                    </div>\r
                    <div class="form-content">\r
                        <!-- Tape Backup configuration content will be added here -->\r
                    </div>\r
                    <div class="form-actions">\r
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">\r
                            Save\r
                        </button>\r
                    </div>\r
                </div>\r
            }\r
        </div>\r
    </section>\r
}\r
@else {\r
    <section>\r
        <div\r
            class="item"\r
            [class.selected]="isDestinationSelected()('local')"\r
            (click)="selectDestination('local')"\r
        >\r
            <div class="item-icon">\r
                <fa-icon icon="laptop"></fa-icon>\r
            </div>\r
            <div class="item-content">\r
                <div class="item-header">\r
                    <h3>Local</h3>\r
                    <p>Backup to a local machine</p>\r
                </div>\r
            </div>\r
        </div>\r
        <div\r
            class="item"\r
            [class.selected]="isDestinationSelected()('actiphy')"\r
            (click)="selectDestination('actiphy')"\r
        >\r
            <div class="item-icon">\r
                <fa-icon icon="server"></fa-icon>\r
            </div>\r
            <div class="item-content">\r
                <div class="item-header">\r
                    <h3>Actiphy Storage Server</h3>\r
                    <p>Backup to an Actiphy Storage Server</p>\r
                </div>\r
            </div>\r
        </div>\r
        <div\r
            class="item"\r
            [class.selected]="isDestinationSelected()('cloud')"\r
            (click)="selectDestination('cloud')"\r
        >\r
            <div class="item-icon">\r
                <fa-icon icon="cloud"></fa-icon>\r
            </div>\r
            <div class="item-content">\r
                <div class="item-header">\r
                    <h3>Actiphy Backup Cloud Service</h3>\r
                    <p>Backup to Actiphy cloud</p>\r
                </div>\r
            </div>\r
        </div>\r
        <div\r
            class="item"\r
            [class.selected]="isDestinationSelected()('azure')"\r
            (click)="selectDestination('azure')"\r
        >\r
            <div class="item-icon">\r
                <fa-icon icon="microsoft" [icon]="['fab', 'microsoft']"></fa-icon>\r
            </div>\r
            <div class="item-content">\r
                <div class="item-header">\r
                    <h3>Azure Blob</h3>\r
                    <p>Backup to Azure Blob storage</p>\r
                </div>\r
            </div>\r
        </div>\r
        <div\r
            class="item"\r
            [class.selected]="isDestinationSelected()('amazon')"\r
            (click)="selectDestination('amazon')"\r
        >\r
            <div class="item-icon">\r
                <fa-icon icon="aws" [icon]="['fab', 'aws']"></fa-icon>\r
            </div>\r
            <div class="item-content">\r
                <div class="item-header">\r
                    <h3>Amazon S3</h3>\r
                    <p>Backup to Amazon S3</p>\r
                </div>\r
            </div>\r
        </div>\r
        <div\r
            class="item"\r
            [class.selected]="isDestinationSelected()('tape')"\r
            (click)="selectDestination('tape')"\r
        >\r
            <div class="item-icon">\r
                <fa-icon icon="tape"></fa-icon>\r
            </div>\r
            <div class="item-content">\r
                <div class="item-header">\r
                    <h3>Tape Devices</h3>\r
                    <p>Backup to tape storage</p>\r
                </div>\r
            </div>\r
        </div>\r
    </section>\r
}\r
`, styles: ['/* renderer/src/app/backup/shared/backup-destination/backup-destination.component.less */\n:host {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 1rem;\n  height: 100%;\n  width: 5rem;\n  border-radius: 0.75rem;\n  opacity: 0;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n}\n:host header {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-bottom: 1rem;\n}\n:host header h2 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0;\n  line-height: 1.125rem;\n  color: #ffffff;\n}\n:host header .icon-container {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n:host section {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-template-rows: repeat(3, 1fr);\n  gap: 1rem;\n  flex: 1;\n  min-height: 0;\n}\n:host section.expanded-content {\n  display: flex;\n  gap: 1rem;\n  overflow: hidden;\n  padding-top: 0;\n}\n:host section.expanded-content .left-column {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  width: 8rem;\n  flex-shrink: 0;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n:host section.expanded-content .left-column::-webkit-scrollbar {\n  display: none;\n}\n:host section.expanded-content .left-column .item.single-column {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  padding: 1rem;\n  height: 8rem;\n  cursor: pointer;\n  transition: background 0.25s ease, border-color 0.25s ease;\n}\n:host section.expanded-content .left-column .item.single-column:hover {\n  background: rgba(255, 255, 255, 0.05);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n:host section.expanded-content .left-column .item.single-column.selected {\n  border-color: rgba(16, 185, 129, 0.5);\n  background: rgba(16, 185, 129, 0.2);\n  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n}\n:host section.expanded-content .left-column .item.single-column.active {\n  border-color: rgba(16, 185, 129, 0.5);\n  background: rgba(16, 185, 129, 0.2);\n  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n}\n:host section.expanded-content .left-column .item.single-column .item-icon {\n  font-size: 3rem;\n}\n:host section.expanded-content .left-column .item.single-column .item-name {\n  font-family: "Noto Sans", sans-serif;\n  font-size: 1rem;\n  font-weight: 500;\n  line-height: 1.36;\n  color: #ffffff;\n  text-align: center;\n}\n:host section.expanded-content .right-column {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n:host section.expanded-content .right-column .destination-form {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 8px;\n  padding: 10px 24px 24px 10px;\n}\n:host section.expanded-content .right-column .destination-form .form-header {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 10px 0;\n  margin-bottom: 16px;\n}\n:host section.expanded-content .right-column .destination-form .form-header .header-icon {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0);\n}\n:host section.expanded-content .right-column .destination-form .form-header h3 {\n  font-family: "Inter", sans-serif;\n  font-size: 16px;\n  font-weight: 500;\n  line-height: 1.25;\n  color: #ffffff;\n  margin: 0;\n}\n:host section.expanded-content .right-column .destination-form .form-content {\n  flex: 1;\n  padding-left: 40px;\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table {\n  background: rgba(16, 185, 129, 0.2);\n  border: 1px solid rgba(16, 185, 129, 0.5);\n  border-radius: 8px;\n  overflow: hidden;\n  width: 100%;\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-header {\n  background: rgba(255, 255, 255, 0.05);\n  display: flex;\n  border-bottom: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-header .header-cell {\n  padding: 0 0 0 10px;\n  font-family: "Noto Sans", sans-serif;\n  font-size: 12px;\n  font-weight: 400;\n  line-height: 1.36;\n  color: #ffffff;\n  text-align: left;\n  height: 31px;\n  display: flex;\n  align-items: center;\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-header .header-cell:first-child {\n  width: 125px;\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-header .header-cell:nth-child(2) {\n  width: 125px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-header .header-cell:nth-child(3) {\n  width: 128px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-header .header-cell:last-child {\n  flex: 1;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-row {\n  display: flex;\n  border-bottom: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-row:last-child {\n  border-bottom: none;\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-row .row-cell {\n  padding: 0 0 0 10px;\n  font-family: "Noto Sans", sans-serif;\n  font-size: 12px;\n  font-weight: 400;\n  line-height: 1.36;\n  color: #ffffff;\n  height: 31px;\n  display: flex;\n  align-items: center;\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-row .row-cell:first-child {\n  width: 125px;\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-row .row-cell:first-child .expand-icon {\n  width: 15px;\n  height: 15px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 3px;\n  margin-right: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  flex-shrink: 0;\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-row .row-cell:first-child .expand-icon::after {\n  content: "";\n  width: 12px;\n  height: 12px;\n  background: #D9D9D9;\n  border-radius: 2px;\n  clip-path: polygon(50% 20%, 20% 80%, 80% 80%);\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-row .row-cell:nth-child(2) {\n  width: 125px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-row .row-cell:nth-child(3) {\n  width: 128px;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section.expanded-content .right-column .destination-form .form-content .file-select-table .table-row .row-cell:last-child {\n  flex: 1;\n  border-left: 1px solid rgba(16, 185, 129, 0.5);\n}\n:host section.expanded-content .right-column .destination-form .form-actions {\n  display: flex;\n  justify-content: flex-end;\n  padding-top: 16px;\n}\n:host section.expanded-content .right-column .destination-form .form-actions .save-button {\n  background:\n    linear-gradient(\n      90deg,\n      #ef4e38 0%,\n      #b61e0e 100%);\n  border: none;\n  border-radius: 8px;\n  color: #ffffff;\n  font-family: "Noto Sans", sans-serif;\n  font-size: 14px;\n  font-weight: 400;\n  line-height: 1.36;\n  text-align: center;\n  padding: 8px 12px;\n  width: 194px;\n  height: 34px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-sizing: border-box;\n}\n:host section.expanded-content .right-column .destination-form .form-actions .save-button:hover {\n  box-shadow: 0 4px 8px rgba(239, 78, 56, 0.3);\n}\n:host section.expanded-content .right-column .destination-form .form-actions .save-button:active {\n  box-shadow: 0 2px 4px rgba(239, 78, 56, 0.3);\n}\n:host section.expanded-content .right-column .destination-form .form-actions .save-button:focus {\n  outline: 2px solid rgba(239, 78, 56, 0.5);\n  outline-offset: 2px;\n}\n:host section.has-expanded {\n  grid-template-rows: 1fr repeat(auto-fit, min-content);\n}\n:host section .item {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n  gap: 0.75rem;\n  background: rgba(255, 255, 255, 0);\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  cursor: pointer;\n  transition: background 0.25s ease, border-color 0.25s ease;\n  flex-shrink: 0;\n}\n:host section .item:hover {\n  background: rgba(255, 255, 255, 0.05);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n:host section .item.selected {\n  border-color: rgba(16, 185, 129, 0.6);\n  background: rgba(16, 185, 129, 0.08);\n  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n}\n:host section .item .item-icon {\n  font-size: 3rem;\n}\n:host section .item .item-content {\n  flex-shrink: 0;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  gap: 0.5em;\n}\n:host section .item .item-content .item-header {\n  text-align: center;\n}\n:host section .item .item-content .item-header h3 {\n  font-size: 1rem;\n  font-weight: 500;\n  margin: 0;\n  line-height: 1.375;\n  color: #ffffff;\n}\n:host section .item .item-content .item-header p {\n  font-size: 0.875rem;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n  line-height: 1.375;\n  opacity: 0.8;\n  margin-top: 0.25rem;\n  display: none;\n}\n:host section .item.compact {\n  height: 74px;\n}\n:host section .item.compact .item-icon {\n  width: 52px;\n  height: 52px;\n}\n:host section .item.compact .item-content {\n  padding: 8px 0;\n  gap: 4px;\n}\n:host section .item.compact .item-content .item-header h3 {\n  font-size: 16px;\n  font-weight: 500;\n  line-height: 1.36;\n}\n:host section .item.compact .item-content .item-header p {\n  font-size: 13px;\n  line-height: 1.36;\n  margin-top: 0;\n}\n:host.collapsed header {\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  padding: 0;\n  height: 100%;\n  flex-direction: row-reverse;\n  justify-content: flex-end;\n}\n:host.collapsed header h2 {\n  font-size: 1rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-align: center;\n  margin: 0;\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  color: #ffffff;\n  letter-spacing: -0.02em;\n  transform: rotate(180deg);\n}\n:host.collapsed header .icon-container {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n:host.collapsed section {\n  display: none;\n}\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n/*# sourceMappingURL=backup-destination.component.css.map */\n'] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(BackupDestinationComponent, { className: "BackupDestinationComponent", filePath: "renderer/src/app/backup/shared/backup-destination/backup-destination.component.ts", lineNumber: 10 });
})();

// renderer/src/app/backup/shared/backup-schedule/backup-schedule.component.ts
var BackupScheduleComponent = class _BackupScheduleComponent {
  // Inputs from parent component
  selectedSchedule = input(null);
  isCollapsedVertical = input(false);
  // Outputs to parent component
  scheduleSelected = output();
  // Check if a specific schedule is selected
  isScheduleSelected = computed(() => (scheduleType) => this.selectedSchedule() === scheduleType);
  // Handle schedule selection
  selectSchedule(scheduleType) {
    this.scheduleSelected.emit(scheduleType);
  }
  static \u0275fac = function BackupScheduleComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _BackupScheduleComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _BackupScheduleComponent, selectors: [["app-backup-schedule"]], inputs: { selectedSchedule: [1, "selectedSchedule"], isCollapsedVertical: [1, "isCollapsedVertical"] }, outputs: { scheduleSelected: "scheduleSelected" }, decls: 25, vars: 8, consts: [["icon", "clock"], [1, "item", 3, "click"], [1, "icon"], ["icon", "calendar-day"], [1, "name"], ["icon", "calendar-week"], ["icon", "calendar"], ["icon", "calendar-check"]], template: function BackupScheduleComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "header")(1, "h2");
      \u0275\u0275text(2, "Scheduling");
      \u0275\u0275elementEnd();
      \u0275\u0275element(3, "fa-icon", 0);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "section")(5, "div", 1);
      \u0275\u0275listener("click", function BackupScheduleComponent_Template_div_click_5_listener() {
        return ctx.selectSchedule("once");
      });
      \u0275\u0275elementStart(6, "div", 2);
      \u0275\u0275element(7, "fa-icon", 3);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(8, "div", 4);
      \u0275\u0275text(9, " Once ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(10, "div", 1);
      \u0275\u0275listener("click", function BackupScheduleComponent_Template_div_click_10_listener() {
        return ctx.selectSchedule("weekly");
      });
      \u0275\u0275elementStart(11, "div", 2);
      \u0275\u0275element(12, "fa-icon", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "div", 4);
      \u0275\u0275text(14, " Weekly ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(15, "div", 1);
      \u0275\u0275listener("click", function BackupScheduleComponent_Template_div_click_15_listener() {
        return ctx.selectSchedule("monthly");
      });
      \u0275\u0275elementStart(16, "div", 2);
      \u0275\u0275element(17, "fa-icon", 6);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "div", 4);
      \u0275\u0275text(19, " Monthly ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(20, "div", 1);
      \u0275\u0275listener("click", function BackupScheduleComponent_Template_div_click_20_listener() {
        return ctx.selectSchedule("specific");
      });
      \u0275\u0275elementStart(21, "div", 2);
      \u0275\u0275element(22, "fa-icon", 7);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(23, "div", 4);
      \u0275\u0275text(24, " Specific Dates ");
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(5);
      \u0275\u0275classProp("selected", ctx.isScheduleSelected()("once"));
      \u0275\u0275advance(5);
      \u0275\u0275classProp("selected", ctx.isScheduleSelected()("weekly"));
      \u0275\u0275advance(5);
      \u0275\u0275classProp("selected", ctx.isScheduleSelected()("monthly"));
      \u0275\u0275advance(5);
      \u0275\u0275classProp("selected", ctx.isScheduleSelected()("specific"));
    }
  }, dependencies: [FontAwesomeModule, FaIconComponent], styles: ["\n\n[_nghost-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 1rem;\n  height: 100%;\n  width: 5rem;\n  border-radius: 0.75rem;\n  opacity: 0;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-bottom: 1rem;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0;\n  line-height: 1.125rem;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-template-rows: 1fr 1fr;\n  gap: 1rem;\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n  padding-top: 4px;\n  grid-auto-rows: min-content;\n  min-height: 0;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%] {\n  background: #ffffff0d;\n  border-radius: 0.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 0.75rem;\n  gap: 0.75em;\n  justify-content: center;\n  cursor: pointer;\n  transition: background 0.25s ease, border-color 0.25s ease;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.08);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item.selected[_ngcontent-%COMP%] {\n  border-color: rgba(16, 185, 129, 0.6);\n  background: rgba(16, 185, 129, 0.08);\n  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  font-weight: 500;\n  color: #ffffff;\n  text-align: center;\n  line-height: 1.25;\n  margin: 0;\n  white-space: nowrap;\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  padding: 0;\n  height: 100%;\n  flex-direction: row-reverse;\n  justify-content: flex-end;\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-align: center;\n  margin: 0;\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  color: #ffffff;\n  letter-spacing: -0.02em;\n  transform: rotate(180deg);\n}\n.collapsed[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n.collapsed[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  display: none;\n}\n/*# sourceMappingURL=backup-schedule.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BackupScheduleComponent, [{
    type: Component,
    args: [{ selector: "app-backup-schedule", imports: [FontAwesomeModule], template: `<header>\r
    <h2>Scheduling</h2>\r
    <fa-icon icon="clock"></fa-icon>\r
</header>\r
\r
<section>\r
    <div\r
        class="item"\r
        [class.selected]="isScheduleSelected()('once')"\r
        (click)="selectSchedule('once')"\r
    >\r
        <div class="icon">\r
            <fa-icon icon="calendar-day"></fa-icon>\r
        </div>\r
        <div class="name">\r
            Once\r
        </div>\r
    </div>\r
\r
    <div\r
        class="item"\r
        [class.selected]="isScheduleSelected()('weekly')"\r
        (click)="selectSchedule('weekly')"\r
    >\r
        <div class="icon">\r
            <fa-icon icon="calendar-week"></fa-icon>\r
        </div>\r
        <div class="name">\r
            Weekly\r
        </div>\r
    </div>\r
\r
    <div\r
        class="item"\r
        [class.selected]="isScheduleSelected()('monthly')"\r
        (click)="selectSchedule('monthly')"\r
    >\r
        <div class="icon">\r
            <fa-icon icon="calendar"></fa-icon>\r
        </div>\r
        <div class="name">\r
            Monthly\r
        </div>\r
    </div>\r
\r
    <div\r
        class="item"\r
        [class.selected]="isScheduleSelected()('specific')"\r
        (click)="selectSchedule('specific')"\r
    >\r
        <div class="icon">\r
            <fa-icon icon="calendar-check"></fa-icon>\r
        </div>\r
        <div class="name">\r
            Specific Dates\r
        </div>\r
    </div>\r
</section>\r
`, styles: ["/* renderer/src/app/backup/shared/backup-schedule/backup-schedule.component.less */\n:host {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 1rem;\n  height: 100%;\n  width: 5rem;\n  border-radius: 0.75rem;\n  opacity: 0;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n}\n:host header {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-bottom: 1rem;\n}\n:host header h2 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0;\n  line-height: 1.125rem;\n  color: #ffffff;\n}\n:host section {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-template-rows: 1fr 1fr;\n  gap: 1rem;\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n  padding-top: 4px;\n  grid-auto-rows: min-content;\n  min-height: 0;\n}\n:host section .item {\n  background: #ffffff0d;\n  border-radius: 0.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 0.75rem;\n  gap: 0.75em;\n  justify-content: center;\n  cursor: pointer;\n  transition: background 0.25s ease, border-color 0.25s ease;\n}\n:host section .item:hover {\n  background: rgba(255, 255, 255, 0.08);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n:host section .item.selected {\n  border-color: rgba(16, 185, 129, 0.6);\n  background: rgba(16, 185, 129, 0.08);\n  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n}\n:host section .item .icon {\n  font-size: 3rem;\n}\n:host section .item .name {\n  font-size: 1rem;\n  font-weight: 500;\n  color: #ffffff;\n  text-align: center;\n  line-height: 1.25;\n  margin: 0;\n  white-space: nowrap;\n}\n:host.collapsed header {\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  padding: 0;\n  height: 100%;\n  flex-direction: row-reverse;\n  justify-content: flex-end;\n}\n:host.collapsed header h2 {\n  font-size: 1rem;\n  font-weight: 600;\n  line-height: 1.2;\n  text-align: center;\n  margin: 0;\n  writing-mode: vertical-lr;\n  text-orientation: mixed;\n  color: #ffffff;\n  letter-spacing: -0.02em;\n  transform: rotate(180deg);\n}\n:host.collapsed header .icon-container {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0);\n  padding: 0;\n}\n:host.collapsed section {\n  display: none;\n}\n/*# sourceMappingURL=backup-schedule.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(BackupScheduleComponent, { className: "BackupScheduleComponent", filePath: "renderer/src/app/backup/shared/backup-schedule/backup-schedule.component.ts", lineNumber: 10 });
})();

// renderer/src/app/backup/shared/options/options.component.ts
var OptionsComponent = class _OptionsComponent {
  // Track which option is currently selected/expanded
  selectedOption = signal(null);
  // Events for parent component
  taskOptionsSelected = new EventEmitter();
  selectOption(option) {
    const wasSelected = this.selectedOption() === option;
    this.selectedOption.set(wasSelected ? null : option);
    if (option === "taskOptions") {
      this.taskOptionsSelected.emit(!wasSelected);
    }
  }
  isSelected(option) {
    return this.selectedOption() === option;
  }
  static \u0275fac = function OptionsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _OptionsComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _OptionsComponent, selectors: [["app-options"]], outputs: { taskOptionsSelected: "taskOptionsSelected" }, decls: 24, vars: 12, consts: [[1, "item", 3, "click"], ["icon", "gear"], [1, "text"], ["icon", "gears"], ["icon", "hard-drive"], ["icon", "power-off"], ["icon", "arrows-minimize"], ["icon", "copy"]], template: function OptionsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0);
      \u0275\u0275listener("click", function OptionsComponent_Template_div_click_0_listener() {
        return ctx.selectOption("taskOptions");
      });
      \u0275\u0275element(1, "fa-icon", 1);
      \u0275\u0275elementStart(2, "div", 2);
      \u0275\u0275text(3, "Task Options");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(4, "div", 0);
      \u0275\u0275listener("click", function OptionsComponent_Template_div_click_4_listener() {
        return ctx.selectOption("advancedOptions");
      });
      \u0275\u0275element(5, "fa-icon", 3);
      \u0275\u0275elementStart(6, "div", 2);
      \u0275\u0275text(7, "Advanced Options");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "div", 0);
      \u0275\u0275listener("click", function OptionsComponent_Template_div_click_8_listener() {
        return ctx.selectOption("imageVerify");
      });
      \u0275\u0275element(9, "fa-icon", 4);
      \u0275\u0275elementStart(10, "div", 2);
      \u0275\u0275text(11, "Image Verify");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(12, "div", 0);
      \u0275\u0275listener("click", function OptionsComponent_Template_div_click_12_listener() {
        return ctx.selectOption("bootCheck");
      });
      \u0275\u0275element(13, "fa-icon", 5);
      \u0275\u0275elementStart(14, "div", 2);
      \u0275\u0275text(15, "Boot Check");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(16, "div", 0);
      \u0275\u0275listener("click", function OptionsComponent_Template_div_click_16_listener() {
        return ctx.selectOption("consolidate");
      });
      \u0275\u0275element(17, "fa-icon", 6);
      \u0275\u0275elementStart(18, "div", 2);
      \u0275\u0275text(19, "Consolidate");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(20, "div", 0);
      \u0275\u0275listener("click", function OptionsComponent_Template_div_click_20_listener() {
        return ctx.selectOption("replication");
      });
      \u0275\u0275element(21, "fa-icon", 7);
      \u0275\u0275elementStart(22, "div", 2);
      \u0275\u0275text(23, "Replication");
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275classProp("selected", ctx.isSelected("taskOptions"));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("selected", ctx.isSelected("advancedOptions"));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("selected", ctx.isSelected("imageVerify"));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("selected", ctx.isSelected("bootCheck"));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("selected", ctx.isSelected("consolidate"));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("selected", ctx.isSelected("replication"));
    }
  }, dependencies: [FontAwesomeModule, FaIconComponent], styles: ["\n\n[_nghost-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr;\n  grid-template-rows: repeat(6, 1fr);\n  gap: 0.5rem;\n  width: 5rem;\n  height: 100%;\n  opacity: 0;\n  background: transparent;\n}\n[_nghost-%COMP%]   .item[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem;\n  width: 5rem;\n  height: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid transparent;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n[_nghost-%COMP%]   .item[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.15);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n[_nghost-%COMP%]   .item.selected[_ngcontent-%COMP%] {\n  background: rgba(16, 185, 129, 0.2);\n  border-color: rgba(16, 185, 129, 0.6);\n  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n}\n[_nghost-%COMP%]   .item[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%] {\n  font-size: 0.625rem;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  text-align: center;\n  line-height: 1.2;\n}\n/*# sourceMappingURL=options.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OptionsComponent, [{
    type: Component,
    args: [{ selector: "app-options", imports: [FontAwesomeModule], template: `<div class="item"\r
    [class.selected]="isSelected('taskOptions')"\r
    (click)="selectOption('taskOptions')">\r
    <fa-icon icon="gear"></fa-icon>\r
    <div class="text">Task Options</div>\r
</div>\r
\r
<div class="item"\r
    [class.selected]="isSelected('advancedOptions')"\r
    (click)="selectOption('advancedOptions')">\r
    <fa-icon icon="gears"></fa-icon>\r
    <div class="text">Advanced Options</div>\r
</div>\r
\r
<div class="item"\r
    [class.selected]="isSelected('imageVerify')"\r
    (click)="selectOption('imageVerify')">\r
    <fa-icon icon="hard-drive"></fa-icon>\r
    <div class="text">Image Verify</div>\r
</div>\r
\r
<div class="item"\r
    [class.selected]="isSelected('bootCheck')"\r
    (click)="selectOption('bootCheck')">\r
    <fa-icon icon="power-off"></fa-icon>\r
    <div class="text">Boot Check</div>\r
</div>\r
\r
<div class="item"\r
    [class.selected]="isSelected('consolidate')"\r
    (click)="selectOption('consolidate')">\r
    <fa-icon icon="arrows-minimize"></fa-icon>\r
    <div class="text">Consolidate</div>\r
</div>\r
\r
<div class="item"\r
    [class.selected]="isSelected('replication')"\r
    (click)="selectOption('replication')">\r
    <fa-icon icon="copy"></fa-icon>\r
    <div class="text">Replication</div>\r
</div>\r
`, styles: ["/* renderer/src/app/backup/shared/options/options.component.less */\n:host {\n  display: grid;\n  grid-template-columns: 1fr;\n  grid-template-rows: repeat(6, 1fr);\n  gap: 0.5rem;\n  width: 5rem;\n  height: 100%;\n  opacity: 0;\n  background: transparent;\n}\n:host .item {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem;\n  width: 5rem;\n  height: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid transparent;\n  border-radius: 0.75rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n:host .item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n:host .item.selected {\n  background: rgba(16, 185, 129, 0.2);\n  border-color: rgba(16, 185, 129, 0.6);\n  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);\n}\n:host .item .text {\n  font-size: 0.625rem;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.9);\n  text-align: center;\n  line-height: 1.2;\n}\n/*# sourceMappingURL=options.component.css.map */\n"] }]
  }], null, { taskOptionsSelected: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(OptionsComponent, { className: "OptionsComponent", filePath: "renderer/src/app/backup/shared/options/options.component.ts", lineNumber: 10 });
})();

// renderer/src/app/backup/shared/backup-settings/backup-settings.component.ts
var BackupSettingsComponent = class _BackupSettingsComponent {
  settingsSaved = new EventEmitter();
  settingsClosed = new EventEmitter();
  datePipe = new DatePipe("en-US");
  // Generate default task name with current date and time
  generateDefaultTaskName() {
    const now = /* @__PURE__ */ new Date();
    const dateStr = this.datePipe.transform(now, "yyyyMMdd_HHmm");
    return `backup_${dateStr}`;
  }
  // Form data model
  settings = {
    taskName: this.generateDefaultTaskName(),
    compression: false,
    compressionType: "deduplication",
    compressionLevel: 1,
    passwordProtection: false,
    password: "",
    confirmPassword: "",
    encryption: "No Encryption",
    destinationIsolation: false,
    isolationType: "unassign",
    networkInterface: "Select network interface",
    imageComment: "",
    taskEffectiveFrom: "2025/06/05 11:06",
    taskEffectiveTo: "2025/06/05 11:06",
    useEffectiveTo: false,
    runOnShutdown: false,
    shutdownType: "Base and incremental",
    autoRunMissed: false,
    runMissedBase: false,
    enableRetention: false,
    retentionType: "Delete base and incremental",
    retentionCount: 3,
    deleteOlderImages: false,
    enableReconcile: false,
    preserveReconciled: false,
    preserveDays: 15,
    emailNotification: false,
    emailCondition: "When task failed",
    executionPriority: false,
    fullPriority: 2,
    incrementalPriority: 2
  };
  // Handle priority sliders
  onFullPriorityChange(event) {
    const target = event.target;
    this.settings.fullPriority = parseInt(target.value);
  }
  onIncrementalPriorityChange(event) {
    const target = event.target;
    this.settings.incrementalPriority = parseInt(target.value);
  }
  // Get priority label
  getPriorityLabel(priority) {
    const labels = ["Lowest", "Low", "Medium", "High"];
    return labels[priority] || "Medium";
  }
  // Save settings
  saveSettings() {
    this.settingsSaved.emit(this.settings);
    this.closeSettings();
  }
  // Close settings panel
  closeSettings() {
    this.settingsClosed.emit();
  }
  static \u0275fac = function BackupSettingsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _BackupSettingsComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _BackupSettingsComponent, selectors: [["app-backup-settings"]], outputs: { settingsSaved: "settingsSaved", settingsClosed: "settingsClosed" }, decls: 254, vars: 58, consts: [[1, "container"], ["icon", "gear"], [1, "form-group", "inline"], [1, "form-label"], ["type", "text", "name", "taskName", "placeholder", "Enter task name", 1, "form-input", 3, "ngModelChange", "ngModel"], [1, "columns"], [1, "column"], [1, "compression"], [1, "form-group"], ["type", "checkbox", "name", "compression", 1, "form-checkbox", 3, "ngModelChange", "ngModel"], [1, "form-checkbox-label"], [1, "options"], [1, "dedup-compression"], ["type", "radio", "name", "compressionType", "value", "deduplication", 1, "form-radio", 3, "ngModelChange", "ngModel", "disabled"], [1, "form-radio-label"], [1, "form-group", "dedup-slider"], ["type", "range", "name", "compressionLevel", "min", "1", "max", "3", 1, "form-range", 3, "ngModelChange", "ngModel", "disabled"], [1, "form-range-labels"], [1, "label"], [1, "common-compression"], ["type", "radio", "name", "compressionType", "value", "common", 1, "form-radio", 3, "ngModelChange", "ngModel", "disabled"], [1, "form-select-wrapper"], ["name", "commonCompression", 1, "form-select", 3, "disabled"], [1, "password-protection"], ["type", "checkbox", 1, "form-checkbox", 3, "ngModelChange", "ngModel"], ["type", "password", "name", "password", "placeholder", "******", 1, "form-input", 3, "ngModelChange", "ngModel", "disabled"], ["type", "password", "name", "confirmPassword", "placeholder", "******", 1, "form-input", 3, "ngModelChange", "ngModel", "disabled"], ["name", "encryption", 1, "form-select", 3, "ngModelChange", "ngModel", "disabled"], ["value", "No Encryption"], ["value", "AES-256"], [1, "destination-isolation"], ["type", "radio", "name", "isolationType", "value", "unassign", 1, "form-radio", 3, "ngModelChange", "ngModel", "disabled"], ["type", "radio", "name", "isolationType", "value", "offline", 1, "form-radio", 3, "ngModelChange", "ngModel", "disabled"], ["type", "radio", "name", "isolationType", "value", "eject", 1, "form-radio", 3, "ngModelChange", "ngModel", "disabled"], [1, "network-connection"], ["type", "radio", "name", "isolationType", "value", "disable", 1, "form-radio", 3, "ngModelChange", "ngModel", "disabled"], ["name", "networkInterface", 1, "form-select", 3, "ngModelChange", "ngModel", "disabled"], ["value", "Select network interface"], [1, "image-comment"], ["placeholder", "Enter image comment", "rows", "3", 1, "form-textarea", 3, "ngModelChange", "ngModel"], [1, "effective-dates"], [1, "form-row"], [1, "form-input-group"], ["type", "text", 1, "form-input", 3, "ngModelChange", "ngModel"], ["icon", "calendar", 1, "form-input-icon"], ["type", "text", 1, "form-input", 3, "ngModelChange", "ngModel", "disabled"], [1, "execution-settings"], ["name", "shutdownType", 1, "form-select", 3, "ngModelChange", "ngModel", "disabled"], ["value", "Base and incremental"], ["value", "Base only"], ["value", "Incremental only"], ["type", "checkbox", 1, "form-checkbox", 3, "ngModelChange", "ngModel", "disabled"], [1, "retention-policy"], ["name", "retentionType", 1, "form-select", 3, "ngModelChange", "ngModel", "disabled"], ["value", "Delete base and incremental"], ["type", "number", 1, "form-input", 3, "ngModelChange", "ngModel", "disabled"], [1, "reconcile-image"], ["type", "number", 1, "form-input", "always-enable-label", 3, "ngModelChange", "ngModel", "disabled"], [1, "notification"], ["name", "emailCondition", 1, "form-select", 3, "ngModelChange", "ngModel", "disabled"], ["value", "When task failed"], ["value", "When task completed"], ["value", "Always"], [1, "execution-priority"], ["type", "range", "min", "0", "max", "3", 1, "form-range", 3, "ngModelChange", "disabled", "ngModel"], [1, "form-button", 3, "click"]], template: function BackupSettingsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "header")(2, "h2");
      \u0275\u0275text(3, "Backup Settings");
      \u0275\u0275elementEnd();
      \u0275\u0275element(4, "fa-icon", 1);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(5, "section")(6, "div", 2)(7, "label", 3);
      \u0275\u0275text(8, "Task Name");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(9, "input", 4);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_9_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.taskName, $event) || (ctx.settings.taskName = $event);
        return $event;
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(10, "div", 5)(11, "div", 6)(12, "h3");
      \u0275\u0275text(13, "Image Options");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(14, "div", 7)(15, "div", 8)(16, "label", 3)(17, "input", 9);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_17_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.compression, $event) || (ctx.settings.compression = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "span", 10);
      \u0275\u0275text(19, "Compression");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(20, "div", 11)(21, "div", 12)(22, "div", 8)(23, "label", 3)(24, "input", 13);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_24_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.compressionType, $event) || (ctx.settings.compressionType = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(25, "span", 14);
      \u0275\u0275text(26, "Deduplication Compression");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(27, "div", 11)(28, "div", 15)(29, "input", 16);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_29_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.compressionLevel, $event) || (ctx.settings.compressionLevel = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(30, "div", 17)(31, "span", 18);
      \u0275\u0275text(32, "Maximum");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(33, "span", 18);
      \u0275\u0275text(34, "Optimized");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(35, "span", 18);
      \u0275\u0275text(36, "Fast");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(37, "div", 17)(38, "span", 18);
      \u0275\u0275text(39, "Level 1");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(40, "span", 18);
      \u0275\u0275text(41, "Level 2");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(42, "span", 18);
      \u0275\u0275text(43, "Level 3");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(44, "div", 19)(45, "div", 8)(46, "label", 3)(47, "input", 20);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_47_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.compressionType, $event) || (ctx.settings.compressionType = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(48, "span", 14);
      \u0275\u0275text(49, "Common Compression");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(50, "div", 11)(51, "div", 8)(52, "div", 21)(53, "select", 22)(54, "option");
      \u0275\u0275text(55, "Fast");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(56, "option");
      \u0275\u0275text(57, "Optimized");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(58, "option");
      \u0275\u0275text(59, "Maximum");
      \u0275\u0275elementEnd()()()()()()()();
      \u0275\u0275elementStart(60, "div", 23)(61, "div", 8)(62, "label", 3)(63, "input", 24);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_63_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.passwordProtection, $event) || (ctx.settings.passwordProtection = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(64, "span", 10);
      \u0275\u0275text(65, "Password Protection");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(66, "div", 11)(67, "div", 8)(68, "input", 25);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_68_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.password, $event) || (ctx.settings.password = $event);
        return $event;
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(69, "div", 8)(70, "input", 26);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_70_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.confirmPassword, $event) || (ctx.settings.confirmPassword = $event);
        return $event;
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(71, "div", 8)(72, "div", 21)(73, "select", 27);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_select_ngModelChange_73_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.encryption, $event) || (ctx.settings.encryption = $event);
        return $event;
      });
      \u0275\u0275elementStart(74, "option", 28);
      \u0275\u0275text(75, "No Encryption");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(76, "option", 29);
      \u0275\u0275text(77, "AES-256");
      \u0275\u0275elementEnd()()()()()();
      \u0275\u0275elementStart(78, "div", 30)(79, "div", 8)(80, "label", 3)(81, "input", 24);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_81_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.destinationIsolation, $event) || (ctx.settings.destinationIsolation = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(82, "span", 10);
      \u0275\u0275text(83, "Destination Isolation Options (after backup completes)");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(84, "div", 11)(85, "div", 8)(86, "label", 3)(87, "input", 31);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_87_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.isolationType, $event) || (ctx.settings.isolationType = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(88, "span", 14);
      \u0275\u0275text(89, "Un-assign the drive letter from local hard disk");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(90, "div", 8)(91, "label", 3)(92, "input", 32);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_92_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.isolationType, $event) || (ctx.settings.isolationType = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(93, "span", 14);
      \u0275\u0275text(94, "Take the destination local hard disk offline");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(95, "div", 8)(96, "label", 3)(97, "input", 33);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_97_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.isolationType, $event) || (ctx.settings.isolationType = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(98, "span", 14);
      \u0275\u0275text(99, "Eject the destination USB hard disk");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(100, "div", 34)(101, "div", 8)(102, "label", 3)(103, "input", 35);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_103_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.isolationType, $event) || (ctx.settings.isolationType = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(104, "span", 14);
      \u0275\u0275text(105, "Disable destination network connection");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(106, "div", 11)(107, "div", 8)(108, "div", 21)(109, "select", 36);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_select_ngModelChange_109_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.networkInterface, $event) || (ctx.settings.networkInterface = $event);
        return $event;
      });
      \u0275\u0275elementStart(110, "option", 37);
      \u0275\u0275text(111, "Select network interface");
      \u0275\u0275elementEnd()()()()()()()();
      \u0275\u0275elementStart(112, "div", 38)(113, "div", 8)(114, "label", 3);
      \u0275\u0275text(115, "Image Comment");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(116, "textarea", 39);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_textarea_ngModelChange_116_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.imageComment, $event) || (ctx.settings.imageComment = $event);
        return $event;
      });
      \u0275\u0275text(117, "                        ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(118, "div", 6)(119, "h3");
      \u0275\u0275text(120, "Task Execution Options");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(121, "div", 40)(122, "div", 8)(123, "label", 3);
      \u0275\u0275text(124, "Task effective dates:");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(125, "div", 41)(126, "div", 42)(127, "input", 43);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_127_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.taskEffectiveFrom, $event) || (ctx.settings.taskEffectiveFrom = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275element(128, "fa-icon", 44);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(129, "label", 3)(130, "input", 24);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_130_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.useEffectiveTo, $event) || (ctx.settings.useEffectiveTo = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(131, "span", 10);
      \u0275\u0275text(132, "To:");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(133, "div", 42)(134, "input", 45);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_134_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.taskEffectiveTo, $event) || (ctx.settings.taskEffectiveTo = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275element(135, "fa-icon", 44);
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(136, "div", 46)(137, "div", 41)(138, "div", 8)(139, "label", 3)(140, "input", 24);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_140_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.runOnShutdown, $event) || (ctx.settings.runOnShutdown = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(141, "span", 10);
      \u0275\u0275text(142, "Run task on shutdown / reboot");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(143, "div", 8)(144, "div", 21)(145, "select", 47);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_select_ngModelChange_145_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.shutdownType, $event) || (ctx.settings.shutdownType = $event);
        return $event;
      });
      \u0275\u0275elementStart(146, "option", 48);
      \u0275\u0275text(147, "Base and incremental");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(148, "option", 49);
      \u0275\u0275text(149, "Base only");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(150, "option", 50);
      \u0275\u0275text(151, "Incremental only");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(152, "div", 8)(153, "label", 3)(154, "input", 24);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_154_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.autoRunMissed, $event) || (ctx.settings.autoRunMissed = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(155, "span", 10);
      \u0275\u0275text(156, "Auto run missed schedule tasks");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(157, "div", 11)(158, "div", 8)(159, "label", 3)(160, "input", 51);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_160_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.runMissedBase, $event) || (ctx.settings.runMissedBase = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(161, "span", 10);
      \u0275\u0275text(162, "Run missed schedule base backup");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(163, "div", 52)(164, "div", 8)(165, "label", 3)(166, "input", 24);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_166_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.enableRetention, $event) || (ctx.settings.enableRetention = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(167, "span", 10);
      \u0275\u0275text(168, "Enable retention policy");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(169, "div", 11)(170, "div", 41)(171, "div", 8)(172, "div", 21)(173, "select", 53);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_select_ngModelChange_173_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.retentionType, $event) || (ctx.settings.retentionType = $event);
        return $event;
      });
      \u0275\u0275elementStart(174, "option", 54);
      \u0275\u0275text(175, "Delete base and incremental");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(176, "div", 2)(177, "label", 3);
      \u0275\u0275text(178, "Retain");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(179, "input", 55);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_179_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.retentionCount, $event) || (ctx.settings.retentionCount = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(180, "label", 3);
      \u0275\u0275text(181, "image sets");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(182, "div", 8)(183, "label", 3)(184, "input", 51);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_184_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.deleteOlderImages, $event) || (ctx.settings.deleteOlderImages = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(185, "span", 10);
      \u0275\u0275text(186, "Delete older image before new base backup");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(187, "div", 56)(188, "div", 8)(189, "label", 3)(190, "input", 24);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_190_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.enableReconcile, $event) || (ctx.settings.enableReconcile = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(191, "span", 10);
      \u0275\u0275text(192, "Enable reconcile image");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(193, "div", 11)(194, "div", 2)(195, "label", 3)(196, "input", 51);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_196_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.preserveReconciled, $event) || (ctx.settings.preserveReconciled = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(197, "span", 10);
      \u0275\u0275text(198, "Preserve reconciled image for");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(199, "input", 57);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_199_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.preserveDays, $event) || (ctx.settings.preserveDays = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(200, "label", 3);
      \u0275\u0275text(201, "days");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(202, "div", 58)(203, "div", 8)(204, "label", 3)(205, "input", 24);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_205_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.emailNotification, $event) || (ctx.settings.emailNotification = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(206, "span", 10);
      \u0275\u0275text(207, "Send email notification");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(208, "div", 11)(209, "div", 8)(210, "div", 21)(211, "select", 59);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_select_ngModelChange_211_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.emailCondition, $event) || (ctx.settings.emailCondition = $event);
        return $event;
      });
      \u0275\u0275elementStart(212, "option", 60);
      \u0275\u0275text(213, "When task failed");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(214, "option", 61);
      \u0275\u0275text(215, "When task completed");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(216, "option", 62);
      \u0275\u0275text(217, "Always");
      \u0275\u0275elementEnd()()()()()();
      \u0275\u0275elementStart(218, "div", 63)(219, "div", 8)(220, "label", 3)(221, "input", 24);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_221_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.executionPriority, $event) || (ctx.settings.executionPriority = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(222, "span", 10);
      \u0275\u0275text(223, "Execution Priority");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(224, "div", 11)(225, "div", 8)(226, "label", 3);
      \u0275\u0275text(227, "Full (Base)");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(228, "input", 64);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_228_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.fullPriority, $event) || (ctx.settings.fullPriority = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(229, "div", 17)(230, "span", 18);
      \u0275\u0275text(231, "Lowest");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(232, "span", 18);
      \u0275\u0275text(233, "Low");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(234, "span", 18);
      \u0275\u0275text(235, "Medium");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(236, "span", 18);
      \u0275\u0275text(237, "High");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(238, "div", 8)(239, "label", 3);
      \u0275\u0275text(240, "Incremental");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(241, "input", 64);
      \u0275\u0275twoWayListener("ngModelChange", function BackupSettingsComponent_Template_input_ngModelChange_241_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.settings.incrementalPriority, $event) || (ctx.settings.incrementalPriority = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(242, "div", 17)(243, "span", 18);
      \u0275\u0275text(244, "Lowest");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(245, "span", 18);
      \u0275\u0275text(246, "Low");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(247, "span", 18);
      \u0275\u0275text(248, "Medium");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(249, "span", 18);
      \u0275\u0275text(250, "High");
      \u0275\u0275elementEnd()()()()()()()();
      \u0275\u0275elementStart(251, "footer")(252, "button", 65);
      \u0275\u0275listener("click", function BackupSettingsComponent_Template_button_click_252_listener() {
        return ctx.saveSettings();
      });
      \u0275\u0275text(253, " Save ");
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(9);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.taskName);
      \u0275\u0275advance(8);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.compression);
      \u0275\u0275advance(7);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.compressionType);
      \u0275\u0275property("disabled", !ctx.settings.compression);
      \u0275\u0275advance(5);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.compressionLevel);
      \u0275\u0275property("disabled", !ctx.settings.compression || ctx.settings.compressionType !== "deduplication");
      \u0275\u0275advance(18);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.compressionType);
      \u0275\u0275property("disabled", !ctx.settings.compression);
      \u0275\u0275advance(6);
      \u0275\u0275property("disabled", !ctx.settings.compression || ctx.settings.compressionType !== "common");
      \u0275\u0275advance(10);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.passwordProtection);
      \u0275\u0275advance(5);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.password);
      \u0275\u0275property("disabled", !ctx.settings.passwordProtection);
      \u0275\u0275advance(2);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.confirmPassword);
      \u0275\u0275property("disabled", !ctx.settings.passwordProtection);
      \u0275\u0275advance(3);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.encryption);
      \u0275\u0275property("disabled", !ctx.settings.passwordProtection);
      \u0275\u0275advance(8);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.destinationIsolation);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.isolationType);
      \u0275\u0275property("disabled", !ctx.settings.destinationIsolation);
      \u0275\u0275advance(5);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.isolationType);
      \u0275\u0275property("disabled", !ctx.settings.destinationIsolation);
      \u0275\u0275advance(5);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.isolationType);
      \u0275\u0275property("disabled", !ctx.settings.destinationIsolation);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.isolationType);
      \u0275\u0275property("disabled", !ctx.settings.destinationIsolation);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.networkInterface);
      \u0275\u0275property("disabled", ctx.settings.isolationType !== "disable" || !ctx.settings.destinationIsolation);
      \u0275\u0275advance(7);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.imageComment);
      \u0275\u0275advance(11);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.taskEffectiveFrom);
      \u0275\u0275advance(3);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.useEffectiveTo);
      \u0275\u0275advance(4);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.taskEffectiveTo);
      \u0275\u0275property("disabled", !ctx.settings.useEffectiveTo);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.runOnShutdown);
      \u0275\u0275advance(5);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.shutdownType);
      \u0275\u0275property("disabled", !ctx.settings.runOnShutdown);
      \u0275\u0275advance(9);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.autoRunMissed);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.runMissedBase);
      \u0275\u0275property("disabled", !ctx.settings.autoRunMissed);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.enableRetention);
      \u0275\u0275advance(7);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.retentionType);
      \u0275\u0275property("disabled", !ctx.settings.enableRetention);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.retentionCount);
      \u0275\u0275property("disabled", !ctx.settings.enableRetention);
      \u0275\u0275advance(5);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.deleteOlderImages);
      \u0275\u0275property("disabled", !ctx.settings.enableRetention);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.enableReconcile);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.preserveReconciled);
      \u0275\u0275property("disabled", !ctx.settings.enableReconcile);
      \u0275\u0275advance(3);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.preserveDays);
      \u0275\u0275property("disabled", !ctx.settings.enableReconcile || !ctx.settings.preserveReconciled);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.emailNotification);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.emailCondition);
      \u0275\u0275property("disabled", !ctx.settings.emailNotification);
      \u0275\u0275advance(10);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.executionPriority);
      \u0275\u0275advance(7);
      \u0275\u0275property("disabled", !ctx.settings.executionPriority);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.fullPriority);
      \u0275\u0275advance(13);
      \u0275\u0275property("disabled", !ctx.settings.executionPriority);
      \u0275\u0275twoWayProperty("ngModel", ctx.settings.incrementalPriority);
    }
  }, dependencies: [FontAwesomeModule, FaIconComponent, FormsModule, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, NumberValueAccessor, RangeValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, RadioControlValueAccessor, NgControlStatus, NgModel, CommonModule], styles: ["\n\n[_nghost-%COMP%] {\n  flex: 1;\n  height: 100%;\n  width: 5rem;\n  border-radius: 0.75rem;\n  opacity: 1;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%] {\n  padding: 1rem;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   header[_ngcontent-%COMP%] {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-bottom: 1rem;\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0;\n  line-height: 1.125rem;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%] {\n  flex: 1 1 auto;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  align-self: stretch;\n  overflow-y: auto;\n  min-height: 0;\n  container-type: inline-size;\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]::-webkit-scrollbar {\n  display: none;\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1rem;\n  align-self: stretch;\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%] {\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  padding: 1rem;\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-weight: 400;\n  font-size: 1rem;\n  margin-bottom: 1rem;\n  text-transform: uppercase;\n}\n@container (min-width: 56rem) {\n  [_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%] {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .compression[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .password-protection[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .destination-isolation[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .effective-dates[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .execution-settings[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .retention-policy[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .reconcile-image[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .execution-priority[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .compression[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .password-protection[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .destination-isolation[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .effective-dates[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .execution-settings[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .retention-policy[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .reconcile-image[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .execution-priority[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\n  margin-bottom: 0.5rem;\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .compression[_ngcontent-%COMP%]   .options[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .password-protection[_ngcontent-%COMP%]   .options[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .destination-isolation[_ngcontent-%COMP%]   .options[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .effective-dates[_ngcontent-%COMP%]   .options[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .execution-settings[_ngcontent-%COMP%]   .options[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .retention-policy[_ngcontent-%COMP%]   .options[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .reconcile-image[_ngcontent-%COMP%]   .options[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%]   .options[_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .execution-priority[_ngcontent-%COMP%]   .options[_ngcontent-%COMP%] {\n  padding-left: 1.5rem;\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .compression[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .password-protection[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .destination-isolation[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .effective-dates[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .execution-settings[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .retention-policy[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .reconcile-image[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .notification[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .execution-priority[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%] {\n  width: 5rem;\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .dedup-slider[_ngcontent-%COMP%]   .form-range-labels[_ngcontent-%COMP%]:last-child {\n  order: -1;\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   footer[_ngcontent-%COMP%] {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  gap: 1rem;\n  padding-top: 1rem;\n}\n[_nghost-%COMP%]   .container[_ngcontent-%COMP%]   footer[_ngcontent-%COMP%]   .form-button[_ngcontent-%COMP%] {\n  min-width: 12rem;\n}\n/*# sourceMappingURL=backup-settings.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BackupSettingsComponent, [{
    type: Component,
    args: [{ selector: "app-backup-settings", imports: [FontAwesomeModule, FormsModule, CommonModule], template: `<div class="container">\r
    <!-- Panel Header -->\r
    <header>\r
        <h2>Backup Settings</h2>\r
        <fa-icon icon="gear"></fa-icon>\r
    </header>\r
\r
    <!-- Scrolling Content -->\r
    <section>\r
        <!-- Task Name Row -->\r
        <div class="form-group inline">\r
            <label class="form-label">Task Name</label>\r
            <input type="text" class="form-input" name="taskName"\r
                [(ngModel)]="settings.taskName"\r
                placeholder="Enter task name">\r
        </div>\r
\r
        <!-- Settings Layout (2 columns) -->\r
        <div class="columns">\r
            <!-- Left Column - Image Options -->\r
            <div class="column">\r
                <!-- Table Head -->\r
                <h3>Image Options</h3>\r
\r
                <!-- Compression Section -->\r
                <div class="compression">\r
                    <!-- Compression Checkbox -->\r
                    <div class="form-group">\r
                        <label class="form-label">\r
                            <input type="checkbox" name="compression" class="form-checkbox" [(ngModel)]="settings.compression">\r
                            <span class="form-checkbox-label">Compression</span>\r
                        </label>\r
                    </div>\r
\r
                    <div class="options">\r
                        <!-- Deduplication Compression -->\r
                        <div class="dedup-compression">\r
                            <div class="form-group">\r
                                <label class="form-label">\r
                                    <input type="radio" class="form-radio" name="compressionType" value="deduplication"\r
                                        [(ngModel)]="settings.compressionType"\r
                                        [disabled]="!settings.compression">\r
                                    <span class="form-radio-label">Deduplication Compression</span>\r
                                </label>\r
                            </div>\r
                            <div class="options">\r
                                <!-- Compression Level Slider -->\r
                                <div class="form-group dedup-slider">\r
                                    <input type="range" name="compressionLevel" class="form-range" min="1" max="3"\r
                                        [(ngModel)]="settings.compressionLevel"\r
                                        [disabled]="!settings.compression || settings.compressionType !== 'deduplication'">\r
                                    <div class="form-range-labels">\r
                                        <span class="label">Maximum</span>\r
                                        <span class="label">Optimized</span>\r
                                        <span class="label">Fast</span>\r
                                    </div>\r
                                    <div class="form-range-labels">\r
                                        <span class="label">Level 1</span>\r
                                        <span class="label">Level 2</span>\r
                                        <span class="label">Level 3</span>\r
                                    </div>\r
                                </div>\r
                            </div>\r
                        </div>\r
\r
                        <!-- Common Compression -->\r
                        <div class="common-compression">\r
                            <div class="form-group">\r
                                <label class="form-label">\r
                                    <input type="radio" class="form-radio" name="compressionType" value="common"\r
                                            [(ngModel)]="settings.compressionType"\r
                                            [disabled]="!settings.compression">\r
                                    <span class="form-radio-label">Common Compression</span>\r
                                </label>\r
                            </div>\r
\r
                            <div class="options">\r
                                <!-- Compression Type Select -->\r
                                <div class="form-group">\r
                                    <div class="form-select-wrapper">\r
                                        <select class="form-select" name="commonCompression" [disabled]="!settings.compression || settings.compressionType !== 'common'">\r
                                            <option>Fast</option>\r
                                            <option>Optimized</option>\r
                                            <option>Maximum</option>\r
                                        </select>\r
                                    </div>\r
                                </div>\r
                            </div>\r
                        </div>\r
                    </div>\r
                </div>\r
\r
                <!-- Password Protection Section -->\r
                <div class="password-protection">\r
                    <!-- Password Protection Checkbox -->\r
                    <div class="form-group">\r
                        <label class="form-label">\r
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.passwordProtection">\r
                            <span class="form-checkbox-label">Password Protection</span>\r
                        </label>\r
                    </div>\r
\r
                    <div class="options">\r
                        <!-- Password Input -->\r
                        <div class="form-group">\r
                            <input type="password" name="password" class="form-input" placeholder="******"\r
                                    [(ngModel)]="settings.password"\r
                                    [disabled]="!settings.passwordProtection">\r
                        </div>\r
\r
                        <!-- Confirm Password Input -->\r
                        <div class="form-group">\r
                            <input type="password" name="confirmPassword" class="form-input" placeholder="******"\r
                                    [(ngModel)]="settings.confirmPassword"\r
                                    [disabled]="!settings.passwordProtection">\r
                        </div>\r
\r
                        <!-- Encryption Select -->\r
                        <div class="form-group">\r
                            <div class="form-select-wrapper">\r
                                <select class="form-select" name="encryption"\r
                                        [(ngModel)]="settings.encryption"\r
                                        [disabled]="!settings.passwordProtection">\r
                                    <option value="No Encryption">No Encryption</option>\r
                                    <option value="AES-256">AES-256</option>\r
                                </select>\r
                            </div>\r
                        </div>\r
                    </div>\r
                </div>\r
\r
                <!-- Destination Isolation Options -->\r
                <div class="destination-isolation">\r
                    <div class="form-group">\r
                        <label class="form-label">\r
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.destinationIsolation">\r
                            <span class="form-checkbox-label">Destination Isolation Options (after backup completes)</span>\r
                        </label>\r
                    </div>\r
\r
                    <div class="options">\r
                        <!-- Isolation Options -->\r
                        <div class="form-group">\r
                            <label class="form-label">\r
                                <input type="radio" class="form-radio" name="isolationType" value="unassign"\r
                                        [(ngModel)]="settings.isolationType"\r
                                        [disabled]="!settings.destinationIsolation">\r
                                <span class="form-radio-label">Un-assign the drive letter from local hard disk</span>\r
                            </label>\r
                        </div>\r
\r
                        <div class="form-group">\r
                            <label class="form-label">\r
                                <input type="radio" class="form-radio" name="isolationType" value="offline"\r
                                        [(ngModel)]="settings.isolationType"\r
                                        [disabled]="!settings.destinationIsolation">\r
                                <span class="form-radio-label">Take the destination local hard disk offline</span>\r
                            </label>\r
                        </div>\r
\r
                        <div class="form-group">\r
                            <label class="form-label">\r
                                <input type="radio" class="form-radio" name="isolationType" value="eject"\r
                                        [(ngModel)]="settings.isolationType"\r
                                        [disabled]="!settings.destinationIsolation">\r
                                <span class="form-radio-label">Eject the destination USB hard disk</span>\r
                            </label>\r
                        </div>\r
\r
                        <div class="network-connection">\r
                            <div class="form-group">\r
                                <label class="form-label">\r
                                    <input type="radio" class="form-radio" name="isolationType" value="disable"\r
                                            [(ngModel)]="settings.isolationType"\r
                                            [disabled]="!settings.destinationIsolation">\r
                                    <span class="form-radio-label">Disable destination network connection</span>\r
                                </label>\r
                            </div>\r
\r
                            <div class="options">\r
                                <!-- Network Interface Select -->\r
                                <div class="form-group">\r
                                    <div class="form-select-wrapper">\r
                                        <select class="form-select" name="networkInterface"\r
                                                [(ngModel)]="settings.networkInterface"\r
                                                [disabled]="settings.isolationType !== 'disable'  || !settings.destinationIsolation">\r
                                            <option value="Select network interface">Select network interface</option>\r
                                        </select>\r
                                    </div>\r
                                </div>\r
                            </div>\r
                        </div>\r
                    </div>\r
                </div>\r
\r
                <!-- Image Comment -->\r
                <div class="image-comment">\r
                    <div class="form-group">\r
                        <label class="form-label">Image Comment</label>\r
                        <textarea class="form-textarea"\r
                                [(ngModel)]="settings.imageComment"\r
                                placeholder="Enter image comment"\r
                                rows="3">\r
                        </textarea>\r
                    </div>\r
                </div>\r
            </div>\r
\r
            <!-- Right Column - Task Execution Options -->\r
            <div class="column">\r
                <!-- Table Head -->\r
                <h3>Task Execution Options</h3>\r
\r
                <!-- Task Effective Dates Section -->\r
                <div class="effective-dates">\r
                    <div class="form-group">\r
                        <label class="form-label">Task effective dates:</label>\r
                        <div class="form-row">\r
                            <div class="form-input-group">\r
                                <input type="text" class="form-input" [(ngModel)]="settings.taskEffectiveFrom">\r
                                <fa-icon icon="calendar" class="form-input-icon"></fa-icon>\r
                            </div>\r
                            <label class="form-label">\r
                                <input class="form-checkbox" type="checkbox" [(ngModel)]="settings.useEffectiveTo">\r
                                <span class="form-checkbox-label">To:</span>\r
                            </label>\r
                            <div class="form-input-group">\r
                                <input type="text" class="form-input"\r
                                        [(ngModel)]="settings.taskEffectiveTo"\r
                                        [disabled]="!settings.useEffectiveTo">\r
                                <fa-icon icon="calendar" class="form-input-icon"></fa-icon>\r
                            </div>\r
                        </div>\r
                    </div>\r
                </div>\r
\r
                <!-- Task Execution Settings Section -->\r
                <div class="execution-settings">\r
                    <!-- Run on Shutdown -->\r
                    <div class="form-row">\r
                        <div class="form-group">\r
                            <label class="form-label">\r
                                <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.runOnShutdown">\r
                                <span class="form-checkbox-label">Run task on shutdown / reboot</span>\r
                            </label>\r
                        </div>\r
                        <div class="form-group">\r
                            <div class="form-select-wrapper">\r
                                <select class="form-select" name="shutdownType"\r
                                        [(ngModel)]="settings.shutdownType"\r
                                        [disabled]="!settings.runOnShutdown">\r
                                    <option value="Base and incremental">Base and incremental</option>\r
                                    <option value="Base only">Base only</option>\r
                                    <option value="Incremental only">Incremental only</option>\r
                                </select>\r
                            </div>\r
                        </div>\r
                    </div>\r
\r
                    <!-- Auto Run Missed -->\r
                    <div class="form-group">\r
                        <label class="form-label">\r
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.autoRunMissed">\r
                            <span class="form-checkbox-label">Auto run missed schedule tasks</span>\r
                        </label>\r
                    </div>\r
                    <div class="options">\r
                        <div class="form-group">\r
                            <label class="form-label">\r
                                <input type="checkbox" class="form-checkbox"\r
                                        [(ngModel)]="settings.runMissedBase"\r
                                        [disabled]="!settings.autoRunMissed">\r
                                <span class="form-checkbox-label">Run missed schedule base backup</span>\r
                            </label>\r
                        </div>\r
                    </div>\r
                </div>\r
\r
                <!-- Retention Policy Section -->\r
                <div class="retention-policy">\r
                    <div class="form-group">\r
                        <label class="form-label">\r
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.enableRetention">\r
                            <span class="form-checkbox-label">Enable retention policy</span>\r
                        </label>\r
                    </div>\r
                    <div class="options">\r
                        <div class="form-row">\r
                            <div class="form-group">\r
                                <div class="form-select-wrapper">\r
                                    <select class="form-select" name="retentionType"\r
                                            [(ngModel)]="settings.retentionType"\r
                                            [disabled]="!settings.enableRetention">\r
                                        <option value="Delete base and incremental">Delete base and incremental</option>\r
                                    </select>\r
                                </div>\r
                            </div>\r
                            <div class="form-group inline">\r
                                <label class="form-label">Retain</label>\r
                                <input type="number" class="form-input"\r
                                        [(ngModel)]="settings.retentionCount"\r
                                        [disabled]="!settings.enableRetention">\r
                                <label class="form-label">image sets</label>\r
                            </div>\r
                        </div>\r
                        <div class="form-group">\r
                            <label class="form-label">\r
                                <input type="checkbox" class="form-checkbox"\r
                                        [(ngModel)]="settings.deleteOlderImages"\r
                                        [disabled]="!settings.enableRetention">\r
                                <span class="form-checkbox-label">Delete older image before new base backup</span>\r
                            </label>\r
                        </div>\r
                    </div>\r
                </div>\r
\r
                <!-- Reconcile Image Section -->\r
                <div class="reconcile-image">\r
                    <div class="form-group">\r
                        <label class="form-label">\r
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.enableReconcile">\r
                            <span class="form-checkbox-label">Enable reconcile image</span>\r
                        </label>\r
                    </div>\r
\r
                    <div class="options">\r
                        <div class="form-group inline">\r
                            <label class="form-label">\r
                                <input type="checkbox" class="form-checkbox"\r
                                    [(ngModel)]="settings.preserveReconciled"\r
                                    [disabled]="!settings.enableReconcile">\r
                                <span class="form-checkbox-label">Preserve reconciled image for</span>\r
                            </label>\r
                            <input type="number" class="form-input always-enable-label"\r
                                [(ngModel)]="settings.preserveDays"\r
                                [disabled]="!settings.enableReconcile || !settings.preserveReconciled">\r
                            <label class="form-label">days</label>\r
                        </div>\r
\r
                    </div>\r
                </div>\r
\r
                <!-- Email Notification Section -->\r
                <div class="notification">\r
                    <div class="form-group">\r
                        <label class="form-label">\r
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.emailNotification">\r
                            <span class="form-checkbox-label">Send email notification</span>\r
                        </label>\r
                    </div>\r
\r
                    <div class="options">\r
                        <div class="form-group">\r
                            <div class="form-select-wrapper">\r
                                <select class="form-select" name="emailCondition"\r
                                        [(ngModel)]="settings.emailCondition"\r
                                        [disabled]="!settings.emailNotification">\r
                                    <option value="When task failed">When task failed</option>\r
                                    <option value="When task completed">When task completed</option>\r
                                    <option value="Always">Always</option>\r
                                </select>\r
                            </div>\r
                        </div>\r
                    </div>\r
                </div>\r
\r
                <!-- Execution Priority Section -->\r
                <div class="execution-priority">\r
                    <div class="form-group">\r
                        <label class="form-label">\r
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.executionPriority">\r
                            <span class="form-checkbox-label">Execution Priority</span>\r
                        </label>\r
                    </div>\r
\r
                    <div class="options">\r
                        <!-- Full (Base) Priority -->\r
                        <div class="form-group">\r
                            <label class="form-label">Full (Base)</label>\r
                            <input type="range" class="form-range" min="0" max="3"\r
                                [disabled]="!settings.executionPriority"\r
                                [(ngModel)]="settings.fullPriority">\r
                            <div class="form-range-labels">\r
                                <span class="label">Lowest</span>\r
                                <span class="label">Low</span>\r
                                <span class="label">Medium</span>\r
                                <span class="label">High</span>\r
                            </div>\r
                        </div>\r
\r
                        <!-- Incremental Priority -->\r
                        <div class="form-group">\r
                            <label class="form-label">Incremental</label>\r
                            <input type="range" class="form-range" min="0" max="3"\r
                                [disabled]="!settings.executionPriority"\r
                                [(ngModel)]="settings.incrementalPriority">\r
                            <div class="form-range-labels">\r
                                <span class="label">Lowest</span>\r
                                <span class="label">Low</span>\r
                                <span class="label">Medium</span>\r
                                <span class="label">High</span>\r
                            </div>\r
                        </div>\r
                    </div>\r
                </div>\r
            </div>\r
        </div>\r
    </section>\r
\r
    <!-- Save Button -->\r
    <footer>\r
        <button class="form-button" (click)="saveSettings()">\r
            Save\r
        </button>\r
    </footer>\r
</div>\r
`, styles: ["/* renderer/src/app/backup/shared/backup-settings/backup-settings.component.less */\n:host {\n  flex: 1;\n  height: 100%;\n  width: 5rem;\n  border-radius: 0.75rem;\n  opacity: 1;\n  overflow: hidden;\n  background: rgba(255, 255, 255, 0.1);\n}\n:host .container {\n  padding: 1rem;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n:host .container header {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n  padding-bottom: 1rem;\n}\n:host .container header h2 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0;\n  line-height: 1.125rem;\n  color: #ffffff;\n}\n:host .container section {\n  flex: 1 1 auto;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  align-self: stretch;\n  overflow-y: auto;\n  min-height: 0;\n  container-type: inline-size;\n}\n:host .container section::-webkit-scrollbar {\n  display: none;\n}\n:host .container section .columns {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1rem;\n  align-self: stretch;\n}\n:host .container section .columns .column {\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  padding: 1rem;\n}\n:host .container section .columns .column h3 {\n  font-weight: 400;\n  font-size: 1rem;\n  margin-bottom: 1rem;\n  text-transform: uppercase;\n}\n@container (min-width: 56rem) {\n  :host .container section .columns {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n:host .container section .compression,\n:host .container section .password-protection,\n:host .container section .destination-isolation,\n:host .container section .effective-dates,\n:host .container section .execution-settings,\n:host .container section .retention-policy,\n:host .container section .reconcile-image,\n:host .container section .notification,\n:host .container section .execution-priority {\n  margin-bottom: 1rem;\n}\n:host .container section .compression .form-group,\n:host .container section .password-protection .form-group,\n:host .container section .destination-isolation .form-group,\n:host .container section .effective-dates .form-group,\n:host .container section .execution-settings .form-group,\n:host .container section .retention-policy .form-group,\n:host .container section .reconcile-image .form-group,\n:host .container section .notification .form-group,\n:host .container section .execution-priority .form-group {\n  margin-bottom: 0.5rem;\n}\n:host .container section .compression .options,\n:host .container section .password-protection .options,\n:host .container section .destination-isolation .options,\n:host .container section .effective-dates .options,\n:host .container section .execution-settings .options,\n:host .container section .retention-policy .options,\n:host .container section .reconcile-image .options,\n:host .container section .notification .options,\n:host .container section .execution-priority .options {\n  padding-left: 1.5rem;\n}\n:host .container section .compression input[type=number],\n:host .container section .password-protection input[type=number],\n:host .container section .destination-isolation input[type=number],\n:host .container section .effective-dates input[type=number],\n:host .container section .execution-settings input[type=number],\n:host .container section .retention-policy input[type=number],\n:host .container section .reconcile-image input[type=number],\n:host .container section .notification input[type=number],\n:host .container section .execution-priority input[type=number] {\n  width: 5rem;\n}\n:host .container section .dedup-slider .form-range-labels:last-child {\n  order: -1;\n}\n:host .container footer {\n  flex: 0 0 auto;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  gap: 1rem;\n  padding-top: 1rem;\n}\n:host .container footer .form-button {\n  min-width: 12rem;\n}\n/*# sourceMappingURL=backup-settings.component.css.map */\n"] }]
  }], null, { settingsSaved: [{
    type: Output
  }], settingsClosed: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(BackupSettingsComponent, { className: "BackupSettingsComponent", filePath: "renderer/src/app/backup/shared/backup-settings/backup-settings.component.ts", lineNumber: 49 });
})();

// renderer/src/app/backup/backup.component.ts
function BackupComponent_Conditional_15_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "app-backup-settings", 10);
    \u0275\u0275listener("settingsSaved", function BackupComponent_Conditional_15_Template_app_backup_settings_settingsSaved_0_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onBackupSettingsSaved($event));
    })("settingsClosed", function BackupComponent_Conditional_15_Template_app_backup_settings_settingsClosed_0_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onBackupSettingsClosed());
    });
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    \u0275\u0275property("@show", void 0);
  }
}
var BackupComponent = class _BackupComponent {
  Sidebar;
  // Task configuration using signals
  taskName = signal("");
  // Selected options using signals
  selectedSource = signal(null);
  selectedDestination = signal(null);
  selectedSchedule = signal(null);
  // Expanded state for source panel
  expandedSource = signal(null);
  // Expanded state for destination panel
  expandedDestination = signal(null);
  // BackupSettings visibility and state
  showBackupSettings = signal(false);
  backupSettings = signal(null);
  constructor(Sidebar) {
    this.Sidebar = Sidebar;
  }
  // Handle selection changes
  selectSource(sourceId) {
    this.selectedSource.set(sourceId);
    this.expandedSource.set(sourceId);
    this.expandedDestination.set(null);
    this.checkEnableButton();
  }
  selectDestination(destinationId) {
    this.selectedDestination.set(destinationId);
    this.expandedDestination.set(destinationId);
    this.expandedSource.set(null);
    this.checkEnableButton();
  }
  // Handle task name input change
  onTaskNameChange(event) {
    const target = event.target;
    this.taskName.set(target.value);
  }
  selectSchedule(scheduleId) {
    this.selectedSchedule.set(scheduleId);
    this.checkEnableButton();
  }
  // Handle Task Options selection from Options component
  onTaskOptionsSelected(isSelected) {
    this.showBackupSettings.set(isSelected);
    if (isSelected) {
      this.expandedSource.set(null);
      this.expandedDestination.set(null);
      this.Sidebar.collapse();
    }
  }
  // Handle backup settings save
  onBackupSettingsSaved(settings) {
    this.backupSettings.set(settings);
    this.showBackupSettings.set(false);
    console.log("Backup settings saved:", settings);
  }
  // Handle backup settings close
  onBackupSettingsClosed() {
    this.showBackupSettings.set(false);
  }
  // Check if all options are selected to enable the button
  isButtonEnabled = computed(() => {
    return this.selectedSource() !== null && this.selectedDestination() !== null && this.selectedSchedule() !== null;
  });
  // Helper method to check and update button state
  checkEnableButton() {
  }
  // Process backup with selected options
  processBackup() {
    if (this.isButtonEnabled()) {
      console.log("Processing backup with options:", {
        source: this.selectedSource(),
        destination: this.selectedDestination(),
        schedule: this.selectedSchedule()
      });
    }
  }
  // Reset all selections
  resetSelections() {
    this.taskName.set("");
    this.selectedSource.set(null);
    this.selectedDestination.set(null);
    this.selectedSchedule.set(null);
    this.expandedSource.set(null);
    this.expandedDestination.set(null);
  }
  // Save backup source configuration
  saveSourceConfiguration($event) {
    $event.stopPropagation();
    const sourceType = this.selectedSource();
    if (sourceType === "local") {
      console.log("Saving local backup source configuration");
    } else if (sourceType === "remote") {
      console.log("Saving remote backup source configuration");
    } else if (sourceType === "hypervisor") {
      console.log("Saving hypervisor backup source configuration");
    }
    this.expandedSource.set(null);
  }
  // Save backup destination configuration
  saveDestinationConfiguration($event) {
    $event.stopPropagation();
    const destinationType = this.selectedDestination();
    if (destinationType === "local") {
      console.log("Saving local backup destination configuration");
    } else if (destinationType === "network") {
      console.log("Saving network backup destination configuration");
    } else if (destinationType === "actiphy") {
      console.log("Saving actiphy backup destination configuration");
    } else if (destinationType === "cloud") {
      console.log("Saving cloud backup destination configuration");
    } else if (destinationType === "azure") {
      console.log("Saving azure backup destination configuration");
    } else if (destinationType === "amazon") {
      console.log("Saving amazon backup destination configuration");
    } else if (destinationType === "tape") {
      console.log("Saving tape backup destination configuration");
    }
    this.expandedDestination.set(null);
  }
  static \u0275fac = function BackupComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _BackupComponent)(\u0275\u0275directiveInject(SidebarService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _BackupComponent, selectors: [["app-backup"]], decls: 17, vars: 14, consts: [[1, "title"], [1, "actions"], [1, "process", 3, "disabled"], [1, "reset", 3, "click"], ["icon", "rotate-left"], [1, "panel", 3, "sourceSelected", "sourceConfigurationSaved", "selectedSource", "expandedSource"], [1, "panel", 3, "destinationSelected", "destinationConfigurationSaved", "selectedDestination", "expandedDestination"], [1, "panel", 3, "scheduleSelected", "selectedSchedule"], [1, "panel", "settings"], [1, "panel", "options", 3, "taskOptionsSelected"], [1, "panel", "settings", 3, "settingsSaved", "settingsClosed"]], template: function BackupComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "header")(1, "div", 0)(2, "h1");
      \u0275\u0275text(3, "Backup");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p");
      \u0275\u0275text(5, "What, Where & When");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 1)(7, "button", 2);
      \u0275\u0275text(8);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(9, "button", 3);
      \u0275\u0275listener("click", function BackupComponent_Template_button_click_9_listener() {
        return ctx.resetSelections();
      });
      \u0275\u0275element(10, "fa-icon", 4);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(11, "section")(12, "app-backup-source", 5);
      \u0275\u0275listener("sourceSelected", function BackupComponent_Template_app_backup_source_sourceSelected_12_listener($event) {
        return ctx.selectSource($event);
      })("sourceConfigurationSaved", function BackupComponent_Template_app_backup_source_sourceConfigurationSaved_12_listener($event) {
        return ctx.saveSourceConfiguration($event);
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "app-backup-destination", 6);
      \u0275\u0275listener("destinationSelected", function BackupComponent_Template_app_backup_destination_destinationSelected_13_listener($event) {
        return ctx.selectDestination($event);
      })("destinationConfigurationSaved", function BackupComponent_Template_app_backup_destination_destinationConfigurationSaved_13_listener($event) {
        return ctx.saveDestinationConfiguration($event);
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(14, "app-backup-schedule", 7);
      \u0275\u0275listener("scheduleSelected", function BackupComponent_Template_app_backup_schedule_scheduleSelected_14_listener($event) {
        return ctx.selectSchedule($event);
      });
      \u0275\u0275elementEnd();
      \u0275\u0275conditionalCreate(15, BackupComponent_Conditional_15_Template, 1, 1, "app-backup-settings", 8);
      \u0275\u0275elementStart(16, "app-options", 9);
      \u0275\u0275listener("taskOptionsSelected", function BackupComponent_Template_app_options_taskOptionsSelected_16_listener($event) {
        return ctx.onTaskOptionsSelected($event);
      });
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275property("disabled", !ctx.isButtonEnabled());
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.isButtonEnabled() ? "Start Backup" : "Choose options first", " ");
      \u0275\u0275advance(4);
      \u0275\u0275classProp("collapsed", ctx.expandedDestination() !== null || ctx.showBackupSettings());
      \u0275\u0275property("selectedSource", ctx.selectedSource())("expandedSource", ctx.expandedSource());
      \u0275\u0275advance();
      \u0275\u0275classProp("collapsed", ctx.expandedSource() !== null || ctx.showBackupSettings());
      \u0275\u0275property("selectedDestination", ctx.selectedDestination())("expandedDestination", ctx.expandedDestination());
      \u0275\u0275advance();
      \u0275\u0275classProp("collapsed", ctx.expandedSource() !== null || ctx.expandedDestination() !== null || ctx.showBackupSettings());
      \u0275\u0275property("selectedSchedule", ctx.selectedSchedule());
      \u0275\u0275advance();
      \u0275\u0275conditional(ctx.showBackupSettings() ? 15 : -1);
    }
  }, dependencies: [
    FormsModule,
    FontAwesomeModule,
    FaIconComponent,
    BackupSourceComponent,
    BackupDestinationComponent,
    BackupScheduleComponent,
    BackupSettingsComponent,
    OptionsComponent
  ], styles: ["\n\n[_nghost-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  height: 100%;\n  padding: 1rem;\n  overflow: hidden;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 0ms;\n  animation-name: fadeInUp;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  transition-property:\n    color,\n    background,\n    box-shadow;\n  transition-timing-function: ease;\n  transition-duration: 300ms;\n  border: none;\n  padding: 1rem;\n  border-radius: 0.75rem;\n  font-size: 1rem;\n  color: white;\n  cursor: pointer;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   button.process[_ngcontent-%COMP%] {\n  min-width: 16.25rem;\n  background:\n    linear-gradient(\n      90deg,\n      #ef4e38 0%,\n      #b61e0e 100%);\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   button.process[_ngcontent-%COMP%]:disabled {\n  cursor: not-allowed;\n  color: rgba(255, 255, 255, 0.5);\n  background: rgba(255, 255, 255, 0.1);\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   button.process[_ngcontent-%COMP%]:hover:not(:disabled) {\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   button.reset[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.1);\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   button.reset[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  gap: 1rem;\n  width: 100%;\n  min-width: 0;\n  overflow: hidden;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .panel[_ngcontent-%COMP%] {\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 50ms;\n  animation-name: fadeInUp;\n  transition-property: flex, box-shadow;\n  transition-timing-function: ease;\n  transition-duration: 300ms;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .panel[_ngcontent-%COMP%]:nth-child(2) {\n  animation-delay: 100ms;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .panel[_ngcontent-%COMP%]:nth-child(3) {\n  animation-delay: 150ms;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .panel[_ngcontent-%COMP%]:nth-child(4) {\n  animation-delay: 200ms;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .panel[_ngcontent-%COMP%]:nth-child(5) {\n  animation-delay: 250ms;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .panel.settings[_ngcontent-%COMP%] {\n  width: 0rem;\n  animation: none;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .panel.options[_ngcontent-%COMP%] {\n  flex: 0 0 5rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .panel.collapsed[_ngcontent-%COMP%] {\n  flex: 0 0 5rem;\n  width: 5rem;\n  padding: 1.5rem 1rem;\n  align-items: center;\n  justify-content: flex-start;\n  position: relative;\n}\n/*# sourceMappingURL=backup.component.css.map */"], data: { animation: [
    trigger("show", [
      transition(":enter", [
        style({
          flex: 0,
          opacity: 0
        }),
        animate("300ms ease", style({
          flex: 1,
          opacity: 1
        }))
      ]),
      transition(":leave", [
        style({
          flex: 1,
          opacity: 1
        }),
        animate("300ms ease", style({
          flex: 0,
          opacity: 0
        }))
      ])
    ])
  ] } });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BackupComponent, [{
    type: Component,
    args: [{ selector: "app-backup", imports: [
      FormsModule,
      FontAwesomeModule,
      BackupSourceComponent,
      BackupDestinationComponent,
      BackupScheduleComponent,
      BackupSettingsComponent,
      OptionsComponent
    ], animations: [
      trigger("show", [
        transition(":enter", [
          style({
            flex: 0,
            opacity: 0
          }),
          animate("300ms ease", style({
            flex: 1,
            opacity: 1
          }))
        ]),
        transition(":leave", [
          style({
            flex: 1,
            opacity: 1
          }),
          animate("300ms ease", style({
            flex: 0,
            opacity: 0
          }))
        ])
      ])
    ], template: `<!-- Header Section -->\r
<header>\r
    <div class="title">\r
        <h1>Backup</h1>\r
        <p>What, Where & When</p>\r
    </div>\r
    <div class="actions">\r
        <button class="process" [disabled]="!isButtonEnabled()">\r
            {{ isButtonEnabled() ? 'Start Backup' : 'Choose options first' }}\r
        </button>\r
        <button class="reset" (click)="resetSelections()">\r
            <fa-icon icon="rotate-left"></fa-icon>\r
        </button>\r
    </div>\r
</header>\r
\r
<!-- Main Content Container -->\r
<section>\r
    <!-- Backup Source Panel -->\r
    <app-backup-source class="panel"\r
        [selectedSource]="selectedSource()"\r
        [expandedSource]="expandedSource()"\r
        [class.collapsed]="expandedDestination() !== null || showBackupSettings()"\r
        (sourceSelected)="selectSource($event)"\r
        (sourceConfigurationSaved)="saveSourceConfiguration($event)"\r
    ></app-backup-source>\r
\r
    <!-- Backup Destination Panel -->\r
    <app-backup-destination class="panel"\r
        [selectedDestination]="selectedDestination()"\r
        [expandedDestination]="expandedDestination()"\r
        [class.collapsed]="expandedSource() !== null || showBackupSettings()"\r
        (destinationSelected)="selectDestination($event)"\r
        (destinationConfigurationSaved)="saveDestinationConfiguration($event)"\r
    ></app-backup-destination>\r
\r
    <!-- Scheduling Panel -->\r
    <app-backup-schedule class="panel"\r
        [selectedSchedule]="selectedSchedule()"\r
        [class.collapsed]="expandedSource() !== null || expandedDestination() !== null || showBackupSettings()"\r
        (scheduleSelected)="selectSchedule($event)"\r
    ></app-backup-schedule>\r
\r
    <!-- BackupSettings Panel -->\r
    @if (showBackupSettings()) {\r
        <app-backup-settings [@show] class="panel settings"\r
            (settingsSaved)="onBackupSettingsSaved($event)"\r
            (settingsClosed)="onBackupSettingsClosed()">\r
        </app-backup-settings>\r
    }\r
\r
    <!-- Settings Panel -->\r
    <app-options class="panel options" (taskOptionsSelected)="onTaskOptionsSelected($event)"></app-options>\r
</section>\r
`, styles: ["/* renderer/src/app/backup/backup.component.less */\n:host {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  height: 100%;\n  padding: 1rem;\n  overflow: hidden;\n}\n:host header {\n  display: flex;\n  justify-content: space-between;\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 0ms;\n  animation-name: fadeInUp;\n}\n:host header .title h1 {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0 0 0.5rem 0;\n  color: #ffffff;\n}\n:host header .title p {\n  font-size: 1rem;\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n}\n:host header .actions {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n:host header .actions button {\n  transition-property:\n    color,\n    background,\n    box-shadow;\n  transition-timing-function: ease;\n  transition-duration: 300ms;\n  border: none;\n  padding: 1rem;\n  border-radius: 0.75rem;\n  font-size: 1rem;\n  color: white;\n  cursor: pointer;\n}\n:host header .actions button.process {\n  min-width: 16.25rem;\n  background:\n    linear-gradient(\n      90deg,\n      #ef4e38 0%,\n      #b61e0e 100%);\n}\n:host header .actions button.process:disabled {\n  cursor: not-allowed;\n  color: rgba(255, 255, 255, 0.5);\n  background: rgba(255, 255, 255, 0.1);\n}\n:host header .actions button.process:hover:not(:disabled) {\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\n}\n:host header .actions button.reset {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.1);\n}\n:host header .actions button.reset:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n:host section {\n  flex: 1;\n  display: flex;\n  gap: 1rem;\n  width: 100%;\n  min-width: 0;\n  overflow: hidden;\n}\n:host section .panel {\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 50ms;\n  animation-name: fadeInUp;\n  transition-property: flex, box-shadow;\n  transition-timing-function: ease;\n  transition-duration: 300ms;\n}\n:host section .panel:nth-child(2) {\n  animation-delay: 100ms;\n}\n:host section .panel:nth-child(3) {\n  animation-delay: 150ms;\n}\n:host section .panel:nth-child(4) {\n  animation-delay: 200ms;\n}\n:host section .panel:nth-child(5) {\n  animation-delay: 250ms;\n}\n:host section .panel.settings {\n  width: 0rem;\n  animation: none;\n}\n:host section .panel.options {\n  flex: 0 0 5rem;\n}\n:host section .panel.collapsed {\n  flex: 0 0 5rem;\n  width: 5rem;\n  padding: 1.5rem 1rem;\n  align-items: center;\n  justify-content: flex-start;\n  position: relative;\n}\n/*# sourceMappingURL=backup.component.css.map */\n"] }]
  }], () => [{ type: SidebarService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(BackupComponent, { className: "BackupComponent", filePath: "renderer/src/app/backup/backup.component.ts", lineNumber: 50 });
})();
export {
  BackupComponent
};
//# sourceMappingURL=chunk-WQR3HH72.js.map
