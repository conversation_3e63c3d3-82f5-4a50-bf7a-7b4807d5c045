import { Component, input, output, computed } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { Dialog } from '@angular/cdk/dialog';
import { RecoveryPinDialogComponent } from './recovery-pin-dialog/recovery-pin-dialog.component';

export interface RecoveryPoint {
    id: string;
    date: string;
    size: string;
    hasChildren: boolean;
    level: number;
}

@Component({
    selector: 'app-recovery-source',
    templateUrl: './recovery-source.component.html',
    styleUrl: './recovery-source.component.less',
    imports: [FontAwesomeModule],
})
export class RecoverySourceComponent {
    // Inputs from parent component
    selectedSource = input<string | null>(null);
    expandedPanel = input<string | null>(null);

    // Outputs to parent component
    sourceSelected = output<string>();
    onPanelExpand = output<'source'>();

    constructor(public dialog: Dialog) {}

    openPinDialog(): void {
        const dialogRef = this.dialog.open<string>(RecoveryPinDialogComponent);

        dialogRef.closed.subscribe(result => {
            console.log('The dialog was closed');
            if (result) {
                console.log('PIN:', result);
            }
        });
    }

    // Mock recovery points data
    recoveryPoints: RecoveryPoint[] = [
        {
            id: 'rp1',
            date: '2025-04-06 6:00:00',
            size: '1.19 TB',
            hasChildren: false,
            level: 0
        },
        {
            id: 'rp2',
            date: '2025-04-07 6:00:00',
            size: '1.04 GB',
            hasChildren: true,
            level: 1
        },
        {
            id: 'rp3',
            date: '2025-04-08 6:00:00',
            size: '2.11 GB',
            hasChildren: true,
            level: 1
        },
        {
            id: 'rp4',
            date: '2025-04-09 6:00:00',
            size: '2.07 GB',
            hasChildren: true,
            level: 1
        },
        {
            id: 'rp5',
            date: '2025-04-10 6:00:00',
            size: '5.81 GB',
            hasChildren: true,
            level: 1
        },
        {
            id: 'rp6',
            date: '2025-04-11 6:00:00',
            size: '3.88 GB',
            hasChildren: true,
            level: 1
        },
        {
            id: 'rp7',
            date: '2025-04-11 6:00:00',
            size: '1.19 TB',
            hasChildren: false,
            level: 0
        },
        {
            id: 'rp8',
            date: '2025-04-12 6:00:00',
            size: '1.18 GB',
            hasChildren: true,
            level: 1
        },
        {
            id: 'rp9',
            date: '2025-04-13 6:00:00',
            size: '1.19 GB',
            hasChildren: false,
            level: 0
        },
        {
            id: 'rp10',
            date: '2025-04-14 6:00:00',
            size: '2.21 GB',
            hasChildren: true,
            level: 1
        }
    ];

    // Check if panel is expanded
    isPanelExpanded = computed(() => this.expandedPanel() === 'source');

    // Check if panel is collapsed
    isPanelCollapsed = computed(() => {
        const expanded = this.expandedPanel();
        return expanded !== null && expanded !== 'source';
    });

    // Handle source selection
    selectSource(sourceId: string): void {
        this.sourceSelected.emit(sourceId);
    }

    // Handle panel expand
    expandPanel(): void {
        this.onPanelExpand.emit('source');
    }
}
