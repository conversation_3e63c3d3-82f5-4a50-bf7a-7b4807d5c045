import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { RouterModule } from '@angular/router';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { filter } from 'rxjs/operators';

@Component({
    selector: 'app-preferences',
    imports: [RouterModule, FontAwesomeModule],
    templateUrl: './preferences.component.html',
    styleUrl: './preferences.component.less'
})
export class PreferencesComponent implements OnInit {
    activeRoute = 'general';

    constructor(private router: Router) {}

    ngOnInit(): void {
        // Listen to route changes to update active route
        this.router.events
            .pipe(filter(event => event instanceof NavigationEnd))
            .subscribe((event: NavigationEnd) => {
                const urlSegments = event.url.split('/');
                const lastSegment = urlSegments[urlSegments.length - 1];
                this.activeRoute = lastSegment || 'general';
            });

        // Set initial active route
        const currentUrl = this.router.url;
        const urlSegments = currentUrl.split('/');
        const lastSegment = urlSegments[urlSegments.length - 1];
        this.activeRoute = lastSegment || 'general';
    }
}
