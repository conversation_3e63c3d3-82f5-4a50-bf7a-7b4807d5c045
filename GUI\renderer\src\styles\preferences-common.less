@import "../../../styles/variables.less";
@import "../../../styles/mixins.less";

.settings-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 1rem;
}

.settings-header {
    .settings-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: @border-radius-small;

        .settings-icon {
            padding: 0.25rem;

            fa-icon {
                color: @text-color;
                font-size: 1rem;
            }
        }

        h2 {
            margin: 0;
            font-family: @font-family;
            font-weight: 500;
            font-size: 1rem;
            color: @text-color;
        }
    }
}

.settings-form {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    color: @text-color;
}
