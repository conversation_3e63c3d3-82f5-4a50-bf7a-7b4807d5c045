<header>
    <h2>Backup Destination</h2>
    <div class="icon-container">
        <fa-icon icon="location-dot"></fa-icon>
    </div>
</header>
@if (expandedDestination() !== null) {
    <section class="expanded-content">
        <div class="left-column">
            <div
                class="item single-column"
                [class.selected]="isDestinationSelected()('local') && isDestinationExpanded()('local')"
                [class.active]="isDestinationExpanded()('local')"
                (click)="selectDestination('local')"
            >
                <div class="item-icon">
                    <fa-icon icon="desktop"></fa-icon>
                </div>
                <div class="item-name">Machine</div>
            </div>
            <div
                class="item single-column"
                [class.selected]="isDestinationSelected()('actiphy')"
                (click)="selectDestination('actiphy')"
            >
                <div class="item-icon">
                    <fa-icon icon="server"></fa-icon>
                </div>
                <div class="item-name">Actiphy Storage Server</div>
            </div>
            <div
                class="item single-column"
                [class.selected]="isDestinationSelected()('cloud')"
                (click)="selectDestination('cloud')"
            >
                <div class="item-icon">
                    <fa-icon icon="cloud"></fa-icon>
                </div>
                <div class="item-name">Actiphy Backup Cloud Service</div>
            </div>
            <div
                class="item single-column"
                [class.selected]="isDestinationSelected()('azure')"
                (click)="selectDestination('azure')"
            >
                <div class="item-icon">
                    <fa-icon icon="microsoft" [icon]="['fab', 'microsoft']"></fa-icon>
                </div>
                <div class="item-name">Cloud</div>
            </div>
            <div
                class="item single-column"
                [class.selected]="isDestinationSelected()('tape')"
                (click)="selectDestination('tape')"
            >
                <div class="item-icon">
                    <fa-icon icon="tape"></fa-icon>
                </div>
                <div class="item-name">Tape Backup</div>
            </div>
        </div>
        <div class="right-column">
            @if (isDestinationExpanded()('local')) {
                <div class="destination-form">
                    <div class="form-header">
                        <div class="header-icon">
                            <fa-icon icon="desktop"></fa-icon>
                        </div>
                        <h3>Local</h3>
                    </div>
                    <div class="form-content">
                        <div class="file-select-table">
                            <div class="table-header">
                                <div class="header-cell">Select File</div>
                                <div class="header-cell"></div>
                                <div class="header-cell"></div>
                            </div>
                            <div class="table-row">
                                <div class="row-cell">
                                    <div class="expand-icon"></div>
                                    C:
                                </div>
                                <div class="row-cell"></div>
                                <div class="row-cell"></div>
                            </div>
                            <div class="table-row">
                                <div class="row-cell">
                                    <div class="expand-icon"></div>
                                    D:
                                </div>
                                <div class="row-cell"></div>
                                <div class="row-cell"></div>
                            </div>
                            <div class="table-row">
                                <div class="row-cell">
                                    <div class="expand-icon"></div>
                                    E:
                                </div>
                                <div class="row-cell"></div>
                                <div class="row-cell"></div>
                            </div>
                            <div class="table-row">
                                <div class="row-cell">
                                    <div class="expand-icon"></div>
                                    F:
                                </div>
                                <div class="row-cell"></div>
                                <div class="row-cell"></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">
                            Save
                        </button>
                    </div>
                </div>
            }
            @if (isDestinationExpanded()('network')) {
                <div class="destination-form">
                    <div class="form-header">
                        <div class="header-icon">
                            <fa-icon icon="network-wired"></fa-icon>
                        </div>
                        <h3>Network Location</h3>
                    </div>
                    <div class="form-content">
                        <!-- Network configuration content will be added here -->
                    </div>
                    <div class="form-actions">
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">
                            Save
                        </button>
                    </div>
                </div>
            }
            @if (isDestinationExpanded()('actiphy')) {
                <div class="destination-form">
                    <div class="form-header">
                        <div class="header-icon">
                            <fa-icon icon="server"></fa-icon>
                        </div>
                        <h3>Actiphy Storage Server</h3>
                    </div>
                    <div class="form-content">
                        <!-- Actiphy storage configuration content will be added here -->
                    </div>
                    <div class="form-actions">
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">
                            Save
                        </button>
                    </div>
                </div>
            }
            @if (isDestinationExpanded()('cloud')) {
                <div class="destination-form">
                    <div class="form-header">
                        <div class="header-icon">
                            <fa-icon icon="cloud"></fa-icon>
                        </div>
                        <h3>Actiphy Backup Cloud Service</h3>
                    </div>
                    <div class="form-content">
                        <!-- Cloud service configuration content will be added here -->
                    </div>
                    <div class="form-actions">
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">
                            Save
                        </button>
                    </div>
                </div>
            }
            @if (isDestinationExpanded()('azure')) {
                <div class="destination-form">
                    <div class="form-header">
                        <div class="header-icon">
                            <fa-icon icon="microsoft" [icon]="['fab', 'microsoft']"></fa-icon>
                        </div>
                        <h3>Azure Blob</h3>
                    </div>
                    <div class="form-content">
                        <!-- Azure Blob configuration content will be added here -->
                    </div>
                    <div class="form-actions">
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">
                            Save
                        </button>
                    </div>
                </div>
            }
            @if (isDestinationExpanded()('amazon')) {
                <div class="destination-form">
                    <div class="form-header">
                        <div class="header-icon">
                            <fa-icon icon="aws" [icon]="['fab', 'aws']"></fa-icon>
                        </div>
                        <h3>Amazon S3</h3>
                    </div>
                    <div class="form-content">
                        <!-- Amazon S3 configuration content will be added here -->
                    </div>
                    <div class="form-actions">
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">
                            Save
                        </button>
                    </div>
                </div>
            }
            @if (isDestinationExpanded()('tape')) {
                <div class="destination-form">
                    <div class="form-header">
                        <div class="header-icon">
                            <fa-icon icon="tape"></fa-icon>
                        </div>
                        <h3>Tape Backup</h3>
                    </div>
                    <div class="form-content">
                        <!-- Tape Backup configuration content will be added here -->
                    </div>
                    <div class="form-actions">
                        <button class="save-button" (click)="saveDestinationConfiguration($event)">
                            Save
                        </button>
                    </div>
                </div>
            }
        </div>
    </section>
}
@else {
    <section>
        <div
            class="item"
            [class.selected]="isDestinationSelected()('local')"
            (click)="selectDestination('local')"
        >
            <div class="item-icon">
                <fa-icon icon="laptop"></fa-icon>
            </div>
            <div class="item-content">
                <div class="item-header">
                    <h3>Local</h3>
                    <p>Backup to a local machine</p>
                </div>
            </div>
        </div>
        <div
            class="item"
            [class.selected]="isDestinationSelected()('actiphy')"
            (click)="selectDestination('actiphy')"
        >
            <div class="item-icon">
                <fa-icon icon="server"></fa-icon>
            </div>
            <div class="item-content">
                <div class="item-header">
                    <h3>Actiphy Storage Server</h3>
                    <p>Backup to an Actiphy Storage Server</p>
                </div>
            </div>
        </div>
        <div
            class="item"
            [class.selected]="isDestinationSelected()('cloud')"
            (click)="selectDestination('cloud')"
        >
            <div class="item-icon">
                <fa-icon icon="cloud"></fa-icon>
            </div>
            <div class="item-content">
                <div class="item-header">
                    <h3>Actiphy Backup Cloud Service</h3>
                    <p>Backup to Actiphy cloud</p>
                </div>
            </div>
        </div>
        <div
            class="item"
            [class.selected]="isDestinationSelected()('azure')"
            (click)="selectDestination('azure')"
        >
            <div class="item-icon">
                <fa-icon icon="microsoft" [icon]="['fab', 'microsoft']"></fa-icon>
            </div>
            <div class="item-content">
                <div class="item-header">
                    <h3>Azure Blob</h3>
                    <p>Backup to Azure Blob storage</p>
                </div>
            </div>
        </div>
        <div
            class="item"
            [class.selected]="isDestinationSelected()('amazon')"
            (click)="selectDestination('amazon')"
        >
            <div class="item-icon">
                <fa-icon icon="aws" [icon]="['fab', 'aws']"></fa-icon>
            </div>
            <div class="item-content">
                <div class="item-header">
                    <h3>Amazon S3</h3>
                    <p>Backup to Amazon S3</p>
                </div>
            </div>
        </div>
        <div
            class="item"
            [class.selected]="isDestinationSelected()('tape')"
            (click)="selectDestination('tape')"
        >
            <div class="item-icon">
                <fa-icon icon="tape"></fa-icon>
            </div>
            <div class="item-content">
                <div class="item-header">
                    <h3>Tape Devices</h3>
                    <p>Backup to tape storage</p>
                </div>
            </div>
        </div>
    </section>
}
