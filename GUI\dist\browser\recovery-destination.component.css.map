{"version": 3, "sources": ["renderer/src/app/recovery/shared/recovery-destination/recovery-destination.component.less", "renderer/src/styles/mixins.less"], "sourcesContent": ["@import '../../../../styles/variables.less';\n@import '../../../../styles/mixins.less';\n\n:host {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 0.75rem;\n    overflow: hidden;\n    cursor: pointer;\n    padding: 1rem;\n\n    // Panel Header\n    header {\n        flex: 0 0 auto;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        gap: 1rem;\n        padding-bottom: 1rem;\n\n        h2 {\n            font-size: 1.125rem;\n            font-weight: 600;\n            margin: 0;\n            line-height: 1.125rem;\n            color: @text-color;\n        }\n\n        .icon-container {\n            width: 20px;\n            height: 20px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            flex-shrink: 0;\n            border-radius: 6px;\n            background: rgba(255, 255, 255, 0);\n            padding: 0;\n        }\n\n        // Header with mapping mode selection\n        .detail-header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n\n            .mapping-mode-tabs {\n                display: flex;\n                gap: 1rem;\n\n                .tab-button {\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    padding: 0.5rem 0.5rem;\n                    background: rgba(16, 185, 129, 0.2);\n                    border: none;\n                    border-radius: 0.5rem;\n                    color: @text-color;\n                    font-size: 1.125rem;\n                    font-weight: 400;\n                    cursor: pointer;\n\n                    .transition(background, color);\n\n                    &.active {\n                        background: rgba(16, 185, 129, 0.5);\n                        color: @text-color;\n                    }\n\n                    &:hover {\n                        background: rgba(16, 185, 129, 0.3);\n                    }\n                }\n            }\n\n            .undo-button {\n                display: flex;\n                align-items: center;\n                gap: 0.5rem;\n                padding: 0.5rem;\n                background: rgba(255, 255, 255, 0);\n                border: none;\n                border-radius: 0.5rem;\n                color: @text-color;\n                font-size: 1.125rem;\n                cursor: pointer;\n\n                .transition(background);\n\n                &:hover {\n                    background: rgba(255, 255, 255, 0.1);\n                }\n\n                fa-icon {\n                    font-size: 1rem;\n                }\n            }\n        }\n    }\n\n    // Collapsed vertical panel styling\n    &.collapsed {\n        header {\n            writing-mode: vertical-lr;\n            text-orientation: mixed;\n            padding: 0;\n            height: 100%;\n            flex-direction: row-reverse;\n            justify-content: flex-end;\n\n            h2 {\n                font-size: 1rem;\n                font-weight: 600;\n                line-height: 1.2;\n                text-align: center;\n                margin: 0;\n                writing-mode: vertical-lr;\n                text-orientation: mixed;\n                color: @text-color;\n                letter-spacing: -0.02em;\n                transform: rotate(180deg);\n            }\n\n            .icon-container {\n                width: 20px;\n                height: 20px;\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                flex-shrink: 0;\n                border-radius: 6px;\n                background: rgba(255, 255, 255, 0);\n                padding: 0;\n            }\n        }\n\n        section {\n            display: none;\n        }\n    }\n\n    section {\n        flex: 1;\n        overflow-y: auto;\n        overflow-x: hidden;\n        display: grid;\n        grid-template-columns: repeat(2, 1fr);\n        gap: 1rem;\n\n        // Hide scrollbar for Chromium/WebKit (Electron)\n        &::-webkit-scrollbar {\n            display: none;\n        }\n\n        .item {\n            display: flex;\n            align-items: center;\n            gap: 1rem;\n            padding: 1rem;\n            background: rgba(255, 255, 255, 0.05);\n            border: 1px solid rgba(255, 255, 255, 0.1);\n            border-radius: 0.5rem;\n            cursor: pointer;\n            overflow: hidden;\n\n            .transition(background, border-color, box-shadow);\n\n            &:hover {\n                background: rgba(255, 255, 255, 0.08);\n                border-color: rgba(255, 255, 255, 0.2);\n            }\n\n            &.selected {\n                background: rgba(255, 255, 255, 0.1);\n                border-color: @primary-color;\n                box-shadow: 0 0 0 2px rgba(239, 78, 56, 0.3);\n\n                .item-icon {\n                    color: @primary-color;\n                }\n            }\n\n            .item-icon {\n                display: flex;\n                align-items: center;\n                justify-content: center;\n                width: 3.25rem;\n                height: 2.75rem;\n                font-size: 3rem;\n                color: @text-secondary;\n\n                .transition(color);\n\n                fa-icon {\n                    font-size: 3rem;\n                    color: inherit;\n                }\n            }\n\n            .item-content {\n                flex: 1;\n\n                h3 {\n                    font-size: 1rem;\n                    font-weight: 500;\n                    color: @text-color;\n                    margin: 0 0 0.5rem 0;\n                    line-height: 1.3;\n                }\n\n                p {\n                    font-size: 0.8125rem;\n                    color: @text-secondary;\n                    margin: 0;\n                    line-height: 1.4;\n                }\n            }\n        }\n\n        // Detail View Styles\n        &.detail-view {\n            display: flex;\n            flex-direction: column;\n            height: 100%;\n            gap: 1rem;\n\n            // Mapping section\n            .mapping-section {\n                flex: 1;\n                display: flex;\n                flex-direction: column;\n            }\n\n            // Settings footer\n            .settings-footer {\n                display: flex;\n                justify-content: space-between;\n                align-items: flex-end;\n                gap: 1rem;\n                padding-top: 1rem;\n\n                .settings-options {\n                    display: flex;\n                    flex-direction: column;\n                    gap: 0.5rem;\n\n                    .checkbox-group {\n                        display: flex;\n                        align-items: center;\n                        gap: 1rem;\n                        cursor: pointer;\n\n                        input[type=\"checkbox\"] {\n                            display: none;\n                        }\n\n                        .checkbox-custom {\n                            width: 0.9375rem;\n                            height: 0.9375rem;\n                            background: rgba(255, 255, 255, 0.2);\n                            border-radius: 0.1875rem;\n                            position: relative;\n\n                            .transition(background);\n\n                            &::after {\n                                content: '';\n                                position: absolute;\n                                top: 50%;\n                                left: 50%;\n                                width: 0.5rem;\n                                height: 0.25rem;\n                                border: 2px solid @text-color;\n                                border-top: none;\n                                border-right: none;\n                                transform: translate(-50%, -60%) rotate(-45deg);\n                                opacity: 0;\n\n                                .transition(opacity);\n                            }\n                        }\n\n                        input[type=\"checkbox\"]:checked + .checkbox-custom {\n                            background: rgba(255, 255, 255, 0.2);\n\n                            &::after {\n                                opacity: 1;\n                            }\n                        }\n\n                        .checkbox-label {\n                            font-size: 0.875rem;\n                            font-weight: 500;\n                            color: @text-color;\n                            line-height: 1.43;\n                        }\n                    }\n\n                    .post-restore-group {\n                        display: flex;\n                        align-items: center;\n                        gap: 1rem;\n\n                        .restore-action-select {\n                            padding: 0.3125rem 0.625rem;\n                            background: rgba(255, 255, 255, 0.2);\n                            border: none;\n                            border-radius: 0.25rem;\n                            color: @text-color;\n                            font-size: 0.75rem;\n                            font-weight: 500;\n                            line-height: 1.67;\n                            cursor: pointer;\n\n                            .transition(background);\n\n                            &:hover {\n                                background: rgba(255, 255, 255, 0.3);\n                            }\n\n                            option {\n                                background: @background-dark;\n                                color: @text-color;\n                            }\n                        }\n                    }\n                }\n\n                .next-button {\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    gap: 0.5rem;\n                    padding: 0.5rem 0.75rem;\n                    background: linear-gradient(to right, #ef4e38 0%, #b61e0e 100%);\n                    border: none;\n                    border-radius: 0.5rem;\n                    color: @text-color;\n                    font-size: 0.875rem;\n                    font-weight: 400;\n                    line-height: 1.36;\n                    cursor: pointer;\n                    min-width: 12rem;\n\n                    .transition(opacity);\n\n                    &:hover {\n                        opacity: 0.9;\n                    }\n\n                    &:active {\n                        transform: translateY(1px);\n                    }\n                }\n            }\n        }\n    }\n}\n", "/**\n * Mixin\n * <AUTHOR>\n */\n\n@import \"variables.less\";\n\n// Transition\n.transition(...) {\n    & when (length(@arguments) = 0) {\n        transition: none;\n    }\n\n    & when (length(@arguments) = 1) {\n        transition: @arguments 300ms ease;\n    }\n\n    & when (length(@arguments) > 1) {\n        @properties: replace(\"@{arguments}\", \" \", \", \", \"g\");\n        transition-property: ~\"@{properties}\";\n        transition-timing-function: ease;\n        transition-duration: 300ms;\n    }\n}\n\n// Animations\n.animation(@name, @delay: 0ms) {\n    animation-duration: 500ms;\n    animation-fill-mode: forwards;\n    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n    animation-delay: @delay;\n\n    & when (@name = fade-in-up) {\n        animation-name: fadeInUp;\n    }\n\n    & when (@name = fade-in-left) {\n        animation-name: fadeInLeft;\n    }\n\n    & when (@name = fade-in) {\n        animation-name: fadeIn;\n    }\n}\n"], "mappings": ";AAGA;AACI,WAAA;AACA,kBAAA;AACA,UAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,YAAA;AACA,UAAA;AACA,WAAA;;AARJ,MAWI;AACI,QAAA,EAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,kBAAA;;AAjBR,MAWI,OAQI;AACI,aAAA;AACA,eAAA;AACA,UAAA;AACA,eAAA;AACA,SAAA;;AAxBZ,MAWI,OAgBI,CAAA;AACI,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;;AApCZ,MAWI,OA6BI,CAAA;AACI,WAAA;AACA,mBAAA;AACA,eAAA;;AA3CZ,MAWI,OA6BI,CAAA,cAKI,CAAA;AACI,WAAA;AACA,OAAA;;AA/ChB,MAWI,OA6BI,CAAA,cAKI,CAAA,kBAII,CAAA;AACI,WAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA,OAAA;AACA,cAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;AACA,iBAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;AACA,UAAA;AC5CZ,uBAAA,UAAA,EAAA;AACA,8BAAA;AACA,uBAAA;;AD8CY,MArDhB,OA6BI,CAAA,cAKI,CAAA,kBAII,CAAA,UAeK,CAAA;AACG,cAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,SAAA;;AAGJ,MA1DhB,OA6BI,CAAA,cAKI,CAAA,kBAII,CAAA,UAoBK;AACG,cAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAtExB,MAWI,OA6BI,CAAA,cAmCI,CAAA;AACI,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;AACA,iBAAA;AACA,SAAA;AACA,aAAA;AACA,UAAA;AC1ER,cAAA,WAAA,MAAA;;AD8EQ,MA9EZ,OA6BI,CAAA,cAmCI,CAAA,WAcK;AACG,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AA1FpB,MAWI,OA6BI,CAAA,cAmCI,CAAA,YAkBI;AACI,aAAA;;AAOhB,KAAC,CAAA,UACG;AACI,gBAAA;AACA,oBAAA;AACA,WAAA;AACA,UAAA;AACA,kBAAA;AACA,mBAAA;;AAPR,KAAC,CAAA,UACG,OAQI;AACI,aAAA;AACA,eAAA;AACA,eAAA;AACA,cAAA;AACA,UAAA;AACA,gBAAA;AACA,oBAAA;AACA,SAAA;AACA,kBAAA;AACA,aAAW,OAAA;;AAnBvB,KAAC,CAAA,UACG,OAqBI,CAhGJ;AAiGQ,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,WAAA;;AA/BZ,KAAC,CAAA,UAmCG;AACI,WAAA;;AAzIZ,MA6II;AACI,QAAA;AACA,cAAA;AACA,cAAA;AACA,WAAA;AACA,yBAAuB,OAAA,CAAA,EAAA;AACvB,OAAA;;AAGA,MATJ,OASK;AACG,WAAA;;AAvJZ,MA6II,QAaI,CAAA;AACI,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,UAAA;AACA,YAAA;ACnJJ;IAAA,UAAA;IAAA,YAAA;IAAA;AACA,8BAAA;AACA,uBAAA;;ADqJI,MA1BR,QAaI,CAAA,IAaK;AACG,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,gBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,MA/BR,QAaI,CAAA,IAkBK,CAAA;AACG,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,gBAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,KAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA;;AAHJ,MA/BR,QAaI,CAAA,IAkBK,CAAA,SAKG,CAAA;AACI,SAAA;;AAlLpB,MA6II,QAaI,CAAA,KA4BI,CALI;AAMA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,SAAA;AACA,UAAA;AACA,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AClLR,cAAA,MAAA,MAAA;;ADXR,MA6II,QAaI,CAAA,KA4BI,CALI,UAgBA;AACI,aAAA;AACA,SAAA;;AAnMpB,MA6II,QAaI,CAAA,KA6CI,CAAA;AACI,QAAA;;AAxMhB,MA6II,QAaI,CAAA,KA6CI,CAAA,aAGI;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA,EAAA,EAAA,OAAA;AACA,eAAA;;AA/MpB,MA6II,QAaI,CAAA,KA6CI,CAAA,aAWI;AACI,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;AACA,eAAA;;AAMZ,MA/EJ,OA+EK,CAAA;AACG,WAAA;AACA,kBAAA;AACA,UAAA;AACA,OAAA;;AAJJ,MA/EJ,OA+EK,CAAA,YAOG,CAAA;AACI,QAAA;AACA,WAAA;AACA,kBAAA;;AAVR,MA/EJ,OA+EK,CAAA,YAcG,CAAA;AACI,WAAA;AACA,mBAAA;AACA,eAAA;AACA,OAAA;AACA,eAAA;;AAnBR,MA/EJ,OA+EK,CAAA,YAcG,CAAA,gBAOI,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;;AAxBZ,MA/EJ,OA+EK,CAAA,YAcG,CAAA,gBAOI,CAAA,iBAKI,CAAA;AACI,WAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA;;AA9BhB,MA/EJ,OA+EK,CAAA,YAcG,CAAA,gBAOI,CAAA,iBAKI,CAAA,eAMI,KAAK,CAAA;AACD,WAAA;;AAjCpB,MA/EJ,OA+EK,CAAA,YAcG,CAAA,gBAOI,CAAA,iBAKI,CAAA,eAUI,CAAA;AACI,SAAA;AACA,UAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,YAAA;AC1PpB,cAAA,WAAA,MAAA;;AD8PoB,MA5HxB,OA+EK,CAAA,YAcG,CAAA,gBAOI,CAAA,iBAKI,CAAA,eAUI,CAAA,eASK;AACG,WAAS;AACT,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,UAAA,IAAA,MAAA;AACA,cAAA;AACA,gBAAA;AACA,aAAW,UAAA,IAAA,EAAA,MAAsB,OAAA;AACjC,WAAA;ACzQxB,cAAA,QAAA,MAAA;;ADiNA,MA/EJ,OA+EK,CAAA,YAcG,CAAA,gBAOI,CAAA,iBAKI,CAAA,eAoCI,KAAK,CAAA,cAAiB,SAAS,EAAA,CA1B/B;AA2BI,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAEA,MAhJxB,OA+EK,CAAA,YAcG,CAAA,gBAOI,CAAA,iBAKI,CAAA,eAoCI,KAAK,CAAA,cAAiB,SAAS,EAAA,CA1B/B,eA6BK;AACG,WAAA;;AAlExB,MA/EJ,OA+EK,CAAA,YAcG,CAAA,gBAOI,CAAA,iBAKI,CAAA,eA4CI,CAAA;AACI,aAAA;AACA,eAAA;AACA,SAAA;AACA,eAAA;;AA1EpB,MA/EJ,OA+EK,CAAA,YAcG,CAAA,gBAOI,CAAA,iBAyDI,CAAA;AACI,WAAA;AACA,eAAA;AACA,OAAA;;AAjFhB,MA/EJ,OA+EK,CAAA,YAcG,CAAA,gBAOI,CAAA,iBAyDI,CAAA,mBAKI,CAAA;AACI,WAAA,UAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;AACA,iBAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,UAAA;AC7SpB,cAAA,WAAA,MAAA;;ADiToB,MA/KxB,OA+EK,CAAA,YAcG,CAAA,gBAOI,CAAA,iBAyDI,CAAA,mBAKI,CAAA,qBAaK;AACG,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAjGxB,MA/EJ,OA+EK,CAAA,YAcG,CAAA,gBAOI,CAAA,iBAyDI,CAAA,mBAKI,CAAA,sBAiBI;AACI,cAAA;AACA,SAAA;;AAtGxB,MA/EJ,OA+EK,CAAA,YAcG,CAAA,gBA8FI,CAAA;AACI,WAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA;AACA,WAAA,OAAA;AACA;IAAY;MAAA,GAAA,KAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACZ,UAAA;AACA,iBAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA;AACA,UAAA;AACA,aAAA;AC3UZ,cAAA,QAAA,MAAA;;AD+UY,MA7MhB,OA+EK,CAAA,YAcG,CAAA,gBA8FI,CAAA,WAkBK;AACG,WAAA;;AAGJ,MAjNhB,OA+EK,CAAA,YAcG,CAAA,gBA8FI,CAAA,WAsBK;AACG,aAAW,WAAA;;", "names": []}