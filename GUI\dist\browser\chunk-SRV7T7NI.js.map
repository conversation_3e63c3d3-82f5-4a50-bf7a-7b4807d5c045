{"version": 3, "sources": ["renderer/src/app/preferences/general-settings/general-settings.component.ts", "renderer/src/app/preferences/general-settings/general-settings.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n@Component({\r\n    selector: 'app-general-settings',\r\n    imports: [FontAwesomeModule, FormsModule],\r\n    templateUrl: './general-settings.component.html',\r\n    styleUrl: './general-settings.component.less'\r\n})\r\nexport class GeneralSettingsComponent {\r\n    // Form model\r\n    purgeEventLogs = false;\r\n    purgeEventLogsDays = 30;\r\n    purgeTaskLogs = false;\r\n    logWindowsEvent = false;\r\n    logWindowsEventCondition = 'Task Succeeded or failed';\r\n    hyperStandbyTasks = 30;\r\n    hyperReplicationTasks = 30;\r\n    limitCpuLoad = false;\r\n    maxCpuLoad = 100;\r\n    limitDiskScan = false;\r\n    maxDisksToScan = 1;\r\n\r\n    logWindowsEventOptions = [\r\n        'Task Succeeded',\r\n        'Task Failed',\r\n        'Task Succeeded or failed'\r\n    ];\r\n\r\n    onApply() {\r\n        // Handle apply logic\r\n        console.log('Settings applied');\r\n    }\r\n\r\n    onCancel() {\r\n        // Handle cancel logic\r\n        console.log('Settings cancelled');\r\n    }\r\n}\r\n", "<header>\r\n    <div class=\"icon\">\r\n        <fa-icon icon=\"sliders\"></fa-icon>\r\n    </div>\r\n    <h2>General Settings</h2>\r\n</header>\r\n\r\n<section>\r\n    <div class=\"section\">\r\n        <div class=\"header\">\r\n            <fa-icon icon=\"calendar-lines-pen\"></fa-icon>\r\n            <h3>Event Log</h3>\r\n        </div>\r\n        <div class=\"body\">\r\n            <div class=\"form-group inline\">\r\n                <label class=\"form-label\">\r\n                    <input type=\"checkbox\" class=\"form-checkbox\" name=\"purgeEventLogs\" [(ngModel)]=\"purgeEventLogs\">\r\n                    <span class=\"form-checkbox-label\">Purge event logs older than</span>\r\n                </label>\r\n                <input type=\"number\" class=\"form-input always-enable-label\" [(ngModel)]=\"purgeEventLogsDays\" [disabled]=\"!purgeEventLogs\">\r\n                <label class=\"form-label\">days</label>\r\n            </div>\r\n\r\n            <div class=\"options\">\r\n                <div class=\"form-group\">\r\n                    <label class=\"form-label\">\r\n                        <input type=\"checkbox\"  class=\"form-checkbox\" name=\"purgeTaskLogs\" [(ngModel)]=\"purgeTaskLogs\" [disabled]=\"!purgeEventLogs\">\r\n                        <span class=\"form-checkbox-label\">Purge task logs as well</span>\r\n                    </label>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"form-group inline\">\r\n                <label class=\"form-label\">\r\n                    <input type=\"checkbox\" class=\"form-checkbox\" name=\"logWindowsEvent\" [(ngModel)]=\"logWindowsEvent\">\r\n                    <span class=\"form-checkbox-label\">Log Windows event when</span>\r\n                </label>\r\n                <div class=\"form-select-wrapper\">\r\n                    <select class=\"form-select always-enable-label\" name=\"logWindowsEventCondition\" [(ngModel)]=\"logWindowsEventCondition\" [disabled]=\"!logWindowsEvent\">\r\n                        @for (option of logWindowsEventOptions; track option) {\r\n                            <option [value]=\"option\">{{ option }}</option>\r\n                        }\r\n                    </select>\r\n                </div>\r\n                <label class=\"form-label\">days</label>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"section\">\r\n        <div class=\"header\">\r\n            <fa-icon icon=\"rotate\"></fa-icon>\r\n            <h3>Hyper Standby</h3>\r\n        </div>\r\n        <div class=\"body\">\r\n            <div class=\"form-group inline\">\r\n                <label for=\"\" class=\"form-label\">Run</label>\r\n                <input type=\"number\" class=\"form-input\" [(ngModel)]=\"hyperStandbyTasks\">\r\n                <label for=\"\" class=\"form-label\">Hyper Standby Task(s) simultaneously</label>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"section\">\r\n        <div class=\"header\">\r\n            <fa-icon icon=\"copy\"></fa-icon>\r\n            <h3>Hyper Replication</h3>\r\n        </div>\r\n        <div class=\"body\">\r\n            <div class=\"form-group inline\">\r\n                <label class=\"form-label\">Run</label>\r\n                <input type=\"number\" class=\"form-input\" [(ngModel)]=\"hyperReplicationTasks\">\r\n                <label for=\"\" class=\"form-label\">Hyper Replication Task(s) simultaneously</label>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"section\">\r\n        <div class=\"header\">\r\n            <fa-icon icon=\"chart-simple\"></fa-icon>\r\n            <h3>Default performance</h3>\r\n        </div>\r\n        <div class=\"body\">\r\n            <div class=\"form-group inline\">\r\n                <label class=\"form-label\">\r\n                    <input type=\"checkbox\" class=\"form-checkbox\" [(ngModel)]=\"limitCpuLoad\">\r\n                    <span class=\"form-checkbox-label\">Limit maximum CPU load</span>\r\n                </label>\r\n                <input type=\"number\" class=\"form-input always-enable-label\" [(ngModel)]=\"maxCpuLoad\" [disabled]=\"!limitCpuLoad\">\r\n                <label for=\"\" class=\"form-label\">%</label>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"section\">\r\n        <div class=\"header\">\r\n            <fa-icon icon=\"hard-drive\"></fa-icon>\r\n            <h3>Disk Scan</h3>\r\n        </div>\r\n        <div class=\"body\">\r\n            <div class=\"form-group inline\">\r\n                <label class=\"form-label\">\r\n                    <input type=\"checkbox\" class=\"form-checkbox\" [(ngModel)]=\"limitDiskScan\">\r\n                    <span class=\"form-checkbox-label\">Limit number of disks to scan to</span>\r\n                </label>\r\n                <input type=\"number\" class=\"form-input always-enable-label\" [(ngModel)]=\"maxDisksToScan\" [disabled]=\"!limitDiskScan\">\r\n                <label for=\"\" class=\"form-label\">disks</label>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n\r\n<footer>\r\n    <span class=\"notice\">0 Configuration changes waiting to be applied</span>\r\n    <div class=\"buttons\">\r\n        <button type=\"button\" class=\"button secondary\" (click)=\"onCancel()\">Cancel</button>\r\n        <button type=\"button\" class=\"button primary\" (click)=\"onApply()\">Apply</button>\r\n    </div>\r\n</footer>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwC4B,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAyB,IAAA,iBAAA,CAAA;AAAY,IAAA,uBAAA;;;;AAA7B,IAAA,qBAAA,SAAA,SAAA;AAAiB,IAAA,oBAAA;AAAA,IAAA,4BAAA,SAAA;;;AD9B/C,IAAO,2BAAP,MAAO,0BAAwB;;EAEjC,iBAAiB;EACjB,qBAAqB;EACrB,gBAAgB;EAChB,kBAAkB;EAClB,2BAA2B;EAC3B,oBAAoB;EACpB,wBAAwB;EACxB,eAAe;EACf,aAAa;EACb,gBAAgB;EAChB,iBAAiB;EAEjB,yBAAyB;IACrB;IACA;IACA;;EAGJ,UAAO;AAEH,YAAQ,IAAI,kBAAkB;EAClC;EAEA,WAAQ;AAEJ,YAAQ,IAAI,oBAAoB;EACpC;;qCA5BS,2BAAwB;EAAA;yEAAxB,2BAAwB,WAAA,CAAA,CAAA,sBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,SAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,QAAA,oBAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,QAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,QAAA,YAAA,QAAA,kBAAA,GAAA,iBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,QAAA,UAAA,GAAA,cAAA,uBAAA,GAAA,iBAAA,WAAA,UAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,QAAA,YAAA,QAAA,iBAAA,GAAA,iBAAA,GAAA,iBAAA,WAAA,UAAA,GAAA,CAAA,QAAA,YAAA,QAAA,mBAAA,GAAA,iBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,QAAA,4BAAA,GAAA,eAAA,uBAAA,GAAA,iBAAA,WAAA,UAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,QAAA,GAAA,CAAA,OAAA,IAAA,GAAA,YAAA,GAAA,CAAA,QAAA,UAAA,GAAA,cAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,MAAA,GAAA,CAAA,QAAA,cAAA,GAAA,CAAA,QAAA,YAAA,GAAA,iBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,YAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,aAAA,GAAA,OAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,WAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,kCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACVrC,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,OAAA,CAAA;AAEA,MAAA,oBAAA,GAAA,WAAA,CAAA;AACJ,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,kBAAA;AAAgB,MAAA,uBAAA,EAAK;AAG7B,MAAA,yBAAA,GAAA,SAAA,EAAS,GAAA,OAAA,CAAA,EACgB,GAAA,OAAA,CAAA;AAEb,MAAA,oBAAA,GAAA,WAAA,CAAA;AACA,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA,EAAK;AAEtB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAkB,IAAA,OAAA,CAAA,EACiB,IAAA,SAAA,CAAA,EACD,IAAA,SAAA,CAAA;AAC6C,MAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,gBAAA,MAAA,MAAA,IAAA,iBAAA;AAAA,eAAA;MAAA,CAAA;AAAnE,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,QAAA,CAAA;AAAkC,MAAA,iBAAA,IAAA,6BAAA;AAA2B,MAAA,uBAAA,EAAO;AAExE,MAAA,yBAAA,IAAA,SAAA,EAAA;AAA4D,MAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,oBAAA,MAAA,MAAA,IAAA,qBAAA;AAAA,eAAA;MAAA,CAAA;AAA5D,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,SAAA,CAAA;AAA0B,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA,EAAQ;AAG1C,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAqB,IAAA,OAAA,EAAA,EACO,IAAA,SAAA,CAAA,EACM,IAAA,SAAA,EAAA;AAC6C,MAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,eAAA,MAAA,MAAA,IAAA,gBAAA;AAAA,eAAA;MAAA,CAAA;AAAnE,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,QAAA,CAAA;AAAkC,MAAA,iBAAA,IAAA,yBAAA;AAAuB,MAAA,uBAAA,EAAO,EAC5D,EACN;AAGV,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA+B,IAAA,SAAA,CAAA,EACD,IAAA,SAAA,EAAA;AAC8C,MAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,iBAAA,MAAA,MAAA,IAAA,kBAAA;AAAA,eAAA;MAAA,CAAA;AAApE,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,QAAA,CAAA;AAAkC,MAAA,iBAAA,IAAA,wBAAA;AAAsB,MAAA,uBAAA,EAAO;AAEnE,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAiC,IAAA,UAAA,EAAA;AACmD,MAAA,2BAAA,iBAAA,SAAA,mEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,0BAAA,MAAA,MAAA,IAAA,2BAAA;AAAA,eAAA;MAAA,CAAA;AAC5E,MAAA,2BAAA,IAAA,0CAAA,GAAA,GAAA,UAAA,IAAA,mCAAA;AAGJ,MAAA,uBAAA,EAAS;AAEb,MAAA,yBAAA,IAAA,SAAA,CAAA;AAA0B,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA,EAAQ,EACpC,EACJ;AAGV,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,OAAA,CAAA;AAEb,MAAA,oBAAA,IAAA,WAAA,EAAA;AACA,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA,EAAK;AAE1B,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAkB,IAAA,OAAA,CAAA,EACiB,IAAA,SAAA,EAAA;AACM,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACpC,MAAA,yBAAA,IAAA,SAAA,EAAA;AAAwC,MAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,mBAAA,MAAA,MAAA,IAAA,oBAAA;AAAA,eAAA;MAAA,CAAA;AAAxC,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,SAAA,EAAA;AAAiC,MAAA,iBAAA,IAAA,sCAAA;AAAoC,MAAA,uBAAA,EAAQ,EAC3E,EACJ;AAGV,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,OAAA,CAAA;AAEb,MAAA,oBAAA,IAAA,WAAA,EAAA;AACA,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA,EAAK;AAE9B,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAkB,IAAA,OAAA,CAAA,EACiB,IAAA,SAAA,CAAA;AACD,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,SAAA,EAAA;AAAwC,MAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,uBAAA,MAAA,MAAA,IAAA,wBAAA;AAAA,eAAA;MAAA,CAAA;AAAxC,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,SAAA,EAAA;AAAiC,MAAA,iBAAA,IAAA,0CAAA;AAAwC,MAAA,uBAAA,EAAQ,EAC/E,EACJ;AAGV,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,OAAA,CAAA;AAEb,MAAA,oBAAA,IAAA,WAAA,EAAA;AACA,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,qBAAA;AAAmB,MAAA,uBAAA,EAAK;AAEhC,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAkB,IAAA,OAAA,CAAA,EACiB,IAAA,SAAA,CAAA,EACD,IAAA,SAAA,EAAA;AACuB,MAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,cAAA,MAAA,MAAA,IAAA,eAAA;AAAA,eAAA;MAAA,CAAA;AAA7C,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,QAAA,CAAA;AAAkC,MAAA,iBAAA,IAAA,wBAAA;AAAsB,MAAA,uBAAA,EAAO;AAEnE,MAAA,yBAAA,IAAA,SAAA,EAAA;AAA4D,MAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,YAAA,MAAA,MAAA,IAAA,aAAA;AAAA,eAAA;MAAA,CAAA;AAA5D,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,SAAA,EAAA;AAAiC,MAAA,iBAAA,IAAA,GAAA;AAAC,MAAA,uBAAA,EAAQ,EACxC,EACJ;AAGV,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,OAAA,CAAA;AAEb,MAAA,oBAAA,IAAA,WAAA,EAAA;AACA,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA,EAAK;AAEtB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAkB,IAAA,OAAA,CAAA,EACiB,IAAA,SAAA,CAAA,EACD,IAAA,SAAA,EAAA;AACuB,MAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,eAAA,MAAA,MAAA,IAAA,gBAAA;AAAA,eAAA;MAAA,CAAA;AAA7C,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,QAAA,CAAA;AAAkC,MAAA,iBAAA,IAAA,kCAAA;AAAgC,MAAA,uBAAA,EAAO;AAE7E,MAAA,yBAAA,IAAA,SAAA,EAAA;AAA4D,MAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,gBAAA,MAAA,MAAA,IAAA,iBAAA;AAAA,eAAA;MAAA,CAAA;AAA5D,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,SAAA,EAAA;AAAiC,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAQ,EAC5C,EACJ,EACJ;AAGV,MAAA,yBAAA,IAAA,QAAA,EAAQ,IAAA,QAAA,EAAA;AACiB,MAAA,iBAAA,IAAA,+CAAA;AAA6C,MAAA,uBAAA;AAClE,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAqB,IAAA,UAAA,EAAA;AAC8B,MAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,eAAS,IAAA,SAAA;MAAU,CAAA;AAAE,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC1E,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA6C,MAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,eAAS,IAAA,QAAA;MAAS,CAAA;AAAE,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAS,EAC7E;;;AArG6E,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,cAAA;AAGX,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,kBAAA;AAAiC,MAAA,qBAAA,YAAA,CAAA,IAAA,cAAA;AAOlB,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,aAAA;AAA4B,MAAA,qBAAA,YAAA,CAAA,IAAA,cAAA;AAQ/B,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,eAAA;AAIY,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,wBAAA;AAAuC,MAAA,qBAAA,YAAA,CAAA,IAAA,eAAA;AACnH,MAAA,oBAAA;AAAA,MAAA,qBAAA,IAAA,sBAAA;AAkBgC,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,iBAAA;AAcA,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,qBAAA;AAcS,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,YAAA;AAGW,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,UAAA;AAAyB,MAAA,qBAAA,YAAA,CAAA,IAAA,YAAA;AAcpC,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,aAAA;AAGW,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,cAAA;AAA6B,MAAA,qBAAA,YAAA,CAAA,IAAA,aAAA;;oBDnG3F,mBAAiB,iBAAE,aAAW,gBAAA,8BAAA,sBAAA,qBAAA,8BAAA,4BAAA,iBAAA,OAAA,GAAA,QAAA,CAAA,62FAAA,EAAA,CAAA;;;sEAI/B,0BAAwB,CAAA;UANpC;uBACa,wBAAsB,SACvB,CAAC,mBAAmB,WAAW,GAAC,UAAA,wrKAAA,QAAA,CAAA,u+DAAA,EAAA,CAAA;;;;6EAIhC,0BAAwB,EAAA,WAAA,4BAAA,UAAA,+EAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}