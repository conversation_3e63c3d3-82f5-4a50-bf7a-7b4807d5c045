{"version": 3, "sources": ["renderer/src/app/preferences/proxy/proxy.component.ts", "renderer/src/app/preferences/proxy/proxy.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\n\r\n@Component({\r\n    selector: 'app-proxy',\r\n    imports: [FontAwesomeModule],\r\n    templateUrl: './proxy.component.html',\r\n    styleUrl: './proxy.component.less'\r\n})\r\nexport class ProxyComponent {\r\n}\r\n", "<header>\r\n    <div class=\"icon\">\r\n        <fa-icon icon=\"shield-alt\"></fa-icon>\r\n    </div>\r\n    <h2>Proxy Settings</h2>\r\n</header>\r\n\r\n<section>\r\n    <p>Proxy settings will be implemented here.</p>\r\n</section>\r\n\r\n<footer>\r\n    <span class=\"notice\">0 Configuration changes waiting to be applied</span>\r\n    <div class=\"buttons\">\r\n        <button type=\"button\" class=\"button secondary\">Cancel</button>\r\n        <button type=\"button\" class=\"button primary\">Apply</button>\r\n    </div>\r\n</footer>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AASM,IAAO,iBAAP,MAAO,gBAAc;;qCAAd,iBAAc;EAAA;yEAAd,iBAAc,WAAA,CAAA,CAAA,WAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,YAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,WAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,SAAA,CAAA,GAAA,UAAA,SAAA,wBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACT3B,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,OAAA,CAAA;AAEA,MAAA,oBAAA,GAAA,WAAA,CAAA;AACJ,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,gBAAA;AAAc,MAAA,uBAAA,EAAK;AAG3B,MAAA,yBAAA,GAAA,SAAA,EAAS,GAAA,GAAA;AACF,MAAA,iBAAA,GAAA,0CAAA;AAAwC,MAAA,uBAAA,EAAI;AAGnD,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,QAAA,CAAA;AACiB,MAAA,iBAAA,IAAA,+CAAA;AAA6C,MAAA,uBAAA;AAClE,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,UAAA,CAAA;AAC8B,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AACrD,MAAA,yBAAA,IAAA,UAAA,CAAA;AAA6C,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAS,EACzD;;oBDXI,mBAAiB,eAAA,GAAA,QAAA,CAAA,m9CAAA,EAAA,CAAA;;;sEAIlB,gBAAc,CAAA;UAN1B;uBACa,aAAW,SACZ,CAAC,iBAAiB,GAAC,UAAA,qhBAAA,QAAA,CAAA,4rCAAA,EAAA,CAAA;;;;6EAInB,gBAAc,EAAA,WAAA,kBAAA,UAAA,yDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}