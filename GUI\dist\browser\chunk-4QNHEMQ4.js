import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-TBQAZTY7.js";

// renderer/src/app/tools/tools.component.ts
var ToolsComponent = class _ToolsComponent {
  static \u0275fac = function ToolsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ToolsComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ToolsComponent, selectors: [["app-tools"]], decls: 2, vars: 0, template: function ToolsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "p");
      \u0275\u0275text(1, "tools works!");
      \u0275\u0275elementEnd();
    }
  }, encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ToolsComponent, [{
    type: Component,
    args: [{ selector: "app-tools", imports: [], template: "<p>tools works!</p>\r\n" }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ToolsComponent, { className: "ToolsComponent", filePath: "renderer/src/app/tools/tools.component.ts", lineNumber: 9 });
})();
export {
  ToolsComponent
};
//# sourceMappingURL=chunk-4QNHEMQ4.js.map
