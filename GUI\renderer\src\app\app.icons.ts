import { IconDefinition, IconPrefix, IconName } from "@fortawesome/angular-fontawesome";
import {
    faGauge,
    faTimeline,
    faCalendarCheck,
    faRotate,
    faGear,
    faWrench,
    faServer,
    faShieldHalved,
    faBoxArchive,
    faArrowUp,
    faClockRotateLeft,
    faDatabase,
    faFile,
    faPlus,
    faRotateLeft,
    faCopy,
    faDesktop,
    faChartLine,
    faCloud,
    faNetworkWired,
    faCalendar,
    faCalendarWeek,
    faCalendarDay,
    faFolder,
    faCube,
    faTape,
    faHome,
    faQuestionCircle,
    faRepeat,
    faCalendarTimes,
    faArchive,
    faLayerGroup,
    faLocationDot,
    faClock,
    faLaptop,
    faCog,
    faHardDrive,
    faGears,
    faPowerOff,
    faArrowsMinimize,
    faPlay,
    faInfo,
    faSliders,
    faGlobe,
    faBell,
    faShield,
    faExclamationTriangle,
    faRocket,
    faExternalLinkAlt,
    faUndoAlt,
    faLifeRing,
    faHistory,
    faBoot,
    faCalendarLinesPen,
    faChartSimple,
    faSquareTerminal,
    fa1,
    faChevronDown,
    faFilter,
    // New icons for detail view
    faArrowLeft,
    faArrowRight,
    faRotateRight,
    faCheck,
    faEllipsis,
    faUndo,
    // New icons for replication
    faComputer,
    faBullseye,
    faList,
} from "@fortawesome/pro-solid-svg-icons";
import {
    faWindows,
    faAws,
    faMicrosoft
} from "@fortawesome/free-brands-svg-icons";

const faCustomHypervisor: IconDefinition = {
    prefix: 'fac' as IconPrefix,
    iconName: 'custom-hypervisor' as IconName,
    icon: [
        52, // width
        44, // height
        [], // ligatures
        '', // unicode
        // SVG path data
        'M14.64 10c.147.539.618.942 1.183 1.057l1.996.412q.206.537.478 1.037a1.5 1.5 0 0 1-1.11.494h-7.53v8.333h3.714c1.641 0 2.972.781 2.972 1.744v19.179c0 .963-1.33 1.744-2.972 1.744h-10.4C1.332 44 0 43.219 0 42.256V23.077c0-.963 1.33-1.744 2.972-1.744h3.714v-10.46c0-.482.665-.873 1.486-.873zM8 37a2 2 0 1 0 0 4 2 2 0 0 0 0-4m-3.437-7a1.5 1.5 0 0 0 0 3h7a1.5 1.5 0 0 0 0-3zm0-5.25a1.5 1.5 0 0 0 0 3h7a1.5 1.5 0 0 0 0-3zm22.922-7.786v4.369H31.2c1.641 0 2.972.781 2.972 1.744v19.179c0 .963-1.33 1.744-2.972 1.744H20.8c-1.641 0-2.972-.781-2.972-1.744V23.077c0-.963 1.33-1.744 2.972-1.744h3.715v-4.27l.095-.106a9.1 9.1 0 0 0 2.869 0zM26 37a2 2 0 1 0 0 4 2 2 0 0 0 0-4m-3.5-7a1.5 1.5 0 0 0 0 3h7a1.5 1.5 0 0 0 0-3zm0-5.25a1.5 1.5 0 0 0 0 3h7a1.5 1.5 0 0 0 0-3zM43.828 10c.82 0 1.486.39 1.486.872v10.461h3.714c1.641 0 2.972.781 2.972 1.744v19.179C52 43.219 50.67 44 49.028 44h-10.4c-1.64 0-2.97-.781-2.97-1.744V23.077c0-.963 1.33-1.744 2.97-1.744h3.715V13h-7.53c-.412 0-.783-.166-1.054-.434a8 8 0 0 0 .504-1.084l1.996-.412c.576-.116 1.045-.524 1.186-1.07zM44 37a2 2 0 1 0 0 4 2 2 0 0 0 0-4m-3.5-7a1.5 1.5 0 0 0 0 3h7a1.5 1.5 0 0 0 0-3zm0-5.25a1.5 1.5 0 0 0 0 3h7a1.5 1.5 0 0 0 0-3zM28.26 16.085a1.23 1.23 0 0 0 1.365.328q.436-.181.846-.402l.295-.163q.41-.24.797-.518c.414-.3.55-.828.387-1.303l-.508-1.489a6.6 6.6 0 0 0 1.141-1.91l1.589-.323c.507-.1.911-.48.971-.976a8.8 8.8 0 0 0 0-2.154c-.06-.496-.47-.876-.971-.976l-1.589-.328a6.7 6.7 0 0 0-1.14-1.91l.507-1.489c.164-.475.027-1.003-.387-1.304a11 11 0 0 0-.797-.522l-.29-.159a9 9 0 0 0-.851-.4C29.15-.11 28.61.038 28.26.413l-1.08 1.171a7.4 7.4 0 0 0-2.282 0l-1.08-1.17a1.23 1.23 0 0 0-1.365-.328q-.434.182-.851.401l-.284.159a8 8 0 0 0-.797.522c-.415.301-.551.83-.388 1.304l.508 1.489a6.6 6.6 0 0 0-1.14 1.91l-1.589.317c-.508.1-.912.48-.971.977a8.8 8.8 0 0 0 0 2.153c.06.497.469.877.971.977l1.588.322a6.7 6.7 0 0 0 1.141 1.91l-.508 1.49c-.163.474-.027 1.002.388 1.303.256.185.518.359.797.517l.295.164q.41.22.846.401c.474.195 1.015.047 1.364-.327l1.08-1.172a7.4 7.4 0 0 0 2.282 0l1.08 1.172zM26.04 5.713c.344 0 .685.066 1.003.193s.606.314.85.55c.243.234.436.514.567.821a2.46 2.46 0 0 1 0 1.94 2.5 2.5 0 0 1-.567.821 2.6 2.6 0 0 1-.85.55 2.7 2.7 0 0 1-2.006 0 2.6 2.6 0 0 1-.85-.55 2.5 2.5 0 0 1-.567-.822 2.46 2.46 0 0 1 0-1.939 2.5 2.5 0 0 1 .567-.822c.244-.235.533-.422.85-.549a2.7 2.7 0 0 1 1.003-.193'
    ]
};

export const icons: IconDefinition[] = [
    // Solid icons
    faGauge,
    faTimeline,
    faCalendarCheck,
    faServer,
    faRotate,
    faShieldHalved,
    faGear,
    faWrench,
    faDatabase,
    faBoxArchive,
    faArrowUp,
    faClockRotateLeft,
    faDatabase,
    faFile,
    faPlus,
    faRotateLeft,
    faPowerOff,
    faCopy,
    faDesktop,
    faChartLine,
    faCloud,
    faNetworkWired,
    faCalendar,
    faCalendarWeek,
    faCalendarDay,
    faFolder,
    faCube,
    faTape,
    faHome,
    faQuestionCircle,
    faRepeat,
    faCalendarTimes,
    faArchive,
    faLayerGroup,
    faLocationDot,
    faClock,
    faLaptop,
    faCog,
    faHardDrive,
    faGears,
    faArrowsMinimize,
    faPlay,
    faInfo,
    faSliders,
    faGlobe,
    faBell,
    faShield,
    faExclamationTriangle,
    faRocket,
    faExternalLinkAlt,
    faUndoAlt,
    faLifeRing,
    faHistory,
    faBoot,
    faCalendarLinesPen,
    faChartSimple,
    faSquareTerminal,
    fa1,
    faChevronDown,
    faFilter,
    // New icons for detail view
    faArrowLeft,
    faArrowRight,
    faRotateRight,
    faCheck,
    faEllipsis,
    faUndo,
    // New icons for replication
    faComputer,
    faBullseye,
    faList,

    // Brands icons
    faWindows,
    faAws,
    faMicrosoft,

    // Regular icons

    // Light icons

    // Thin icons

    // Custom icons
    faCustomHypervisor,
];
