@import '../../../../styles/variables.less';
@import '../../../../styles/mixins.less';

:host {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.75rem;
    overflow: hidden;
    cursor: pointer;
    padding: 1rem;

    // Panel Header
    header {
        flex: 0 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        padding-bottom: 1rem;

        h2 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
            line-height: 1.125rem;
            color: @text-color;
        }

        .icon-container {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0);
            padding: 0;
        }
    }

    // Collapsed vertical panel styling
    &.collapsed {
        header {
            writing-mode: vertical-lr;
            text-orientation: mixed;
            padding: 0;
            height: 100%;
            flex-direction: row-reverse;
            justify-content: flex-end;

            h2 {
                font-size: 1rem;
                font-weight: 600;
                line-height: 1.2;
                text-align: center;
                margin: 0;
                writing-mode: vertical-lr;
                text-orientation: mixed;
                color: @text-color;
                letter-spacing: -0.02em;
                transform: rotate(180deg);
            }

            .icon-container {
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                border-radius: 6px;
                background: rgba(255, 255, 255, 0);
                padding: 0;
            }
        }

        section {
            display: none;
        }
    }

    section {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;

        // Hide scrollbar for Chromium/WebKit (Electron)
        &::-webkit-scrollbar {
            display: none;
        }

        .item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
            cursor: pointer;
            overflow: hidden;

            .transition(background, border-color, box-shadow);

            &:hover {
                background: rgba(255, 255, 255, 0.08);
                border-color: rgba(255, 255, 255, 0.2);
            }

            &.selected {
                background: rgba(255, 255, 255, 0.1);
                border-color: @primary-color;
                box-shadow: 0 0 0 2px rgba(239, 78, 56, 0.3);

                .item-icon {
                    color: @primary-color;
                }
            }

            .item-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 3.25rem;
                height: 2.75rem;
                font-size: 3rem;
                color: @text-secondary;

                .transition(color);
            }

            .item-content {
                flex: 1;

                h3 {
                    font-size: 1rem;
                    font-weight: 500;
                    color: @text-color;
                    margin: 0 0 0.5rem 0;
                    line-height: 1.3;
                }

                p {
                    font-size: 0.8125rem;
                    color: @text-secondary;
                    margin: 0;
                    line-height: 1.4;
                }
            }
        }
    }
}
