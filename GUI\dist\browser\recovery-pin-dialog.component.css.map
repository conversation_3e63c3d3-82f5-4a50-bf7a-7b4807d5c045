{"version": 3, "sources": ["renderer/src/app/recovery/shared/recovery-source/recovery-pin-dialog/recovery-pin-dialog.component.less"], "sourcesContent": ["/*\nOur team follows strict coding standards. Please ensure all generated code adheres to the following rules:\n\n- Use **4 spaces for indentation**, no tabs.\n- All **code comments must be in English**.\n- Avoid any **unnecessary or redundant syntax**.\n- Code must be **clean, idiomatic**, and follow modern best practices.\n- We are building an application with **Angular 20 (renderer)** and **Electron 36 (main process)**.\n- In Angular templates, **do not use deprecated syntax** such as `*ngIf` or `*ngFor`. Instead, use the **new control flow syntax** like `@if`, `@for` as recommended by the latest Angular Style Guide.\n- Follow principles of **modularity, readability**, and **maintainability**.\n\nAlways write production-ready, modern, and maintainable code.\n*/\n:host {\n    display: block;\n    background: #2e4a5a;\n    border-radius: .75rem; // 12px in rem\n    padding: 1rem; // 16px in rem\n    width: 32rem; // 512px in rem\n    border: 1px solid rgba(16, 185, 129, 0.5);\n}\n\n.dialog-container {\n    display: flex;\n    flex-direction: column;\n    gap: 1rem; // 16px in rem\n}\n\nh2 {\n    font-family: Inter, sans-serif;\n    font-size: 1.125rem; // 18px in rem\n    font-weight: 400;\n    margin: 0;\n    color: #ffffff;\n}\n\np {\n    font-family: 'Noto Sans', sans-serif;\n    font-size: 0.875rem; // 14px in rem\n    font-weight: 300;\n    color: #ffffff;\n    margin: 0;\n    line-height: 1.4;\n}\n\n.pin-input-grid {\n    display: grid;\n    grid-template-rows: 1fr 1fr;\n    gap: 1rem; // 16px in rem\n    margin: 1rem 0;\n}\n\n.pin-input-row {\n    display: flex;\n    gap: 1rem; // 16px in rem\n    align-items: center;\n    justify-content: center;\n\n    input {\n        width: 4.25rem; // Adjusted width to fill space\n        height: 1.8125rem; // 29px in rem\n        text-align: left;\n        padding: .3125rem .625rem; // 5px in rem\n        font-family: Inter, sans-serif;\n        font-size: 0.75rem; // 12px in rem\n        font-weight: 500;\n        border-radius: .25rem; // 4px in rem\n        border: 0;\n        background-color: rgba(255, 255, 255, 0.2);\n        color: #ffffff;\n\n        &::placeholder {\n            color: rgba(255, 255, 255, 0.5);\n        }\n\n        &:focus {\n            outline: none;\n            border-color: #3895d3;\n        }\n    }\n\n    .separator {\n        font-family: 'Noto Sans', sans-serif;\n        font-size: 1rem; // 16px in rem\n        font-weight: 500;\n        color: #ffffff;\n    }\n}\n\n[cdk-dialog-actions] {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: 16px;\n\n    button {\n        padding: 8px 12px;\n        border-radius: 8px;\n        border: 1px solid transparent;\n        font-family: 'Noto Sans', sans-serif;\n        font-size: 14px;\n        cursor: pointer;\n\n        &.secondary {\n            background-color: rgba(255, 255, 255, 0.1);\n            border: 1px solid rgba(255, 255, 255, 0.2);\n            color: #ffffff;\n        }\n\n        &:disabled {\n            background-color: rgba(255, 255, 255, 0.1);\n            color: rgba(255, 255, 255, 0.5);\n            cursor: not-allowed;\n            border: 1px solid transparent;\n        }\n\n        &:not(:disabled):not(.secondary) {\n            background-color: #3895d3; // A default primary color\n            color: #ffffff;\n        }\n    }\n}\n"], "mappings": ";AAaA;AACI,WAAA;AACA,cAAA;AACA,iBAAA;AACA,WAAA;AACA,SAAA;AACA,UAAA,IAAA,MAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;;AAGJ;AACI,eAAA,KAAA,EAAA;AACA,aAAA;AACA,eAAA;AACA,UAAA;AACA,SAAA;;AAGJ;AACI,eAAa,WAAA,EAAA;AACb,aAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA;AACA,eAAA;;AAGJ,CAAA;AACI,WAAA;AACA,sBAAA,IAAA;AACA,OAAA;AACA,UAAA,KAAA;;AAGJ,CAAA;AACI,WAAA;AACA,OAAA;AACA,eAAA;AACA,mBAAA;;AAJJ,CAAA,cAMI;AACI,SAAA;AACA,UAAA;AACA,cAAA;AACA,WAAA,UAAA;AACA,eAAA,KAAA,EAAA;AACA,aAAA;AACA,eAAA;AACA,iBAAA;AACA,UAAA;AACA,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,SAAA;;AAEA,CAnBR,cAMI,KAaK;AACG,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGJ,CAvBR,cAMI,KAiBK;AACG,WAAA;AACA,gBAAA;;AAzBZ,CAAA,cA6BI,CAAA;AACI,eAAa,WAAA,EAAA;AACb,aAAA;AACA,eAAA;AACA,SAAA;;AAIR,CAAA;AACI,WAAA;AACA,yBAAA,IAAA;AACA,OAAA;;AAHJ,CAAA,oBAKI;AACI,WAAA,IAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA;AACA,eAAa,WAAA,EAAA;AACb,aAAA;AACA,UAAA;;AAEA,CAAA,oBARJ,MAQK,CAAA;AACG,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA,IAAA,MAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,SAAA;;AAGJ,CAAA,oBAdJ,MAcK;AACG,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;AACA,UAAA,IAAA,MAAA;;AAGJ,CAAA,oBArBJ,MAqBK,KAAI,UAAW,KAAI,CAbnB;AAcG,oBAAA;AACA,SAAA;;", "names": []}