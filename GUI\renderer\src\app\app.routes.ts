import { Routes } from '@angular/router';

export const routes: Routes = [
    {
        path: '',
        pathMatch: 'full',
        redirectTo: 'dashboard'
    },
    {
        path: 'dashboard',
        loadComponent: () => import('./dashboard/dashboard.component').then(m => m.DashboardComponent)
    },
    {
        path: 'activities',
        loadComponent: () => import('./activities/activities.component').then(m => m.ActivitiesComponent)
    },
    {
        path: 'tasks',
        loadComponent: () => import('./tasks/tasks.component').then(m => m.TasksComponent)
    },
    {
        path: 'backup',
        loadComponent: () => import('./backup/backup.component').then(m => m.BackupComponent)
    },
    {
        path: 'standby',
        loadComponent: () => import('./standby/standby.component').then(m => m.StandbyComponent)
    },
    {
        path: 'replication',
        loadComponent: () => import('./replication/replication.component').then(m => m.ReplicationComponent)
    },
    {
        path: 'recovery',
        loadComponent: () => import('./recovery/recovery.component').then(m => m.RecoveryComponent)
    },
    {
        path: 'preferences',
        loadComponent: () => import('./preferences/preferences.component').then(m => m.PreferencesComponent),
        children: [
            {
                path: '',
                redirectTo: 'general',
                pathMatch: 'full'
            },
            {
                path: 'general',
                loadComponent: () => import('./preferences/general-settings/general-settings.component').then(m => m.GeneralSettingsComponent)
            },
            {
                path: 'proxy',
                loadComponent: () => import('./preferences/proxy/proxy.component').then(m => m.ProxyComponent)
            },
            {
                path: 'notifications',
                loadComponent: () => import('./preferences/notifications/notifications.component').then(m => m.NotificationsComponent)
            },
            {
                path: 'backup',
                loadComponent: () => import('./preferences/backup/backup.component').then(m => m.BackupComponent)
            },
            {
                path: 'alert',
                loadComponent: () => import('./preferences/alert/alert.component').then(m => m.AlertComponent)
            },
            {
                path: 'console',
                loadComponent: () => import('./preferences/console/console.component').then(m => m.ConsoleComponent)
            },
            {
                path: 'hyperback',
                loadComponent: () => import('./preferences/hyperback/hyperback.component').then(m => m.HyperbackComponent)
            },
            {
                path: 'online-portal',
                loadComponent: () => import('./preferences/online-portal/online-portal.component').then(m => m.OnlinePortalComponent)
            },
            {
                path: 'quick-recovery',
                loadComponent: () => import('./preferences/quick-recovery/quick-recovery.component').then(m => m.QuickRecoveryComponent)
            },
            {
                path: 'rescue-boot',
                loadComponent: () => import('./preferences/rescue-boot/rescue-boot.component').then(m => m.RescueBootComponent)
            },
        ]
    },
    {
        path: 'tools',
        loadComponent: () => import('./tools/tools.component').then(m => m.ToolsComponent)
    },
    {
        path: '**',
        redirectTo: 'dashboard'
    }
];
