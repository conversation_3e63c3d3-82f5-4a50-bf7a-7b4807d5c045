@import "../../../styles/variables.less";
@import "../../../styles/mixins.less";

:host {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 1rem;
    overflow: hidden;
    padding: 16px;
    border-radius: @border-radius-small;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);

    .animation(fade-in-up);

    header {
        display: flex;
        align-items: center;
        gap: .5rem;
        border-radius: @border-radius-small;

        .icon {
            font-size: 1rem;
        }

        h2 {
            margin: 0;
            font-family: @font-family;
            font-weight: 500;
            font-size: 1rem;
            line-height: 1.25;
            color: @text-color;
        }
    }

    section {
        flex: 1;
        font-size: .875rem;
    }

    footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        flex: 0 0 auto;
        gap: 1rem;

        .notice {
            flex: 1;
            font-size: .875rem;
            line-height: 1.36;
            color: @text-color;
        }

        .buttons {
            .button {
                min-width: 12rem;
            }
        }
    }
}
