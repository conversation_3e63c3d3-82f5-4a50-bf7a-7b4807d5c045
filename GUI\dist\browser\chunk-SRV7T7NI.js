import {
  CheckboxControlValueAccessor,
  DefaultV<PERSON>ueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel,
  NgSelectO<PERSON>,
  NumberValueAccessor,
  SelectControlValueAccessor,
  ɵNgSelectMultipleOption
} from "./chunk-QOWQNEHV.js";
import {
  FaIconComponent,
  FontAwesomeModule
} from "./chunk-N34W2DJ7.js";
import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵproperty,
  ɵɵrepeater,
  ɵɵrepeaterCreate,
  ɵɵrepeaterTrackByIdentity,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-TBQAZTY7.js";

// renderer/src/app/preferences/general-settings/general-settings.component.ts
function GeneralSettingsComponent_For_34_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 17);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const option_r1 = ctx.$implicit;
    \u0275\u0275property("value", option_r1);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(option_r1);
  }
}
var GeneralSettingsComponent = class _GeneralSettingsComponent {
  // Form model
  purgeEventLogs = false;
  purgeEventLogsDays = 30;
  purgeTaskLogs = false;
  logWindowsEvent = false;
  logWindowsEventCondition = "Task Succeeded or failed";
  hyperStandbyTasks = 30;
  hyperReplicationTasks = 30;
  limitCpuLoad = false;
  maxCpuLoad = 100;
  limitDiskScan = false;
  maxDisksToScan = 1;
  logWindowsEventOptions = [
    "Task Succeeded",
    "Task Failed",
    "Task Succeeded or failed"
  ];
  onApply() {
    console.log("Settings applied");
  }
  onCancel() {
    console.log("Settings cancelled");
  }
  static \u0275fac = function GeneralSettingsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _GeneralSettingsComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _GeneralSettingsComponent, selectors: [["app-general-settings"]], decls: 97, vars: 16, consts: [[1, "icon"], ["icon", "sliders"], [1, "section"], [1, "header"], ["icon", "calendar-lines-pen"], [1, "body"], [1, "form-group", "inline"], [1, "form-label"], ["type", "checkbox", "name", "purgeEventLogs", 1, "form-checkbox", 3, "ngModelChange", "ngModel"], [1, "form-checkbox-label"], ["type", "number", 1, "form-input", "always-enable-label", 3, "ngModelChange", "ngModel", "disabled"], [1, "options"], [1, "form-group"], ["type", "checkbox", "name", "purgeTaskLogs", 1, "form-checkbox", 3, "ngModelChange", "ngModel", "disabled"], ["type", "checkbox", "name", "logWindowsEvent", 1, "form-checkbox", 3, "ngModelChange", "ngModel"], [1, "form-select-wrapper"], ["name", "logWindowsEventCondition", 1, "form-select", "always-enable-label", 3, "ngModelChange", "ngModel", "disabled"], [3, "value"], ["icon", "rotate"], ["for", "", 1, "form-label"], ["type", "number", 1, "form-input", 3, "ngModelChange", "ngModel"], ["icon", "copy"], ["icon", "chart-simple"], ["type", "checkbox", 1, "form-checkbox", 3, "ngModelChange", "ngModel"], ["icon", "hard-drive"], [1, "notice"], [1, "buttons"], ["type", "button", 1, "button", "secondary", 3, "click"], ["type", "button", 1, "button", "primary", 3, "click"]], template: function GeneralSettingsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "header")(1, "div", 0);
      \u0275\u0275element(2, "fa-icon", 1);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "h2");
      \u0275\u0275text(4, "General Settings");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(5, "section")(6, "div", 2)(7, "div", 3);
      \u0275\u0275element(8, "fa-icon", 4);
      \u0275\u0275elementStart(9, "h3");
      \u0275\u0275text(10, "Event Log");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(11, "div", 5)(12, "div", 6)(13, "label", 7)(14, "input", 8);
      \u0275\u0275twoWayListener("ngModelChange", function GeneralSettingsComponent_Template_input_ngModelChange_14_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.purgeEventLogs, $event) || (ctx.purgeEventLogs = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "span", 9);
      \u0275\u0275text(16, "Purge event logs older than");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(17, "input", 10);
      \u0275\u0275twoWayListener("ngModelChange", function GeneralSettingsComponent_Template_input_ngModelChange_17_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.purgeEventLogsDays, $event) || (ctx.purgeEventLogsDays = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "label", 7);
      \u0275\u0275text(19, "days");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(20, "div", 11)(21, "div", 12)(22, "label", 7)(23, "input", 13);
      \u0275\u0275twoWayListener("ngModelChange", function GeneralSettingsComponent_Template_input_ngModelChange_23_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.purgeTaskLogs, $event) || (ctx.purgeTaskLogs = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(24, "span", 9);
      \u0275\u0275text(25, "Purge task logs as well");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(26, "div", 6)(27, "label", 7)(28, "input", 14);
      \u0275\u0275twoWayListener("ngModelChange", function GeneralSettingsComponent_Template_input_ngModelChange_28_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.logWindowsEvent, $event) || (ctx.logWindowsEvent = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(29, "span", 9);
      \u0275\u0275text(30, "Log Windows event when");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(31, "div", 15)(32, "select", 16);
      \u0275\u0275twoWayListener("ngModelChange", function GeneralSettingsComponent_Template_select_ngModelChange_32_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.logWindowsEventCondition, $event) || (ctx.logWindowsEventCondition = $event);
        return $event;
      });
      \u0275\u0275repeaterCreate(33, GeneralSettingsComponent_For_34_Template, 2, 2, "option", 17, \u0275\u0275repeaterTrackByIdentity);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(35, "label", 7);
      \u0275\u0275text(36, "days");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(37, "div", 2)(38, "div", 3);
      \u0275\u0275element(39, "fa-icon", 18);
      \u0275\u0275elementStart(40, "h3");
      \u0275\u0275text(41, "Hyper Standby");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(42, "div", 5)(43, "div", 6)(44, "label", 19);
      \u0275\u0275text(45, "Run");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(46, "input", 20);
      \u0275\u0275twoWayListener("ngModelChange", function GeneralSettingsComponent_Template_input_ngModelChange_46_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.hyperStandbyTasks, $event) || (ctx.hyperStandbyTasks = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(47, "label", 19);
      \u0275\u0275text(48, "Hyper Standby Task(s) simultaneously");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(49, "div", 2)(50, "div", 3);
      \u0275\u0275element(51, "fa-icon", 21);
      \u0275\u0275elementStart(52, "h3");
      \u0275\u0275text(53, "Hyper Replication");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(54, "div", 5)(55, "div", 6)(56, "label", 7);
      \u0275\u0275text(57, "Run");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(58, "input", 20);
      \u0275\u0275twoWayListener("ngModelChange", function GeneralSettingsComponent_Template_input_ngModelChange_58_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.hyperReplicationTasks, $event) || (ctx.hyperReplicationTasks = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(59, "label", 19);
      \u0275\u0275text(60, "Hyper Replication Task(s) simultaneously");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(61, "div", 2)(62, "div", 3);
      \u0275\u0275element(63, "fa-icon", 22);
      \u0275\u0275elementStart(64, "h3");
      \u0275\u0275text(65, "Default performance");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(66, "div", 5)(67, "div", 6)(68, "label", 7)(69, "input", 23);
      \u0275\u0275twoWayListener("ngModelChange", function GeneralSettingsComponent_Template_input_ngModelChange_69_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.limitCpuLoad, $event) || (ctx.limitCpuLoad = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(70, "span", 9);
      \u0275\u0275text(71, "Limit maximum CPU load");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(72, "input", 10);
      \u0275\u0275twoWayListener("ngModelChange", function GeneralSettingsComponent_Template_input_ngModelChange_72_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.maxCpuLoad, $event) || (ctx.maxCpuLoad = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(73, "label", 19);
      \u0275\u0275text(74, "%");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(75, "div", 2)(76, "div", 3);
      \u0275\u0275element(77, "fa-icon", 24);
      \u0275\u0275elementStart(78, "h3");
      \u0275\u0275text(79, "Disk Scan");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(80, "div", 5)(81, "div", 6)(82, "label", 7)(83, "input", 23);
      \u0275\u0275twoWayListener("ngModelChange", function GeneralSettingsComponent_Template_input_ngModelChange_83_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.limitDiskScan, $event) || (ctx.limitDiskScan = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(84, "span", 9);
      \u0275\u0275text(85, "Limit number of disks to scan to");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(86, "input", 10);
      \u0275\u0275twoWayListener("ngModelChange", function GeneralSettingsComponent_Template_input_ngModelChange_86_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.maxDisksToScan, $event) || (ctx.maxDisksToScan = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(87, "label", 19);
      \u0275\u0275text(88, "disks");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(89, "footer")(90, "span", 25);
      \u0275\u0275text(91, "0 Configuration changes waiting to be applied");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(92, "div", 26)(93, "button", 27);
      \u0275\u0275listener("click", function GeneralSettingsComponent_Template_button_click_93_listener() {
        return ctx.onCancel();
      });
      \u0275\u0275text(94, "Cancel");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(95, "button", 28);
      \u0275\u0275listener("click", function GeneralSettingsComponent_Template_button_click_95_listener() {
        return ctx.onApply();
      });
      \u0275\u0275text(96, "Apply");
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(14);
      \u0275\u0275twoWayProperty("ngModel", ctx.purgeEventLogs);
      \u0275\u0275advance(3);
      \u0275\u0275twoWayProperty("ngModel", ctx.purgeEventLogsDays);
      \u0275\u0275property("disabled", !ctx.purgeEventLogs);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.purgeTaskLogs);
      \u0275\u0275property("disabled", !ctx.purgeEventLogs);
      \u0275\u0275advance(5);
      \u0275\u0275twoWayProperty("ngModel", ctx.logWindowsEvent);
      \u0275\u0275advance(4);
      \u0275\u0275twoWayProperty("ngModel", ctx.logWindowsEventCondition);
      \u0275\u0275property("disabled", !ctx.logWindowsEvent);
      \u0275\u0275advance();
      \u0275\u0275repeater(ctx.logWindowsEventOptions);
      \u0275\u0275advance(13);
      \u0275\u0275twoWayProperty("ngModel", ctx.hyperStandbyTasks);
      \u0275\u0275advance(12);
      \u0275\u0275twoWayProperty("ngModel", ctx.hyperReplicationTasks);
      \u0275\u0275advance(11);
      \u0275\u0275twoWayProperty("ngModel", ctx.limitCpuLoad);
      \u0275\u0275advance(3);
      \u0275\u0275twoWayProperty("ngModel", ctx.maxCpuLoad);
      \u0275\u0275property("disabled", !ctx.limitCpuLoad);
      \u0275\u0275advance(11);
      \u0275\u0275twoWayProperty("ngModel", ctx.limitDiskScan);
      \u0275\u0275advance(3);
      \u0275\u0275twoWayProperty("ngModel", ctx.maxDisksToScan);
      \u0275\u0275property("disabled", !ctx.limitDiskScan);
    }
  }, dependencies: [FontAwesomeModule, FaIconComponent, FormsModule, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, NumberValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, NgControlStatus, NgModel], styles: ['\n\n[_nghost-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: 1rem;\n  overflow: hidden;\n  padding: 16px;\n  border-radius: 0.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  background: rgba(255, 255, 255, 0.1);\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 0ms;\n  animation-name: fadeInUp;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  border-radius: 0.5rem;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\n  font-size: 1rem;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  margin: 0;\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 500;\n  font-size: 1rem;\n  line-height: 1.25;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  flex: 1;\n  font-size: 0.875rem;\n}\n[_nghost-%COMP%]   footer[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  flex: 0 0 auto;\n  gap: 1rem;\n}\n[_nghost-%COMP%]   footer[_ngcontent-%COMP%]   .notice[_ngcontent-%COMP%] {\n  flex: 1;\n  font-size: 0.875rem;\n  line-height: 1.36;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   footer[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .button[_ngcontent-%COMP%] {\n  min-width: 12rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  padding: 1rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 0.5rem;\n  overflow-y: auto;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]::-webkit-scrollbar {\n  display: none;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1rem;\n  line-height: 1.25;\n  color: #ffffff;\n  margin-bottom: 0.5rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-weight: 700;\n  font-size: 1rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%] {\n  padding-left: 1.75rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\n  margin-bottom: 0.5rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]   .options[_ngcontent-%COMP%] {\n  padding-left: 1.5rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%] {\n  width: 5rem;\n}\n/*# sourceMappingURL=general-settings.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(GeneralSettingsComponent, [{
    type: Component,
    args: [{ selector: "app-general-settings", imports: [FontAwesomeModule, FormsModule], template: '<header>\r\n    <div class="icon">\r\n        <fa-icon icon="sliders"></fa-icon>\r\n    </div>\r\n    <h2>General Settings</h2>\r\n</header>\r\n\r\n<section>\r\n    <div class="section">\r\n        <div class="header">\r\n            <fa-icon icon="calendar-lines-pen"></fa-icon>\r\n            <h3>Event Log</h3>\r\n        </div>\r\n        <div class="body">\r\n            <div class="form-group inline">\r\n                <label class="form-label">\r\n                    <input type="checkbox" class="form-checkbox" name="purgeEventLogs" [(ngModel)]="purgeEventLogs">\r\n                    <span class="form-checkbox-label">Purge event logs older than</span>\r\n                </label>\r\n                <input type="number" class="form-input always-enable-label" [(ngModel)]="purgeEventLogsDays" [disabled]="!purgeEventLogs">\r\n                <label class="form-label">days</label>\r\n            </div>\r\n\r\n            <div class="options">\r\n                <div class="form-group">\r\n                    <label class="form-label">\r\n                        <input type="checkbox"  class="form-checkbox" name="purgeTaskLogs" [(ngModel)]="purgeTaskLogs" [disabled]="!purgeEventLogs">\r\n                        <span class="form-checkbox-label">Purge task logs as well</span>\r\n                    </label>\r\n                </div>\r\n            </div>\r\n\r\n            <div class="form-group inline">\r\n                <label class="form-label">\r\n                    <input type="checkbox" class="form-checkbox" name="logWindowsEvent" [(ngModel)]="logWindowsEvent">\r\n                    <span class="form-checkbox-label">Log Windows event when</span>\r\n                </label>\r\n                <div class="form-select-wrapper">\r\n                    <select class="form-select always-enable-label" name="logWindowsEventCondition" [(ngModel)]="logWindowsEventCondition" [disabled]="!logWindowsEvent">\r\n                        @for (option of logWindowsEventOptions; track option) {\r\n                            <option [value]="option">{{ option }}</option>\r\n                        }\r\n                    </select>\r\n                </div>\r\n                <label class="form-label">days</label>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class="section">\r\n        <div class="header">\r\n            <fa-icon icon="rotate"></fa-icon>\r\n            <h3>Hyper Standby</h3>\r\n        </div>\r\n        <div class="body">\r\n            <div class="form-group inline">\r\n                <label for="" class="form-label">Run</label>\r\n                <input type="number" class="form-input" [(ngModel)]="hyperStandbyTasks">\r\n                <label for="" class="form-label">Hyper Standby Task(s) simultaneously</label>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class="section">\r\n        <div class="header">\r\n            <fa-icon icon="copy"></fa-icon>\r\n            <h3>Hyper Replication</h3>\r\n        </div>\r\n        <div class="body">\r\n            <div class="form-group inline">\r\n                <label class="form-label">Run</label>\r\n                <input type="number" class="form-input" [(ngModel)]="hyperReplicationTasks">\r\n                <label for="" class="form-label">Hyper Replication Task(s) simultaneously</label>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class="section">\r\n        <div class="header">\r\n            <fa-icon icon="chart-simple"></fa-icon>\r\n            <h3>Default performance</h3>\r\n        </div>\r\n        <div class="body">\r\n            <div class="form-group inline">\r\n                <label class="form-label">\r\n                    <input type="checkbox" class="form-checkbox" [(ngModel)]="limitCpuLoad">\r\n                    <span class="form-checkbox-label">Limit maximum CPU load</span>\r\n                </label>\r\n                <input type="number" class="form-input always-enable-label" [(ngModel)]="maxCpuLoad" [disabled]="!limitCpuLoad">\r\n                <label for="" class="form-label">%</label>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class="section">\r\n        <div class="header">\r\n            <fa-icon icon="hard-drive"></fa-icon>\r\n            <h3>Disk Scan</h3>\r\n        </div>\r\n        <div class="body">\r\n            <div class="form-group inline">\r\n                <label class="form-label">\r\n                    <input type="checkbox" class="form-checkbox" [(ngModel)]="limitDiskScan">\r\n                    <span class="form-checkbox-label">Limit number of disks to scan to</span>\r\n                </label>\r\n                <input type="number" class="form-input always-enable-label" [(ngModel)]="maxDisksToScan" [disabled]="!limitDiskScan">\r\n                <label for="" class="form-label">disks</label>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n\r\n<footer>\r\n    <span class="notice">0 Configuration changes waiting to be applied</span>\r\n    <div class="buttons">\r\n        <button type="button" class="button secondary" (click)="onCancel()">Cancel</button>\r\n        <button type="button" class="button primary" (click)="onApply()">Apply</button>\r\n    </div>\r\n</footer>\r\n', styles: ['/* renderer/src/app/preferences/general-settings/general-settings.component.less */\n:host {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: 1rem;\n  overflow: hidden;\n  padding: 16px;\n  border-radius: 0.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  background: rgba(255, 255, 255, 0.1);\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 0ms;\n  animation-name: fadeInUp;\n}\n:host header {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  border-radius: 0.5rem;\n}\n:host header .icon {\n  font-size: 1rem;\n}\n:host header h2 {\n  margin: 0;\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 500;\n  font-size: 1rem;\n  line-height: 1.25;\n  color: #ffffff;\n}\n:host section {\n  flex: 1;\n  font-size: 0.875rem;\n}\n:host footer {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  flex: 0 0 auto;\n  gap: 1rem;\n}\n:host footer .notice {\n  flex: 1;\n  font-size: 0.875rem;\n  line-height: 1.36;\n  color: #ffffff;\n}\n:host footer .buttons .button {\n  min-width: 12rem;\n}\n:host section {\n  padding: 1rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 0.5rem;\n  overflow-y: auto;\n}\n:host section::-webkit-scrollbar {\n  display: none;\n}\n:host section .section {\n  margin-bottom: 1rem;\n}\n:host section .section .header {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1rem;\n  line-height: 1.25;\n  color: #ffffff;\n  margin-bottom: 0.5rem;\n}\n:host section .section .header h3 {\n  font-weight: 700;\n  font-size: 1rem;\n}\n:host section .section .body {\n  padding-left: 1.75rem;\n}\n:host section .section .body .form-group {\n  margin-bottom: 0.5rem;\n}\n:host section .section .body .options {\n  padding-left: 1.5rem;\n}\n:host section .section .body input[type=number] {\n  width: 5rem;\n}\n/*# sourceMappingURL=general-settings.component.css.map */\n'] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(GeneralSettingsComponent, { className: "GeneralSettingsComponent", filePath: "renderer/src/app/preferences/general-settings/general-settings.component.ts", lineNumber: 11 });
})();
export {
  GeneralSettingsComponent
};
//# sourceMappingURL=chunk-SRV7T7NI.js.map
