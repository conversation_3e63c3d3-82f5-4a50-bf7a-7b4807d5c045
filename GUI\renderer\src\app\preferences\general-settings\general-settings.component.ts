import { Component } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FormsModule } from '@angular/forms';

@Component({
    selector: 'app-general-settings',
    imports: [FontAwesomeModule, FormsModule],
    templateUrl: './general-settings.component.html',
    styleUrl: './general-settings.component.less'
})
export class GeneralSettingsComponent {
    // Form model
    purgeEventLogs = false;
    purgeEventLogsDays = 30;
    purgeTaskLogs = false;
    logWindowsEvent = false;
    logWindowsEventCondition = 'Task Succeeded or failed';
    hyperStandbyTasks = 30;
    hyperReplicationTasks = 30;
    limitCpuLoad = false;
    maxCpuLoad = 100;
    limitDiskScan = false;
    maxDisksToScan = 1;

    logWindowsEventOptions = [
        'Task Succeeded',
        'Task Failed',
        'Task Succeeded or failed'
    ];

    onApply() {
        // Handle apply logic
        console.log('Settings applied');
    }

    onCancel() {
        // Handle cancel logic
        console.log('Settings cancelled');
    }
}
