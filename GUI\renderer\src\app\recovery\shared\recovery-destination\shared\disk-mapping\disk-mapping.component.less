@import '../../../../../../styles/variables.less';
@import '../../../../../../styles/mixins.less';

:host {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    flex: 1;
    overflow-y: auto;

    // Hide scrollbar for Chromium/WebKit (Electron)
    &::-webkit-scrollbar {
        display: none;
    }

    .source-column,
    .target-column {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .column-header {
            h4 {
                font-size: 1rem;
                font-weight: 500;
                color: @text-color;
                margin: 0;
                text-align: center;
            }
        }
    }

    .source-column:not(.clone) {
        position: relative;
    }
    .source-column.clone {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        opacity: 0;
    }

    .disk-item {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        .disk-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;

            .disk-name {
                font-size: 0.75rem;
                font-weight: 400;
                color: @text-color;
                line-height: 1.36;
            }

            .disk-type {
                font-size: 0.75rem;
                font-weight: 400;
                color: @text-color;
                line-height: 1.36;
                text-align: center;
                flex: 1;
            }

            .disk-info {
                font-size: 0.75rem;
                font-weight: 400;
                color: @text-color;
                line-height: 1.36;
                text-align: right;
            }
        }

        .disk-body {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.1);

            .disk-icons {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .disk-icon {
                    width: 1.5rem;
                    height: 1.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: @text-color;

                    fa-icon {
                        font-size: 1.25rem;
                    }
                }
            }

            .disk-partitions {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;

                .more-options-disk {
                    position: absolute;
                    right: 0.5rem;
                    top: 1rem;
                    width: 1.2rem;
                    height: 0.3rem;
                    background: none;
                    border: none;
                    color: @text-color;
                    cursor: pointer;

                    .transition(color);

                    &:hover {
                        color: @primary-color;
                    }

                    fa-icon {
                        font-size: 0.8rem;
                    }
                }
            }
        }
    }

    .arrow-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        gap: 0;
        padding-top: 4.375rem;
        min-width: 16rem;

        .mapping-arrows {
            display: flex;
            flex-direction: column;
            gap: .5rem;
            width: 100%;

            .arrow-line {
                display: flex;
                align-items: center;
                width: 100%;
                height: 4.375rem;

                &:first-child {
                    height: 1.25rem;

                    &::before {
                        background: rgba(204, 204, 204, 0.5);
                    }

                    &::after {
                        border-left-color: rgba(204, 204, 204, 0.5);
                    }
                }

                &::before {
                    content: '';
                    width: 100%;
                    height: .25rem;
                    background: rgba(16, 185, 129, 0.5);
                }

                &::after {
                    content: '';
                    border-top: 0.5rem solid transparent;
                    border-bottom: 0.5rem solid transparent;
                    border-left: 0.75rem solid rgba(16, 185, 129, 0.5);
                }
            }
        }
    }
}

.partition-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    background: rgba(16, 185, 129, 0.2);
    border: 1px solid rgba(16, 185, 129, 0.5);
    border-radius: 0.5rem;
    padding: 0.625rem;

    .partition-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .partition-icon {
            color: @text-color;
            font-size: 1rem;
        }

        .partition-name {
            font-size: 0.75rem;
            font-weight: 400;
            color: @text-color;
            line-height: 1.36;
            flex: 1;
        }

        .more-options {
            background: none;
            border: none;
            color: @text-color;
            cursor: pointer;

            .transition(color);

            &:hover {
                color: @primary-color;
            }

            fa-icon {
                font-size: 0.8rem;
            }
        }
    }

    .partition-usage {
        .usage-bar {
            width: 100%;
            height: 1.25rem;
            background: linear-gradient(to bottom, #868686 0%, #ffffff 100%);
            border-radius: 0.25rem;
            overflow: hidden;
            position: relative;

            .usage-fill {
                height: 100%;
                background: linear-gradient(to right, #e73820 0%, #c32615 100%);
                border-radius: 0.25rem;
                transition: width 0.3s ease;
            }
        }
    }
}
