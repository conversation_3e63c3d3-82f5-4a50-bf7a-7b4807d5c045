import { Component, EventEmitter, Output, Input } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FormsModule } from '@angular/forms';
import { CommonModule, DatePipe } from '@angular/common';

export interface BackupSettings {
    taskName: string;
    // Image Options
    compression: boolean;
    compressionType: 'deduplication' | 'common';
    compressionLevel: number;
    passwordProtection: boolean;
    password: string;
    confirmPassword: string;
    encryption: string;
    destinationIsolation: boolean;
    isolationType: 'unassign' | 'offline' | 'eject' | 'disable';
    networkInterface: string;
    imageComment: string;

    // Task Execution Options
    taskEffectiveFrom: string;
    taskEffectiveTo: string;
    useEffectiveTo: boolean;
    runOnShutdown: boolean;
    shutdownType: string;
    autoRunMissed: boolean;
    runMissedBase: boolean;
    enableRetention: boolean;
    retentionType: string;
    retentionCount: number;
    deleteOlderImages: boolean;
    enableReconcile: boolean;
    preserveReconciled: boolean;
    preserveDays: number;
    emailNotification: boolean;
    emailCondition: string;
    executionPriority: boolean;
    fullPriority: number;
    incrementalPriority: number;
}

@Component({
    selector: 'app-backup-settings',
    imports: [FontAwesomeModule, FormsModule, CommonModule],
    templateUrl: './backup-settings.component.html',
    styleUrl: './backup-settings.component.less'
})
export class BackupSettingsComponent {
    @Output() settingsSaved = new EventEmitter<BackupSettings>();
    @Output() settingsClosed = new EventEmitter<void>();

    private datePipe = new DatePipe('en-US');

    // Generate default task name with current date and time
    private generateDefaultTaskName(): string {
        const now = new Date();
        const dateStr = this.datePipe.transform(now, 'yyyyMMdd_HHmm');
        return `backup_${dateStr}`;
    }

        // Form data model
    settings: BackupSettings = {
        taskName: this.generateDefaultTaskName(),
        compression: false,
        compressionType: 'deduplication',
        compressionLevel: 1,
        passwordProtection: false,
        password: '',
        confirmPassword: '',
        encryption: 'No Encryption',
        destinationIsolation: false,
        isolationType: 'unassign',
        networkInterface: 'Select network interface',
        imageComment: '',
        taskEffectiveFrom: '2025/06/05 11:06',
        taskEffectiveTo: '2025/06/05 11:06',
        useEffectiveTo: false,
        runOnShutdown: false,
        shutdownType: 'Base and incremental',
        autoRunMissed: false,
        runMissedBase: false,
        enableRetention: false,
        retentionType: 'Delete base and incremental',
        retentionCount: 3,
        deleteOlderImages: false,
        enableReconcile: false,
        preserveReconciled: false,
        preserveDays: 15,
        emailNotification: false,
        emailCondition: 'When task failed',
        executionPriority: false,
        fullPriority: 2,
        incrementalPriority: 2
    };

    // Handle priority sliders
    onFullPriorityChange(event: Event): void {
        const target = event.target as HTMLInputElement;
        this.settings.fullPriority = parseInt(target.value);
    }

    onIncrementalPriorityChange(event: Event): void {
        const target = event.target as HTMLInputElement;
        this.settings.incrementalPriority = parseInt(target.value);
    }

    // Get priority label
    getPriorityLabel(priority: number): string {
        const labels = ['Lowest', 'Low', 'Medium', 'High'];
        return labels[priority] || 'Medium';
    }

    // Save settings
    saveSettings(): void {
        this.settingsSaved.emit(this.settings);
        this.closeSettings();
    }

    // Close settings panel
    closeSettings(): void {
        this.settingsClosed.emit();
    }
}
