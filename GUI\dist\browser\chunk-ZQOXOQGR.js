import {
  CheckboxControlV<PERSON>ueAccessor,
  DefaultV<PERSON>ueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel,
  NgSelectOption,
  RadioControlValueAccessor,
  SelectControlValueAccessor,
  ɵNgSelectMultipleOption
} from "./chunk-QOWQNEHV.js";
import {
  FaIconComponent,
  FontAwesomeModule
} from "./chunk-N34W2DJ7.js";
import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵrepeater,
  ɵɵrepeaterCreate,
  ɵɵrepeaterTrackByIdentity,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-TBQAZTY7.js";

// renderer/src/app/preferences/hyperback/hyperback.component.ts
var _c0 = () => [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
function HyperbackComponent_For_28_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 12);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const num_r1 = ctx.$implicit;
    \u0275\u0275property("value", num_r1);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(num_r1);
  }
}
var HyperbackComponent = class _HyperbackComponent {
  // Operation Mode
  operationMode = "parallel";
  // 'parallel' or 'sequential'
  concurrentTasks = 5;
  // Tracking Method
  useResilientChangeTracking = false;
  // Backup Image File Optimization
  createOptimizedBackupImage = false;
  // Post-Backup (placeholder for future implementation)
  postBackupOption = "option1";
  // Deduplication (placeholder for future implementation)
  deduplicationOption = "option1";
  onApply() {
    console.log("Hyperback settings applied");
  }
  onCancel() {
    console.log("Hyperback settings cancelled");
  }
  static \u0275fac = function HyperbackComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _HyperbackComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _HyperbackComponent, selectors: [["app-hyperback"]], decls: 59, vars: 6, consts: [[1, "icon"], ["icon", "rocket"], [1, "section"], [1, "subsection"], [1, "form-group"], [1, "form-label"], ["type", "radio", "name", "operationMode", "value", "parallel", 1, "form-radio", 3, "ngModelChange", "ngModel"], [1, "form-radio-label"], ["type", "radio", "name", "operationMode", "value", "sequential", 1, "form-radio", 3, "ngModelChange", "ngModel"], [1, "form-group", "inline"], [1, "form-select-wrapper"], [1, "form-select", 3, "ngModelChange", "ngModel"], [3, "value"], ["type", "checkbox", 1, "form-checkbox", 3, "ngModelChange", "ngModel"], [1, "form-checkbox-label"], [1, "form-description"], [1, "notice"], [1, "buttons"], ["type", "button", 1, "button", "secondary", 3, "click"], ["type", "button", 1, "button", "primary", 3, "click"]], template: function HyperbackComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "header")(1, "div", 0);
      \u0275\u0275element(2, "fa-icon", 1);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "h2");
      \u0275\u0275text(4, "Hyperback");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(5, "section")(6, "div", 2)(7, "h3");
      \u0275\u0275text(8, "Options");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(9, "div", 3)(10, "h4");
      \u0275\u0275text(11, "Operation Mode");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "div", 4)(13, "label", 5)(14, "input", 6);
      \u0275\u0275twoWayListener("ngModelChange", function HyperbackComponent_Template_input_ngModelChange_14_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.operationMode, $event) || (ctx.operationMode = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "span", 7);
      \u0275\u0275text(16, "Parallel Backup (backup VMs in parallel)");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(17, "div", 4)(18, "label", 5)(19, "input", 8);
      \u0275\u0275twoWayListener("ngModelChange", function HyperbackComponent_Template_input_ngModelChange_19_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.operationMode, $event) || (ctx.operationMode = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(20, "span", 7);
      \u0275\u0275text(21, "Sequential Backup (backup VMs sequentially)");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(22, "div", 9)(23, "label", 5);
      \u0275\u0275text(24, "Run");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(25, "div", 10)(26, "select", 11);
      \u0275\u0275twoWayListener("ngModelChange", function HyperbackComponent_Template_select_ngModelChange_26_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.concurrentTasks, $event) || (ctx.concurrentTasks = $event);
        return $event;
      });
      \u0275\u0275repeaterCreate(27, HyperbackComponent_For_28_Template, 2, 2, "option", 12, \u0275\u0275repeaterTrackByIdentity);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(29, "label", 5);
      \u0275\u0275text(30, "HyperBack Task(s) simultaneously");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(31, "div", 3)(32, "h4");
      \u0275\u0275text(33, "Tracking Method");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(34, "div", 4)(35, "label", 5)(36, "input", 13);
      \u0275\u0275twoWayListener("ngModelChange", function HyperbackComponent_Template_input_ngModelChange_36_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.useResilientChangeTracking, $event) || (ctx.useResilientChangeTracking = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(37, "span", 14);
      \u0275\u0275text(38, "Use Microsoft Resilient Change Tracking (RCT)");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(39, "div", 15);
      \u0275\u0275text(40, " * * This tracking method only applies to backing up of Generation 2 VMs version 8.0 or later (on Server 2016 or later). ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(41, "div", 3)(42, "h4");
      \u0275\u0275text(43, "Backup Image File Optimization");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(44, "div", 4)(45, "label", 5)(46, "input", 13);
      \u0275\u0275twoWayListener("ngModelChange", function HyperbackComponent_Template_input_ngModelChange_46_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.createOptimizedBackupImage, $event) || (ctx.createOptimizedBackupImage = $event);
        return $event;
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(47, "span", 14);
      \u0275\u0275text(48, "Create optimized backup image file");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(49, "div", 15);
      \u0275\u0275text(50, " * * This option can exclude pagefile.sys and hiberfil.sys, etc. to make the backup image smaller. ");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(51, "footer")(52, "span", 16);
      \u0275\u0275text(53, "0 Configuration changes waiting to be applied");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(54, "div", 17)(55, "button", 18);
      \u0275\u0275listener("click", function HyperbackComponent_Template_button_click_55_listener() {
        return ctx.onCancel();
      });
      \u0275\u0275text(56, "Cancel");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(57, "button", 19);
      \u0275\u0275listener("click", function HyperbackComponent_Template_button_click_57_listener() {
        return ctx.onApply();
      });
      \u0275\u0275text(58, "Apply");
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(14);
      \u0275\u0275twoWayProperty("ngModel", ctx.operationMode);
      \u0275\u0275advance(5);
      \u0275\u0275twoWayProperty("ngModel", ctx.operationMode);
      \u0275\u0275advance(7);
      \u0275\u0275twoWayProperty("ngModel", ctx.concurrentTasks);
      \u0275\u0275advance();
      \u0275\u0275repeater(\u0275\u0275pureFunction0(5, _c0));
      \u0275\u0275advance(9);
      \u0275\u0275twoWayProperty("ngModel", ctx.useResilientChangeTracking);
      \u0275\u0275advance(10);
      \u0275\u0275twoWayProperty("ngModel", ctx.createOptimizedBackupImage);
    }
  }, dependencies: [FontAwesomeModule, FaIconComponent, FormsModule, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, RadioControlValueAccessor, NgControlStatus, NgModel], styles: ['\n\n[_nghost-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: 1rem;\n  overflow: hidden;\n  padding: 16px;\n  border-radius: 0.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  background: rgba(255, 255, 255, 0.1);\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 0ms;\n  animation-name: fadeInUp;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  border-radius: 0.5rem;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\n  font-size: 1rem;\n}\n[_nghost-%COMP%]   header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  margin: 0;\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 500;\n  font-size: 1rem;\n  line-height: 1.25;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  flex: 1;\n  font-size: 0.875rem;\n}\n[_nghost-%COMP%]   footer[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  flex: 0 0 auto;\n  gap: 1rem;\n}\n[_nghost-%COMP%]   footer[_ngcontent-%COMP%]   .notice[_ngcontent-%COMP%] {\n  flex: 1;\n  font-size: 0.875rem;\n  line-height: 1.36;\n  color: #ffffff;\n}\n[_nghost-%COMP%]   footer[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .button[_ngcontent-%COMP%] {\n  min-width: 12rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%] {\n  padding: 1rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 0.5rem;\n  overflow-y: auto;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]::-webkit-scrollbar {\n  display: none;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 700;\n  font-size: 1rem;\n  line-height: 1.25;\n  color: #ffffff;\n  margin-bottom: 1rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .subsection[_ngcontent-%COMP%] {\n  padding-left: 1rem;\n  margin-bottom: 1rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .subsection[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 700;\n  font-size: 0.875rem;\n  line-height: 1.42857143;\n  color: #ffffff;\n  margin-bottom: 1rem;\n}\n[_nghost-%COMP%]   section[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\n  margin-bottom: 0.5rem;\n}\n/*# sourceMappingURL=hyperback.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(HyperbackComponent, [{
    type: Component,
    args: [{ selector: "app-hyperback", imports: [FontAwesomeModule, FormsModule], template: '<header>\r\n    <div class="icon">\r\n        <fa-icon icon="rocket"></fa-icon>\r\n    </div>\r\n    <h2>Hyperback</h2>\r\n</header>\r\n\r\n<section>\r\n    <div class="section">\r\n        <h3>Options</h3>\r\n\r\n        <div class="subsection">\r\n            <h4>Operation Mode</h4>\r\n\r\n            <div class="form-group">\r\n                <label class="form-label">\r\n                    <input type="radio" class="form-radio" name="operationMode" value="parallel" [(ngModel)]="operationMode">\r\n                    <span class="form-radio-label">Parallel Backup (backup VMs in parallel)</span>\r\n                </label>\r\n            </div>\r\n\r\n            <div class="form-group">\r\n                <label class="form-label">\r\n                    <input type="radio" class="form-radio" name="operationMode" value="sequential" [(ngModel)]="operationMode">\r\n                    <span class="form-radio-label">Sequential Backup (backup VMs sequentially)</span>\r\n                </label>\r\n            </div>\r\n\r\n            <div class="form-group inline">\r\n                <label class="form-label">Run</label>\r\n                <div class="form-select-wrapper">\r\n                    <select class="form-select" [(ngModel)]="concurrentTasks">\r\n                        @for (num of [1,2,3,4,5,6,7,8,9,10]; track num) {\r\n                            <option [value]="num">{{ num }}</option>\r\n                        }\r\n                    </select>\r\n                </div>\r\n                <label class="form-label">HyperBack Task(s) simultaneously</label>\r\n            </div>\r\n        </div>\r\n\r\n        <div class="subsection">\r\n            <h4>Tracking Method</h4>\r\n\r\n            <div class="form-group">\r\n                <label class="form-label">\r\n                    <input type="checkbox" class="form-checkbox" [(ngModel)]="useResilientChangeTracking">\r\n                    <span class="form-checkbox-label">Use Microsoft Resilient Change Tracking (RCT)</span>\r\n                </label>\r\n                <div class="form-description">\r\n                    * * This tracking method only applies to backing up of Generation 2 VMs version 8.0 or later (on Server 2016 or later).\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class="subsection">\r\n            <h4>Backup Image File Optimization</h4>\r\n\r\n            <div class="form-group">\r\n                <label class="form-label">\r\n                    <input type="checkbox" class="form-checkbox" [(ngModel)]="createOptimizedBackupImage">\r\n                    <span class="form-checkbox-label">Create optimized backup image file</span>\r\n                </label>\r\n                <div class="form-description">\r\n                    * * This option can exclude pagefile.sys and hiberfil.sys, etc. to make the backup image smaller.\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n\r\n<footer>\r\n    <span class="notice">0 Configuration changes waiting to be applied</span>\r\n    <div class="buttons">\r\n        <button type="button" class="button secondary" (click)="onCancel()">Cancel</button>\r\n        <button type="button" class="button primary" (click)="onApply()">Apply</button>\r\n    </div>\r\n</footer>\r\n', styles: ['/* renderer/src/app/preferences/hyperback/hyperback.component.less */\n:host {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  gap: 1rem;\n  overflow: hidden;\n  padding: 16px;\n  border-radius: 0.5rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  background: rgba(255, 255, 255, 0.1);\n  animation-duration: 500ms;\n  animation-fill-mode: forwards;\n  animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);\n  animation-delay: 0ms;\n  animation-name: fadeInUp;\n}\n:host header {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  border-radius: 0.5rem;\n}\n:host header .icon {\n  font-size: 1rem;\n}\n:host header h2 {\n  margin: 0;\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 500;\n  font-size: 1rem;\n  line-height: 1.25;\n  color: #ffffff;\n}\n:host section {\n  flex: 1;\n  font-size: 0.875rem;\n}\n:host footer {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  flex: 0 0 auto;\n  gap: 1rem;\n}\n:host footer .notice {\n  flex: 1;\n  font-size: 0.875rem;\n  line-height: 1.36;\n  color: #ffffff;\n}\n:host footer .buttons .button {\n  min-width: 12rem;\n}\n:host section {\n  padding: 1rem;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 0.5rem;\n  overflow-y: auto;\n}\n:host section::-webkit-scrollbar {\n  display: none;\n}\n:host section .section {\n  margin-bottom: 1rem;\n}\n:host section .section h3 {\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 700;\n  font-size: 1rem;\n  line-height: 1.25;\n  color: #ffffff;\n  margin-bottom: 1rem;\n}\n:host section .section .subsection {\n  padding-left: 1rem;\n  margin-bottom: 1rem;\n}\n:host section .section .subsection h4 {\n  font-family:\n    "inter",\n    "noto sans",\n    sans-serif;\n  font-weight: 700;\n  font-size: 0.875rem;\n  line-height: 1.42857143;\n  color: #ffffff;\n  margin-bottom: 1rem;\n}\n:host section .section .form-group {\n  margin-bottom: 0.5rem;\n}\n/*# sourceMappingURL=hyperback.component.css.map */\n'] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(HyperbackComponent, { className: "HyperbackComponent", filePath: "renderer/src/app/preferences/hyperback/hyperback.component.ts", lineNumber: 11 });
})();
export {
  HyperbackComponent
};
//# sourceMappingURL=chunk-ZQOXOQGR.js.map
