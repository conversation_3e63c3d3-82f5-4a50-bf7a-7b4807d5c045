import { Component, input, output, computed } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

@Component({
    selector: 'app-backup-destination',
    templateUrl: './backup-destination.component.html',
    styleUrl: './backup-destination.component.less',
    imports: [FontAwesomeModule],
})
export class BackupDestinationComponent {
    // Inputs from parent component
    selectedDestination = input<string | null>(null);
    expandedDestination = input<string | null>(null);
    isCollapsedVertical = input<boolean>(false);

    // Outputs to parent component
    destinationSelected = output<string>();
    destinationConfigurationSaved = output<MouseEvent>();

    // Check if a specific destination is selected
    isDestinationSelected = computed(() => (destinationType: string) => this.selectedDestination() === destinationType);

    // Check if a specific destination is expanded
    isDestinationExpanded = computed(() => (destinationType: string) => this.expandedDestination() === destinationType);

    // Handle destination selection
    selectDestination(destinationType: string): void {
        this.destinationSelected.emit(destinationType);
    }

    // Handle save configuration
    saveDestinationConfiguration(event: MouseEvent): void {
        this.destinationConfigurationSaved.emit(event);
    }
}
