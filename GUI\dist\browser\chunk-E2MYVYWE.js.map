{"version": 3, "sources": ["renderer/src/app/preferences/rescue-boot/rescue-boot.component.ts", "renderer/src/app/preferences/rescue-boot/rescue-boot.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\n\r\n@Component({\r\n    selector: 'app-rescue-boot',\r\n    imports: [FontAwesomeModule],\r\n    templateUrl: './rescue-boot.component.html',\r\n    styleUrl: './rescue-boot.component.less'\r\n})\r\nexport class RescueBootComponent {\r\n\r\n}\r\n", "<header>\r\n    <div class=\"icon\">\r\n        <fa-icon icon=\"life-ring\"></fa-icon>\r\n    </div>\r\n    <h2>Rescue Boot</h2>\r\n</header>\r\n\r\n<section>\r\n    <p>Rescue boot settings will be implemented here.</p>\r\n</section>\r\n\r\n<footer>\r\n    <span class=\"notice\">0 Configuration changes waiting to be applied</span>\r\n    <div class=\"buttons\">\r\n        <button type=\"button\" class=\"button secondary\">Cancel</button>\r\n        <button type=\"button\" class=\"button primary\">Apply</button>\r\n    </div>\r\n</footer>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AASM,IAAO,sBAAP,MAAO,qBAAmB;;qCAAnB,sBAAmB;EAAA;yEAAnB,sBAAmB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,WAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,WAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,SAAA,CAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACThC,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,OAAA,CAAA;AAEA,MAAA,oBAAA,GAAA,WAAA,CAAA;AACJ,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,aAAA;AAAW,MAAA,uBAAA,EAAK;AAGxB,MAAA,yBAAA,GAAA,SAAA,EAAS,GAAA,GAAA;AACF,MAAA,iBAAA,GAAA,gDAAA;AAA8C,MAAA,uBAAA,EAAI;AAGzD,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,QAAA,CAAA;AACiB,MAAA,iBAAA,IAAA,+CAAA;AAA6C,MAAA,uBAAA;AAClE,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,UAAA,CAAA;AAC8B,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AACrD,MAAA,yBAAA,IAAA,UAAA,CAAA;AAA6C,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAS,EACzD;;oBDXI,mBAAiB,eAAA,GAAA,QAAA,CAAA,y9CAAA,EAAA,CAAA;;;sEAIlB,qBAAmB,CAAA;UAN/B;uBACa,mBAAiB,SAClB,CAAC,iBAAiB,GAAC,UAAA,uhBAAA,QAAA,CAAA,8sCAAA,EAAA,CAAA;;;;6EAInB,qBAAmB,EAAA,WAAA,uBAAA,UAAA,qEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}