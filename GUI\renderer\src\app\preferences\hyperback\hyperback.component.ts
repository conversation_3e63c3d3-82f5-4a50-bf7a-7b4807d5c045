import { Component } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FormsModule } from '@angular/forms';

@Component({
    selector: 'app-hyperback',
    imports: [FontAwesomeModule, FormsModule],
    templateUrl: './hyperback.component.html',
    styleUrl: './hyperback.component.less'
})
export class HyperbackComponent {
    // Operation Mode
    operationMode = 'parallel'; // 'parallel' or 'sequential'
    concurrentTasks = 5;

    // Tracking Method
    useResilientChangeTracking = false;

    // Backup Image File Optimization
    createOptimizedBackupImage = false;

    // Post-Backup (placeholder for future implementation)
    postBackupOption = 'option1';

    // Deduplication (placeholder for future implementation)
    deduplicationOption = 'option1';

    onApply() {
        // Handle apply logic
        console.log('Hyperback settings applied');
    }

    onCancel() {
        // Handle cancel logic
        console.log('Hyperback settings cancelled');
    }
}
