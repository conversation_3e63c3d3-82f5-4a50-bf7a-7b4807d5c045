{"version": 3, "sources": ["renderer/src/app/standby/standby.component.ts", "renderer/src/app/standby/standby.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-standby',\r\n  imports: [],\r\n  templateUrl: './standby.component.html',\r\n  styleUrl: './standby.component.less'\r\n})\r\nexport class StandbyComponent {\r\n\r\n}\r\n", "<p>standby works!</p>\r\n"], "mappings": ";;;;;;;;;;;AAQM,IAAO,mBAAP,MAAO,kBAAgB;;qCAAhB,mBAAgB;EAAA;yEAAhB,mBAAgB,WAAA,CAAA,CAAA,aAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,0BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACR7B,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,gBAAA;AAAc,MAAA,uBAAA;;;;;sEDQJ,kBAAgB,CAAA;UAN5B;uBACW,eAAa,SACd,CAAA,GAAE,UAAA,4BAAA,CAAA;;;;6EAIA,kBAAgB,EAAA,WAAA,oBAAA,UAAA,iDAAA,YAAA,EAAA,CAAA;AAAA,GAAA;", "names": []}