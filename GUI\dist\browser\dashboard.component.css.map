{"version": 3, "sources": ["renderer/src/app/dashboard/dashboard.component.less", "renderer/src/styles/variables.less"], "sourcesContent": ["@import '../../styles/variables.less';\n\n:host {\n    display: block;\n    overflow-y: auto;\n    height: 100%;\n\n    section {\n        padding: 1rem;\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n\n        .welcome {\n            animation: fadeInUp 0.5s cubic-bezier(0.215, 0.61, 0.355, 1) forwards;\n            animation-delay: 0.02s;\n            opacity: 0;\n            will-change: transform, opacity;\n\n            h1 {\n                font-size: 1.5rem;\n                font-weight: 700;\n                margin: 0 0 .5rem 0;\n            }\n\n            p {\n                font-size: 1rem;\n                color: @text-secondary;\n                margin: 0;\n            }\n        }\n\n        // Modify widgets-row to use grid layout\n        .row {\n            display: grid;\n            grid-template-columns: repeat(6, 1fr); // Create a 6-column grid\n            gap: 1rem; // Unified spacing\n            width: 100%;\n\n            .widget {\n                background-color: @bg-overlay;\n                border-radius: @border-radius;\n                transition: box-shadow 0.3s ease, background-color 0.3s ease;\n                animation: fadeInUp .5s cubic-bezier(0.215, 0.61, 0.355, 1) forwards;\n                position: relative;\n                overflow: hidden;\n                opacity: 0; // Ensure initial state is completely invisible\n\n                &:hover {\n                    box-shadow: 0 .5rem 1.25rem rgba(0, 0, 0, 0.2);\n                    background-color: rgba(255, 255, 255, 0.15);\n                }\n\n                // First three elements each occupy 2 columns width\n                &.storage,\n                &.backup,\n                &.recovery {\n                    grid-column: span 2; // Occupy 2 columns width\n                }\n\n                // Last four elements each occupy 3 columns width\n                &.recent-backups,\n                &.quick-actions,\n                &.empty-widget {\n                    grid-column: span 3; // Occupy 3 columns width\n                }\n\n                &.storage {\n                    padding: 1rem;\n                    display: flex;\n                    flex-direction: column;\n                    justify-content: center;\n                    animation-delay: 0.05s;\n\n                    .widget-header {\n                        display: flex;\n                        flex-direction: column;\n                        gap: 12px;\n\n                        .widget-title {\n                            display: flex;\n                            justify-content: space-between;\n                            align-items: center;\n\n                            span {\n                                font-size: 18px;\n                            }\n\n                            .icon-container {\n                                width: 17.5px;\n                                height: 20px;\n                                display: flex;\n                                align-items: center;\n                                justify-content: flex-end;\n\n                                img {\n                                    fill: #36AB6B;\n                                }\n                            }\n                        }\n\n                        .widget-value {\n                            display: flex;\n                            align-items: center;\n\n                            .primary-value {\n                                font-size: 30px;\n                                font-weight: 700;\n                                margin-right: 12px;\n                                position: relative;\n\n                                &.animated {\n                                    opacity: 0;\n                                    animation: fadeIn 0.4s cubic-bezier(0.215, 0.61, 0.355, 1) forwards 0.15s;\n                                    will-change: opacity;\n                                }\n                            }\n\n                            .secondary-value {\n                                font-size: 16px;\n                                color: @text-secondary;\n                                position: relative;\n\n                                &.animated {\n                                    opacity: 0;\n                                    animation: fadeIn 0.4s cubic-bezier(0.215, 0.61, 0.355, 1) forwards 0.2s;\n                                    will-change: opacity;\n                                }\n                            }\n                        }\n\n                        .progress-bar {\n                            height: 8px;\n                            background-color: @bg-overlay;\n                            border-radius: 9999px;\n                            overflow: hidden;\n\n                            .progress-fill {\n                                height: 100%;\n                                background: linear-gradient(90deg, #E73820 0%, #C32615 100%);\n                                border-radius: 9999px;\n                                width: 0;\n                                animation: progressAnimate 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards 0.1s;\n                                will-change: width;\n                            }\n                        }\n                    }\n                }\n\n                &.backup,\n                &.recovery {\n                    padding: 1rem;\n\n                    &.backup {\n                        animation-delay: 0.1s;\n                    }\n\n                    &.recovery {\n                        animation-delay: 0.15s;\n                    }\n\n                    .widget-content {\n                        display: flex;\n                        flex-direction: column;\n                        gap: 12px;\n\n                        .widget-header {\n                            display: flex;\n                            justify-content: space-between;\n                            align-items: center;\n\n                            span {\n                                font-size: 18px;\n                            }\n\n                            .icon-container {\n                                width: 20px;\n                                height: 20px;\n                                display: flex;\n                                align-items: center;\n                                justify-content: center;\n\n                                &.green img {\n                                    fill: @success-color;\n                                }\n                            }\n                        }\n\n                        .widget-value {\n                            font-size: 30px;\n                            font-weight: 700;\n                            line-height: 1.21;\n                            margin: 0;\n\n                            &.animated {\n                                opacity: 0;\n                                animation: countUp 0.5s cubic-bezier(0.215, 0.61, 0.355, 1) forwards 0.15s;\n                                position: relative;\n                                will-change: transform, opacity;\n                            }\n                        }\n\n                        .widget-info {\n                            font-size: 14px;\n                            color: @text-secondary;\n                            margin: 0;\n                        }\n\n                        .widget-footer {\n                            display: flex;\n                            align-items: center;\n                            gap: 5px;\n\n                            .trend-icon {\n                                width: 10.5px;\n                                height: 14px;\n                                display: flex;\n                                justify-content: center;\n                                align-items: center;\n\n                                img {\n                                    fill: @success-color;\n                                }\n                            }\n\n                            .trend-text {\n                                font-size: 14px;\n                                color: @success-color;\n                            }\n                        }\n                    }\n                }\n\n                &.recent-backups,\n                &.quick-actions {\n                    padding: 24px;\n                    display: flex;\n                    flex-direction: column;\n                    gap: 24px;\n\n                    &.recent-backups {\n                        animation-delay: 0.2s;\n                    }\n\n                    &.quick-actions {\n                        animation-delay: 0.25s;\n                    }\n\n                    .widget-head {\n                        display: flex;\n                        justify-content: space-between;\n                        align-items: center;\n\n                        h3 {\n                            font-size: 18px;\n                            font-weight: 400;\n                            margin: 0;\n                        }\n\n                        .view-all {\n                            background: transparent;\n                            border: none;\n                            color: @text-secondary;\n                            font-size: 14px;\n                            cursor: pointer;\n                            padding: 0;\n\n                            &:hover {\n                                color: @text-color;\n                                text-decoration: underline;\n                            }\n                        }\n                    }\n                }\n\n                &.recent-backups {\n                    .backups-list {\n                        display: flex;\n                        flex-direction: column;\n                        gap: 10px;\n\n                        .backup-item {\n                            background-color: @light-overlay;\n                            border-radius: @border-radius-small;\n                            padding: 12px 16px;\n                            display: flex;\n                            justify-content: space-between;\n                            align-items: center;\n                            transition: all 0.3s ease;\n                            cursor: pointer;\n\n                            &:hover {\n                                background-color: rgba(255, 255, 255, 0.15);\n                                transform: translateX(5px);\n                            }\n\n                            .backup-info {\n                                display: flex;\n                                align-items: center;\n                                gap: 12px;\n\n                                .backup-icon {\n                                    width: 14px;\n                                    height: 16px;\n                                    display: flex;\n                                    justify-content: center;\n                                    align-items: center;\n\n                                    img {\n                                        width: 100%;\n                                        height: 100%;\n                                    }\n                                }\n\n                                .backup-details {\n                                    h4 {\n                                        font-size: 16px;\n                                        font-weight: 500;\n                                        margin: 0 0 4px 0;\n                                    }\n\n                                    span {\n                                        font-size: 14px;\n                                        color: @text-secondary;\n                                    }\n                                }\n                            }\n\n                            .status-badge {\n                                padding: 4px 10px;\n                                border-radius: 9999px;\n                                font-size: 14px;\n\n                                &.success {\n                                    background-color: @success-bg;\n                                    color: @success-color;\n                                }\n                            }\n                        }\n                    }\n                }\n\n                &.quick-actions {\n                    .actions-grid {\n                        display: flex;\n                        flex-wrap: wrap;\n                        gap: 16px;\n\n                        .action-button {\n                            flex: 1 0 calc(50% - 8px);\n                            min-width: 0;\n                            display: flex;\n                            align-items: center;\n                            justify-content: center;\n                            gap: 10px;\n                            padding: 10px 16px;\n                            height: 52px; // Fixed button height\n                            border-radius: @border-radius;\n                            background-color: @bg-overlay;\n                            border: none;\n                            color: @text-color;\n                            font-size: 16px;\n                            cursor: pointer;\n                            transition: all 0.3s ease;\n                            position: relative;\n                            overflow: hidden;\n\n                            &:hover {\n                                background-color: rgba(255, 255, 255, 0.2);\n                                transform: scale(1.03);\n                            }\n\n                            &.primary {\n                                background: @primary-gradient;\n                                animation: pulse 2s infinite;\n\n                                &:hover {\n                                    opacity: 0.9;\n                                    transform: scale(1.03);\n                                }\n                            }\n\n                            img {\n                                width: 16px;\n                                height: 16px;\n                            }\n                        }\n                    }\n                }\n\n                &.empty-widget {\n                    padding: 24px;\n                    animation-delay: 0.3s;\n\n                    &:nth-of-type(odd) {\n                        animation-delay: 0.3s;\n                    }\n\n                    &:nth-of-type(even) {\n                        animation-delay: 0.35s;\n                    }\n\n                    .widget-head {\n                        h3 {\n                            font-size: 18px;\n                            font-weight: 400;\n                            margin: 0;\n                        }\n                    }\n                }\n            }\n        }\n    }\n}\n", "// variables\n@primary-color: #ef4e38;\n@primary-gradient: linear-gradient(90deg, #ef4e38 0%, #b61e0e 100%);\n@success-color: #34d399;\n@success-bg: rgba(16, 185, 129, 0.2);\n@dark-bg: linear-gradient(284deg, #152b34 0%, #254b5c 100%);\n@background-dark: #1a2a33;\n@bg-overlay: rgba(255, 255, 255, 0.1);\n@light-overlay: rgba(255, 255, 255, 0.05);\n@border-color: rgba(255, 255, 255, 0.1);\n@text-color: rgba(255, 255, 255, 1);\n@text-secondary: rgba(255, 255, 255, 0.5);\n@border-radius: .75rem;\n@border-radius-small: .5rem;\n@font-family: 'inter', 'noto sans', sans-serif;\n"], "mappings": ";AAEA;AACI,WAAA;AACA,cAAA;AACA,UAAA;;AAHJ,MAKI;AACI,WAAA;AACA,WAAA;AACA,kBAAA;AACA,OAAA;;AATR,MAKI,QAMI,CAAA;AACI,aAAA,SAAA,KAAyB,aAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA,GAAA;AACzB,mBAAA;AACA,WAAA;AACA,eAAA,SAAA,EAAA;;AAfZ,MAKI,QAMI,CAAA,QAMI;AACI,aAAA;AACA,eAAA;AACA,UAAA,EAAA,EAAA,OAAA;;AApBhB,MAKI,QAMI,CAAA,QAYI;AACI,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;;AA1BhB,MAKI,QA0BI,CAAA;AACI,WAAA;AACA,yBAAuB,OAAA,CAAA,EAAA;AACvB,OAAA;AACA,SAAA;;AAnCZ,MAKI,QA0BI,CAAA,IAMI,CAAA;AACI,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,cAAA,WAAA,KAAA,IAAA,EAAA,iBAAA,KAAA;AACA,aAAA,SAAA,KAAwB,aAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA,GAAA;AACxB,YAAA;AACA,YAAA;AACA,WAAA;;AAEA,MAzCZ,QA0BI,CAAA,IAMI,CAAA,MASK;AACG,cAAA,EAAA,OAAA,QAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAIJ,MA/CZ,QA0BI,CAAA,IAMI,CAAA,MAeK,CAAA;AACD,MAhDZ,QA0BI,CAAA,IAMI,CAAA,MAgBK,CAAA;AACD,MAjDZ,QA0BI,CAAA,IAMI,CAAA,MAiBK,CAAA;AACG,eAAA,KAAA;;AAIJ,MAtDZ,QA0BI,CAAA,IAMI,CAAA,MAsBK,CAAA;AACD,MAvDZ,QA0BI,CAAA,IAMI,CAAA,MAuBK,CAAA;AACD,MAxDZ,QA0BI,CAAA,IAMI,CAAA,MAwBK,CAAA;AACG,eAAA,KAAA;;AAGJ,MA5DZ,QA0BI,CAAA,IAMI,CAAA,MA4BK,CAbA;AAcG,WAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,mBAAA;;AALJ,MA5DZ,QA0BI,CAAA,IAMI,CAAA,MA4BK,CAbA,QAoBG,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;;AAVR,MA5DZ,QA0BI,CAAA,IAMI,CAAA,MA4BK,CAbA,QAoBG,CAAA,cAKI,CAAA;AACI,WAAA;AACA,mBAAA;AACA,eAAA;;AAfZ,MA5DZ,QA0BI,CAAA,IAMI,CAAA,MA4BK,CAbA,QAoBG,CAAA,cAKI,CAAA,aAKI;AACI,aAAA;;AAlBhB,MA5DZ,QA0BI,CAAA,IAMI,CAAA,MA4BK,CAbA,QAoBG,CAAA,cAKI,CAAA,aASI,CAAA;AACI,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;;AA1BhB,MA5DZ,QA0BI,CAAA,IAMI,CAAA,MA4BK,CAbA,QAoBG,CAAA,cAKI,CAAA,aASI,CAAA,eAOI;AACI,QAAA;;AA7BpB,MA5DZ,QA0BI,CAAA,IAMI,CAAA,MA4BK,CAbA,QAoBG,CAAA,cA2BI,CAAA;AACI,WAAA;AACA,eAAA;;AApCZ,MA5DZ,QA0BI,CAAA,IAMI,CAAA,MA4BK,CAbA,QAoBG,CAAA,cA2BI,CAAA,aAII,CAAA;AACI,aAAA;AACA,eAAA;AACA,gBAAA;AACA,YAAA;;AAEA,MAxG5B,QA0BI,CAAA,IAMI,CAAA,MA4BK,CAbA,QAoBG,CAAA,cA2BI,CAAA,aAII,CAAA,aAMK,CAAA;AACG,WAAA;AACA,aAAA,OAAA,KAAuB,aAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA,GAAA,SAAA;AACvB,eAAA;;AA/CpB,MA5DZ,QA0BI,CAAA,IAMI,CAAA,MA4BK,CAbA,QAoBG,CAAA,cA2BI,CAAA,aAiBI,CAAA;AACI,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,YAAA;;AAEA,MApH5B,QA0BI,CAAA,IAMI,CAAA,MA4BK,CAbA,QAoBG,CAAA,cA2BI,CAAA,aAiBI,CAAA,eAKK,CAZA;AAaG,WAAA;AACA,aAAA,OAAA,KAAuB,aAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA,GAAA,SAAA;AACvB,eAAA;;AA3DpB,MA5DZ,QA0BI,CAAA,IAMI,CAAA,MA4BK,CAbA,QAoBG,CAAA,cAyDI,CAAA;AACI,UAAA;AACA,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,YAAA;;AApEZ,MA5DZ,QA0BI,CAAA,IAMI,CAAA,MA4BK,CAbA,QAoBG,CAAA,cAyDI,CAAA,aAMI,CAAA;AACI,UAAA;AACA;IAAY;MAAA,KAAA;MAAA,QAAA,EAAA;MAAA,QAAA;AACZ,iBAAA;AACA,SAAA;AACA,aAAA,gBAAA,KAAgC,aAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,SAAA;AAChC,eAAA;;AAMhB,MA9IZ,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA;AA+FD,MA/IZ,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA;AA+FG,WAAA;;AAEA,MAlJhB,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,MAkGI,CAlGJ;AAkGG,MAlJhB,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,QAiGI,CAlGJ;AAmGO,mBAAA;;AAGJ,MAtJhB,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,MAsGI,CArGJ;AAqGG,MAtJhB,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,QAqGI,CArGJ;AAsGO,mBAAA;;AATR,MA9IZ,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,OA0GG,CAAA;AAXJ,MA/IZ,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,SAyGG,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;;AAfR,MA9IZ,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,OA0GG,CAAA,eAKI,CA5FJ;AA4EJ,MA/IZ,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,SAyGG,CAAA,eAKI,CA5FJ;AA6FQ,WAAA;AACA,mBAAA;AACA,eAAA;;AApBZ,MA9IZ,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,OA0GG,CAAA,eAKI,CA5FJ,cAiGQ;AArBZ,MA/IZ,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,SAyGG,CAAA,eAKI,CA5FJ,cAiGQ;AACI,aAAA;;AAvBhB,MA9IZ,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,OA0GG,CAAA,eAKI,CA5FJ,cAqGQ,CAvFA;AA8DZ,MA/IZ,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,SAyGG,CAAA,eAKI,CA5FJ,cAqGQ,CAvFA;AAwFI,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;;AAEA,MA/K5B,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,OA0GG,CAAA,eAKI,CA5FJ,cAqGQ,CAvFA,cA8FK,CAAA,MAAO;AAAR,MA/K5B,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,SAyGG,CAAA,eAKI,CA5FJ,cAqGQ,CAvFA,cA8FK,CAAA,MAAO;AACJ,QAAA;;AAlCpB,MA9IZ,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,OA0GG,CAAA,eA2BI,CAvFA;AAiDR,MA/IZ,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,SAyGG,CAAA,eA2BI,CAvFA;AAwFI,aAAA;AACA,eAAA;AACA,eAAA;AACA,UAAA;;AAEA,MA3LxB,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,OA0GG,CAAA,eA2BI,CAvFA,YA6FK,CAnFI;AAmFL,MA3LxB,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,SAyGG,CAAA,eA2BI,CAvFA,YA6FK,CAnFI;AAoFD,WAAA;AACA,aAAA,QAAA,KAAwB,aAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA,GAAA,SAAA;AACxB,YAAA;AACA,eAAA,SAAA,EAAA;;AAjDhB,MA9IZ,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,OA0GG,CAAA,eAyCI,CAAA;AApDR,MA/IZ,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,SAyGG,CAAA,eAyCI,CAAA;AACI,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;;AAxDZ,MA9IZ,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,OA0GG,CAAA,eA+CI,CAAA;AA1DR,MA/IZ,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,SAyGG,CAAA,eA+CI,CAAA;AACI,WAAA;AACA,eAAA;AACA,OAAA;;AA9DZ,MA9IZ,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,OA0GG,CAAA,eA+CI,CAAA,cAKI,CAAA;AA/DZ,MA/IZ,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,SAyGG,CAAA,eA+CI,CAAA,cAKI,CAAA;AACI,SAAA;AACA,UAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;;AArEhB,MA9IZ,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,OA0GG,CAAA,eA+CI,CAAA,cAKI,CAAA,WAOI;AAtEhB,MA/IZ,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,SAyGG,CAAA,eA+CI,CAAA,cAKI,CAAA,WAOI;AACI,QAAA;;AAxEpB,MA9IZ,QA0BI,CAAA,IAMI,CAAA,MA8GK,CA9FA,OA0GG,CAAA,eA+CI,CAAA,cAiBI,CAAA;AA3EZ,MA/IZ,QA0BI,CAAA,IAMI,CAAA,MA+GK,CA9FA,SAyGG,CAAA,eA+CI,CAAA,cAiBI,CAAA;AACI,aAAA;AACA,SAAA;;AAMhB,MAlOZ,QA0BI,CAAA,IAMI,CAAA,MAkMK,CA5KA;AA6KD,MAnOZ,QA0BI,CAAA,IAMI,CAAA,MAmMK,CA5KA;AA6KG,WAAA;AACA,WAAA;AACA,kBAAA;AACA,OAAA;;AAEA,MAzOhB,QA0BI,CAAA,IAMI,CAAA,MAkMK,CA5KA,cAmLI,CAnLJ;AAmLG,MAzOhB,QA0BI,CAAA,IAMI,CAAA,MAmMK,CA5KA,aAkLI,CAnLJ;AAoLO,mBAAA;;AAGJ,MA7OhB,QA0BI,CAAA,IAMI,CAAA,MAkMK,CA5KA,cAuLI,CAtLJ;AAsLG,MA7OhB,QA0BI,CAAA,IAMI,CAAA,MAmMK,CA5KA,aAsLI,CAtLJ;AAuLO,mBAAA;;AAZR,MAlOZ,QA0BI,CAAA,IAMI,CAAA,MAkMK,CA5KA,eA2LG,CAAA;AAdJ,MAnOZ,QA0BI,CAAA,IAMI,CAAA,MAmMK,CA5KA,cA0LG,CAAA;AACI,WAAA;AACA,mBAAA;AACA,eAAA;;AAlBR,MAlOZ,QA0BI,CAAA,IAMI,CAAA,MAkMK,CA5KA,eA2LG,CAAA,YAKI;AAnBR,MAnOZ,QA0BI,CAAA,IAMI,CAAA,MAmMK,CA5KA,cA0LG,CAAA,YAKI;AACI,aAAA;AACA,eAAA;AACA,UAAA;;AAvBZ,MAlOZ,QA0BI,CAAA,IAMI,CAAA,MAkMK,CA5KA,eA2LG,CAAA,YAWI,CAAA;AAzBR,MAnOZ,QA0BI,CAAA,IAMI,CAAA,MAmMK,CA5KA,cA0LG,CAAA,YAWI,CAAA;AACI,cAAA;AACA,UAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,aAAA;AACA,UAAA;AACA,WAAA;;AAEA,MApQxB,QA0BI,CAAA,IAMI,CAAA,MAkMK,CA5KA,eA2LG,CAAA,YAWI,CAAA,QAQK;AAAD,MApQxB,QA0BI,CAAA,IAMI,CAAA,MAmMK,CA5KA,cA0LG,CAAA,YAWI,CAAA,QAQK;AACG,SAAA;AACA,mBAAA;;AAMhB,MA5QZ,QA0BI,CAAA,IAMI,CAAA,MA4OK,CAtNA,eAuNG,CAAA;AACI,WAAA;AACA,kBAAA;AACA,OAAA;;AAJR,MA5QZ,QA0BI,CAAA,IAMI,CAAA,MA4OK,CAtNA,eAuNG,CAAA,aAKI,CAAA;AACI,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,iBAAA;AACA,WAAA,KAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA,IAAA,KAAA;AACA,UAAA;;AAEA,MA5RxB,QA0BI,CAAA,IAMI,CAAA,MA4OK,CAtNA,eAuNG,CAAA,aAKI,CAAA,WAUK;AACG,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,aAAW,WAAA;;AAlB3B,MA5QZ,QA0BI,CAAA,IAMI,CAAA,MA4OK,CAtNA,eAuNG,CAAA,aAKI,CAAA,YAeI,CAAA;AACI,WAAA;AACA,eAAA;AACA,OAAA;;AAxBhB,MA5QZ,QA0BI,CAAA,IAMI,CAAA,MA4OK,CAtNA,eAuNG,CAAA,aAKI,CAAA,YAeI,CAAA,YAKI,CAAA;AACI,SAAA;AACA,UAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;;AA/BpB,MA5QZ,QA0BI,CAAA,IAMI,CAAA,MA4OK,CAtNA,eAuNG,CAAA,aAKI,CAAA,YAeI,CAAA,YAKI,CAAA,YAOI;AACI,SAAA;AACA,UAAA;;AAnCxB,MA5QZ,QA0BI,CAAA,IAMI,CAAA,MA4OK,CAtNA,eAuNG,CAAA,aAKI,CAAA,YAeI,CAAA,YAkBI,CAAA,eACI;AACI,aAAA;AACA,eAAA;AACA,UAAA,EAAA,EAAA,IAAA;;AA3CxB,MA5QZ,QA0BI,CAAA,IAMI,CAAA,MA4OK,CAtNA,eAuNG,CAAA,aAKI,CAAA,YAeI,CAAA,YAkBI,CAAA,eAOI;AACI,aAAA;AACA,SAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAhDxB,MA5QZ,QA0BI,CAAA,IAMI,CAAA,MA4OK,CAtNA,eAuNG,CAAA,aAKI,CAAA,YA+CI,CAAA;AACI,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;;AAEA,MAtU5B,QA0BI,CAAA,IAMI,CAAA,MA4OK,CAtNA,eAuNG,CAAA,aAKI,CAAA,YA+CI,CAAA,YAKK,CAAA;AACG,oBAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,SAAA;;AAOpB,MA/UZ,QA0BI,CAAA,IAMI,CAAA,MA+SK,CAxRA,cAyRG,CAAA;AACI,WAAA;AACA,aAAA;AACA,OAAA;;AAJR,MA/UZ,QA0BI,CAAA,IAMI,CAAA,MA+SK,CAxRA,cAyRG,CAAA,aAKI,CAAA;AACI,QAAA,EAAA,EAAU,KAAA,IAAA,EAAA;AACV,aAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA;AACA,WAAA,KAAA;AACA,UAAA;AACA,iBAAA;AACA,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA;AACA,SAAA;AACA,aAAA;AACA,UAAA;AACA,cAAA,IAAA,KAAA;AACA,YAAA;AACA,YAAA;;AAEA,MAxWxB,QA0BI,CAAA,IAMI,CAAA,MA+SK,CAxRA,cAyRG,CAAA,aAKI,CAAA,aAmBK;AACG,oBAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,aAAW,MAAA;;AAGf,MA7WxB,QA0BI,CAAA,IAMI,CAAA,MA+SK,CAxRA,cAyRG,CAAA,aAKI,CAAA,aAwBK,CAAA;AACG;ICnXb;MAAA,KAAA;MAAA,QAAA,EAAA;MAAA,QAAA;ADoXa,aAAA,MAAA,GAAA;;AAEA,MAjX5B,QA0BI,CAAA,IAMI,CAAA,MA+SK,CAxRA,cAyRG,CAAA,aAKI,CAAA,aAwBK,CAAA,OAII;AACG,WAAA;AACA,aAAW,MAAA;;AApC/B,MA/UZ,QA0BI,CAAA,IAMI,CAAA,MA+SK,CAxRA,cAyRG,CAAA,aAKI,CAAA,cAkCI;AACI,SAAA;AACA,UAAA;;AAMhB,MA/XZ,QA0BI,CAAA,IAMI,CAAA,MA+VK,CAvUA;AAwUG,WAAA;AACA,mBAAA;;AAEA,MAnYhB,QA0BI,CAAA,IAMI,CAAA,MA+VK,CAvUA,YA2UI;AACG,mBAAA;;AAGJ,MAvYhB,QA0BI,CAAA,IAMI,CAAA,MA+VK,CAvUA,YA+UI;AACG,mBAAA;;AATR,MA/XZ,QA0BI,CAAA,IAMI,CAAA,MA+VK,CAvUA,aAmVG,CA1JA,YA2JI;AACI,aAAA;AACA,eAAA;AACA,UAAA;;", "names": []}