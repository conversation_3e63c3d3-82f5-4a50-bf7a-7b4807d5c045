:host {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: repeat(6, 1fr);
    gap: .5rem;
    width: 5rem;
    height: 100%;
    opacity: 0;
    background: transparent;

    .item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: .5rem;
        padding: .75rem;
        width: 5rem;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid transparent;
        border-radius: .75rem;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.2);
        }

        &.selected {
            background: rgba(16, 185, 129, 0.2);
            border-color: rgba(16, 185, 129, 0.6);
            box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);
        }

        .text {
            font-size: 0.625rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            line-height: 1.2;
        }
    }
}
