<!-- Header Section -->
<header>
    <div class="title">
        <h1>Data Replication</h1>
        <p>What, Where & When</p>
    </div>
    <div class="actions">
        <button class="process" [disabled]="!isButtonEnabled()">
            {{ isButtonEnabled() ? 'Start Replication' : 'Choose options first' }}
        </button>
        <button class="reset" (click)="resetSelections()">
            <fa-icon icon="rotate-left"></fa-icon>
        </button>
    </div>
</header>

<!-- Main Content Container -->
<section>
    <!-- Replication Source & Destination Panel -->
    <app-replication-source class="panel replication-source-panel"
        [selectedSource]="selectedSource()"
        [selectedDestination]="selectedDestination()"
        [expandedPanel]="expandedPanel()"
        [class.expanded]="expandedPanel() === 'source'"
        [class.collapsed]="expandedPanel() !== null && expandedPanel() !== 'source'"
        (sourceSelected)="onSourceSelected($event)"
        (destinationSelected)="onDestinationSelected($event)"
        (onPanelExpand)="onPanelExpand($event)"
    ></app-replication-source>

    <!-- Replication Schedule Panel -->
    <app-replication-schedule class="panel replication-schedule-panel"
        [expandedPanel]="expandedPanel()"
        [class.expanded]="expandedPanel() === 'schedule'"
        [class.collapsed]="expandedPanel() !== null && expandedPanel() !== 'schedule'"
        (onScheduleConfigChange)="onScheduleConfigChanged($event)"
        (onPanelExpand)="onPanelExpand($event)"
    ></app-replication-schedule>
</section>
