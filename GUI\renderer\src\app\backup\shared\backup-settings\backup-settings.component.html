<div class="container">
    <!-- Panel Header -->
    <header>
        <h2>Backup Settings</h2>
        <fa-icon icon="gear"></fa-icon>
    </header>

    <!-- Scrolling Content -->
    <section>
        <!-- Task Name Row -->
        <div class="form-group inline">
            <label class="form-label">Task Name</label>
            <input type="text" class="form-input" name="taskName"
                [(ngModel)]="settings.taskName"
                placeholder="Enter task name">
        </div>

        <!-- Settings Layout (2 columns) -->
        <div class="columns">
            <!-- Left Column - Image Options -->
            <div class="column">
                <!-- Table Head -->
                <h3>Image Options</h3>

                <!-- Compression Section -->
                <div class="compression">
                    <!-- Compression Checkbox -->
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" name="compression" class="form-checkbox" [(ngModel)]="settings.compression">
                            <span class="form-checkbox-label">Compression</span>
                        </label>
                    </div>

                    <div class="options">
                        <!-- Deduplication Compression -->
                        <div class="dedup-compression">
                            <div class="form-group">
                                <label class="form-label">
                                    <input type="radio" class="form-radio" name="compressionType" value="deduplication"
                                        [(ngModel)]="settings.compressionType"
                                        [disabled]="!settings.compression">
                                    <span class="form-radio-label">Deduplication Compression</span>
                                </label>
                            </div>
                            <div class="options">
                                <!-- Compression Level Slider -->
                                <div class="form-group dedup-slider">
                                    <input type="range" name="compressionLevel" class="form-range" min="1" max="3"
                                        [(ngModel)]="settings.compressionLevel"
                                        [disabled]="!settings.compression || settings.compressionType !== 'deduplication'">
                                    <div class="form-range-labels">
                                        <span class="label">Maximum</span>
                                        <span class="label">Optimized</span>
                                        <span class="label">Fast</span>
                                    </div>
                                    <div class="form-range-labels">
                                        <span class="label">Level 1</span>
                                        <span class="label">Level 2</span>
                                        <span class="label">Level 3</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Common Compression -->
                        <div class="common-compression">
                            <div class="form-group">
                                <label class="form-label">
                                    <input type="radio" class="form-radio" name="compressionType" value="common"
                                            [(ngModel)]="settings.compressionType"
                                            [disabled]="!settings.compression">
                                    <span class="form-radio-label">Common Compression</span>
                                </label>
                            </div>

                            <div class="options">
                                <!-- Compression Type Select -->
                                <div class="form-group">
                                    <div class="form-select-wrapper">
                                        <select class="form-select" name="commonCompression" [disabled]="!settings.compression || settings.compressionType !== 'common'">
                                            <option>Fast</option>
                                            <option>Optimized</option>
                                            <option>Maximum</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Password Protection Section -->
                <div class="password-protection">
                    <!-- Password Protection Checkbox -->
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.passwordProtection">
                            <span class="form-checkbox-label">Password Protection</span>
                        </label>
                    </div>

                    <div class="options">
                        <!-- Password Input -->
                        <div class="form-group">
                            <input type="password" name="password" class="form-input" placeholder="******"
                                    [(ngModel)]="settings.password"
                                    [disabled]="!settings.passwordProtection">
                        </div>

                        <!-- Confirm Password Input -->
                        <div class="form-group">
                            <input type="password" name="confirmPassword" class="form-input" placeholder="******"
                                    [(ngModel)]="settings.confirmPassword"
                                    [disabled]="!settings.passwordProtection">
                        </div>

                        <!-- Encryption Select -->
                        <div class="form-group">
                            <div class="form-select-wrapper">
                                <select class="form-select" name="encryption"
                                        [(ngModel)]="settings.encryption"
                                        [disabled]="!settings.passwordProtection">
                                    <option value="No Encryption">No Encryption</option>
                                    <option value="AES-256">AES-256</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Destination Isolation Options -->
                <div class="destination-isolation">
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.destinationIsolation">
                            <span class="form-checkbox-label">Destination Isolation Options (after backup completes)</span>
                        </label>
                    </div>

                    <div class="options">
                        <!-- Isolation Options -->
                        <div class="form-group">
                            <label class="form-label">
                                <input type="radio" class="form-radio" name="isolationType" value="unassign"
                                        [(ngModel)]="settings.isolationType"
                                        [disabled]="!settings.destinationIsolation">
                                <span class="form-radio-label">Un-assign the drive letter from local hard disk</span>
                            </label>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <input type="radio" class="form-radio" name="isolationType" value="offline"
                                        [(ngModel)]="settings.isolationType"
                                        [disabled]="!settings.destinationIsolation">
                                <span class="form-radio-label">Take the destination local hard disk offline</span>
                            </label>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <input type="radio" class="form-radio" name="isolationType" value="eject"
                                        [(ngModel)]="settings.isolationType"
                                        [disabled]="!settings.destinationIsolation">
                                <span class="form-radio-label">Eject the destination USB hard disk</span>
                            </label>
                        </div>

                        <div class="network-connection">
                            <div class="form-group">
                                <label class="form-label">
                                    <input type="radio" class="form-radio" name="isolationType" value="disable"
                                            [(ngModel)]="settings.isolationType"
                                            [disabled]="!settings.destinationIsolation">
                                    <span class="form-radio-label">Disable destination network connection</span>
                                </label>
                            </div>

                            <div class="options">
                                <!-- Network Interface Select -->
                                <div class="form-group">
                                    <div class="form-select-wrapper">
                                        <select class="form-select" name="networkInterface"
                                                [(ngModel)]="settings.networkInterface"
                                                [disabled]="settings.isolationType !== 'disable'  || !settings.destinationIsolation">
                                            <option value="Select network interface">Select network interface</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Image Comment -->
                <div class="image-comment">
                    <div class="form-group">
                        <label class="form-label">Image Comment</label>
                        <textarea class="form-textarea"
                                [(ngModel)]="settings.imageComment"
                                placeholder="Enter image comment"
                                rows="3">
                        </textarea>
                    </div>
                </div>
            </div>

            <!-- Right Column - Task Execution Options -->
            <div class="column">
                <!-- Table Head -->
                <h3>Task Execution Options</h3>

                <!-- Task Effective Dates Section -->
                <div class="effective-dates">
                    <div class="form-group">
                        <label class="form-label">Task effective dates:</label>
                        <div class="form-row">
                            <div class="form-input-group">
                                <input type="text" class="form-input" [(ngModel)]="settings.taskEffectiveFrom">
                                <fa-icon icon="calendar" class="form-input-icon"></fa-icon>
                            </div>
                            <label class="form-label">
                                <input class="form-checkbox" type="checkbox" [(ngModel)]="settings.useEffectiveTo">
                                <span class="form-checkbox-label">To:</span>
                            </label>
                            <div class="form-input-group">
                                <input type="text" class="form-input"
                                        [(ngModel)]="settings.taskEffectiveTo"
                                        [disabled]="!settings.useEffectiveTo">
                                <fa-icon icon="calendar" class="form-input-icon"></fa-icon>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Task Execution Settings Section -->
                <div class="execution-settings">
                    <!-- Run on Shutdown -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">
                                <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.runOnShutdown">
                                <span class="form-checkbox-label">Run task on shutdown / reboot</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <div class="form-select-wrapper">
                                <select class="form-select" name="shutdownType"
                                        [(ngModel)]="settings.shutdownType"
                                        [disabled]="!settings.runOnShutdown">
                                    <option value="Base and incremental">Base and incremental</option>
                                    <option value="Base only">Base only</option>
                                    <option value="Incremental only">Incremental only</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Auto Run Missed -->
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.autoRunMissed">
                            <span class="form-checkbox-label">Auto run missed schedule tasks</span>
                        </label>
                    </div>
                    <div class="options">
                        <div class="form-group">
                            <label class="form-label">
                                <input type="checkbox" class="form-checkbox"
                                        [(ngModel)]="settings.runMissedBase"
                                        [disabled]="!settings.autoRunMissed">
                                <span class="form-checkbox-label">Run missed schedule base backup</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Retention Policy Section -->
                <div class="retention-policy">
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.enableRetention">
                            <span class="form-checkbox-label">Enable retention policy</span>
                        </label>
                    </div>
                    <div class="options">
                        <div class="form-row">
                            <div class="form-group">
                                <div class="form-select-wrapper">
                                    <select class="form-select" name="retentionType"
                                            [(ngModel)]="settings.retentionType"
                                            [disabled]="!settings.enableRetention">
                                        <option value="Delete base and incremental">Delete base and incremental</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group inline">
                                <label class="form-label">Retain</label>
                                <input type="number" class="form-input"
                                        [(ngModel)]="settings.retentionCount"
                                        [disabled]="!settings.enableRetention">
                                <label class="form-label">image sets</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                <input type="checkbox" class="form-checkbox"
                                        [(ngModel)]="settings.deleteOlderImages"
                                        [disabled]="!settings.enableRetention">
                                <span class="form-checkbox-label">Delete older image before new base backup</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Reconcile Image Section -->
                <div class="reconcile-image">
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.enableReconcile">
                            <span class="form-checkbox-label">Enable reconcile image</span>
                        </label>
                    </div>

                    <div class="options">
                        <div class="form-group inline">
                            <label class="form-label">
                                <input type="checkbox" class="form-checkbox"
                                    [(ngModel)]="settings.preserveReconciled"
                                    [disabled]="!settings.enableReconcile">
                                <span class="form-checkbox-label">Preserve reconciled image for</span>
                            </label>
                            <input type="number" class="form-input always-enable-label"
                                [(ngModel)]="settings.preserveDays"
                                [disabled]="!settings.enableReconcile || !settings.preserveReconciled">
                            <label class="form-label">days</label>
                        </div>

                    </div>
                </div>

                <!-- Email Notification Section -->
                <div class="notification">
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.emailNotification">
                            <span class="form-checkbox-label">Send email notification</span>
                        </label>
                    </div>

                    <div class="options">
                        <div class="form-group">
                            <div class="form-select-wrapper">
                                <select class="form-select" name="emailCondition"
                                        [(ngModel)]="settings.emailCondition"
                                        [disabled]="!settings.emailNotification">
                                    <option value="When task failed">When task failed</option>
                                    <option value="When task completed">When task completed</option>
                                    <option value="Always">Always</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Execution Priority Section -->
                <div class="execution-priority">
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" class="form-checkbox" [(ngModel)]="settings.executionPriority">
                            <span class="form-checkbox-label">Execution Priority</span>
                        </label>
                    </div>

                    <div class="options">
                        <!-- Full (Base) Priority -->
                        <div class="form-group">
                            <label class="form-label">Full (Base)</label>
                            <input type="range" class="form-range" min="0" max="3"
                                [disabled]="!settings.executionPriority"
                                [(ngModel)]="settings.fullPriority">
                            <div class="form-range-labels">
                                <span class="label">Lowest</span>
                                <span class="label">Low</span>
                                <span class="label">Medium</span>
                                <span class="label">High</span>
                            </div>
                        </div>

                        <!-- Incremental Priority -->
                        <div class="form-group">
                            <label class="form-label">Incremental</label>
                            <input type="range" class="form-range" min="0" max="3"
                                [disabled]="!settings.executionPriority"
                                [(ngModel)]="settings.incrementalPriority">
                            <div class="form-range-labels">
                                <span class="label">Lowest</span>
                                <span class="label">Low</span>
                                <span class="label">Medium</span>
                                <span class="label">High</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Save Button -->
    <footer>
        <button class="form-button" (click)="saveSettings()">
            Save
        </button>
    </footer>
</div>
