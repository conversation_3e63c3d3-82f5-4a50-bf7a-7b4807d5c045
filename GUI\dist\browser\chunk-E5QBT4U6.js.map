{"version": 3, "sources": ["renderer/src/app/preferences/console/console.component.ts", "renderer/src/app/preferences/console/console.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\n\r\n@Component({\r\n    selector: 'app-console',\r\n    imports: [FontAwesomeModule],\r\n    templateUrl: './console.component.html',\r\n    styleUrl: './console.component.less'\r\n})\r\nexport class ConsoleComponent {\r\n\r\n}\r\n", "<header>\r\n    <div class=\"icon\">\r\n        <fa-icon icon=\"terminal\"></fa-icon>\r\n    </div>\r\n    <h2>Console Settings</h2>\r\n</header>\r\n\r\n<section>\r\n    <p>Console settings will be implemented here.</p>\r\n</section>\r\n\r\n<footer>\r\n    <span class=\"notice\">0 Configuration changes waiting to be applied</span>\r\n    <div class=\"buttons\">\r\n        <button type=\"button\" class=\"button secondary\">Cancel</button>\r\n        <button type=\"button\" class=\"button primary\">Apply</button>\r\n    </div>\r\n</footer>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AASM,IAAO,mBAAP,MAAO,kBAAgB;;qCAAhB,mBAAgB;EAAA;yEAAhB,mBAAgB,WAAA,CAAA,CAAA,aAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,MAAA,GAAA,CAAA,QAAA,UAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,WAAA,GAAA,CAAA,QAAA,UAAA,GAAA,UAAA,SAAA,CAAA,GAAA,UAAA,SAAA,0BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACT7B,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,OAAA,CAAA;AAEA,MAAA,oBAAA,GAAA,WAAA,CAAA;AACJ,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,kBAAA;AAAgB,MAAA,uBAAA,EAAK;AAG7B,MAAA,yBAAA,GAAA,SAAA,EAAS,GAAA,GAAA;AACF,MAAA,iBAAA,GAAA,4CAAA;AAA0C,MAAA,uBAAA,EAAI;AAGrD,MAAA,yBAAA,GAAA,QAAA,EAAQ,GAAA,QAAA,CAAA;AACiB,MAAA,iBAAA,IAAA,+CAAA;AAA6C,MAAA,uBAAA;AAClE,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,UAAA,CAAA;AAC8B,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AACrD,MAAA,yBAAA,IAAA,UAAA,CAAA;AAA6C,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAS,EACzD;;oBDXI,mBAAiB,eAAA,GAAA,QAAA,CAAA,q9CAAA,EAAA,CAAA;;;sEAIlB,kBAAgB,CAAA;UAN5B;uBACa,eAAa,SACd,CAAC,iBAAiB,GAAC,UAAA,uhBAAA,QAAA,CAAA,ksCAAA,EAAA,CAAA;;;;6EAInB,kBAAgB,EAAA,WAAA,oBAAA,UAAA,6DAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}