import { DialogModule, DialogRef } from '@angular/cdk/dialog';
import { Component, QueryList, ViewChildren, ElementRef } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-recovery-pin-dialog',
    templateUrl: './recovery-pin-dialog.component.html',
    styleUrls: ['./recovery-pin-dialog.component.less'],
    imports: [CommonModule, DialogModule, FormsModule],
})
export class RecoveryPinDialogComponent {
    pin: string[] = ['', '', '', ''];

    @ViewChildren('pinInput') pinInputs!: QueryList<ElementRef<HTMLInputElement>>;

    constructor(public dialogRef: DialogRef<string>) { }

    get isPinComplete() {
        return this.pin.every(p => p.length === 6 && p.match(/^[0-9]+$/));
    }

    onKeyDown(event: KeyboardEvent, index: number) {
        const inputEl = this.pinInputs.get(index)?.nativeElement;
        if (!inputEl) return;

        if (event.key === 'Backspace' && index > 0 && inputEl.value === '') {
            this.pinInputs.get(index - 1)?.nativeElement.focus();
        }
    }

    onInput(event: Event, index: number) {
        const input = event.target as HTMLInputElement;
        const value = input.value;

        if (value.length === 6 && index < this.pin.length - 1) {
            this.pinInputs.get(index + 1)?.nativeElement.focus();
        }
    }
}
