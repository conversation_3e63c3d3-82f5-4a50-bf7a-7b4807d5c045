/**
 * Common styles for the GUI renderer.
 * <AUTHOR>
 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', 'Noto Sans', sans-serif;
    background: #1A2A33;
    color: #FFFFFF;
    height: 100vh;
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
}

::-webkit-scrollbar {
    width: .5rem;
    height: .5rem;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: .25rem;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
}
