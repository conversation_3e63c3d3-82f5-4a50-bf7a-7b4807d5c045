/**
 * Application
 * <AUTHOR>
 */

import { app, Menu, BrowserWindow, shell } from 'electron';
import { defaults } from './defaults';

export class App {
    // Keep a global reference of the window object, if you don't, the window will
    // be closed automatically when the JavaScript object is garbage collected.
    private window?: BrowserWindow;

    constructor(private platform: NodeJS.Platform) { }

    run(): void {
        // Single instance lock
        if (!app.requestSingleInstanceLock()) {
            app.quit();
        }

        app.on('second-instance', () => this.openMainWindow());

        // This method will be called when Electron has finished
        // initialization and is ready to create browser windows.
        // Some APIs can only be used after this event occurs.
        app.on('ready', () => {
            if (this.platform === 'win32') {
                app.setAppUserModelId(defaults.appId);
                // hidden menu bar
                Menu.setApplicationMenu(null);
            }

            // Create main window
            this.createMainWindow();
        });

        app.on('activate', () => {
            // On macOS it's common to re-create a window in the app when the
            // dock icon is clicked and there are no other windows open.
            if (!this.window) {
                this.createMainWindow();
            }
        });

        // Quit when all windows are closed.
        app.on('window-all-closed', () => {
            // On macOS it is common for applications and their menu bar
            // to stay active until the user quits explicitly with Cmd + Q
            if (this.platform !== 'darwin') {
                app.quit();
            }
        });

        app.on('will-quit', () => {
            if (this.platform === 'darwin') {
                app.dock?.hide();
            }
        });
    }

    /**
     * Create Main window
     */
    private createMainWindow(): void {
        // Create the browser window.
        this.window = new BrowserWindow({
            show: false,
            frame: false,
            resizable: true,
            width: defaults.mainWindowWidth,
            height: defaults.mainWindowHeight,
            title: defaults.title,
            backgroundColor: defaults.bgColor,
            titleBarStyle: 'hidden',
            titleBarOverlay: {
                color: "#142a33",
                symbolColor: "#ffffff",
                height: 39,
            },
            webPreferences: {
                devTools: !process.env['prod'],
                spellcheck: false,
                enableWebSQL: false,
            },
        });

        this.window.once('ready-to-show', () => this.window?.show());

        // Emitted when the window is closed.
        this.window.once('closed', () => {
            // Dereference the window object, usually you would store windows
            // in an array if your app supports multi windows, this is the time
            // when you should delete the corresponding element.
            this.window = undefined;
        });

        // And load the index.html of the app.
        this.window.loadFile('browser/index.html');

        this.window.webContents.setWindowOpenHandler(details => {
            shell.openExternal(details.url);
            return { action: 'deny' };
        });

        if (!process.env['prod']) {
            this.window.webContents.openDevTools({ mode: 'undocked' });
        }
    }

    private openMainWindow(): void {
        if (this.platform === 'darwin') {
            app.dock?.show();
        }

        if (this.window) {
            if (this.window.isMinimized()) {
                this.window.restore();
            }

            this.window.focus();
            this.window.show();
        } else {
            this.createMainWindow();
        }
    }
}
